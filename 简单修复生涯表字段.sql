-- 简单修复 sd_player_career_stats 表缺失字段
-- 根据错误信息，逐步添加缺失的字段

-- 1. 添加 avg_efficiency 字段（当前报错的字段）
ALTER TABLE sd_player_career_stats 
ADD COLUMN avg_efficiency DECIMAL(5,2) DEFAULT 0 COMMENT '场均效率值';

-- 2. 添加其他缺失的基础字段
ALTER TABLE sd_player_career_stats 
ADD COLUMN total_two_points_made INT DEFAULT 0 COMMENT '总二分命中数',
ADD COLUMN total_two_points_attempted INT DEFAULT 0 COMMENT '总二分出手数',
ADD COLUMN total_minutes_played DECIMAL(8,2) DEFAULT 0 COMMENT '总出场时间(分钟)',
ADD COLUMN avg_minutes_played DECIMAL(5,2) DEFAULT 0 COMMENT '场均出场时间';

-- 3. 添加命中率字段
ALTER TABLE sd_player_career_stats
ADD COLUMN two_point_percentage DECIMAL(5,2) DEFAULT 0 COMMENT '二分命中率';

-- 4. 添加高阶统计字段
ALTER TABLE sd_player_career_stats
ADD COLUMN effective_field_goal_percentage DECIMAL(5,2) DEFAULT 0 COMMENT '有效投篮命中率',
ADD COLUMN offensive_rebound_rate DECIMAL(5,2) DEFAULT 0 COMMENT '进攻篮板率',
ADD COLUMN defensive_rebound_rate DECIMAL(5,2) DEFAULT 0 COMMENT '防守篮板率',
ADD COLUMN assist_turnover_ratio DECIMAL(5,2) DEFAULT 0 COMMENT '助攻失误比',
ADD COLUMN player_efficiency_rating DECIMAL(5,2) DEFAULT 0 COMMENT '球员效率指数(PER)',
ADD COLUMN usage_rate DECIMAL(5,2) DEFAULT 0 COMMENT '使用率',
ADD COLUMN plus_minus DECIMAL(5,2) DEFAULT 0 COMMENT '净胜分';

-- 5. 检查修复结果
DESCRIBE sd_player_career_stats;