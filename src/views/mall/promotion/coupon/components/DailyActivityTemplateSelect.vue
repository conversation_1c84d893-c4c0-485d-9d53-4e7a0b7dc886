<template>
  <el-select
    v-model="selectTemplateId"
    :data="templateList"
    :props="defaultProps"
    :multiple="multiple"
    :show-checkbox="multiple"
    class="w-1/1"
    node-key="id"
    placeholder="请选择模板"
  >
    <el-option
      v-for="item in templateList"
      :key="item.id"
      :label="item.title"
      :value="item.id"
    />

  </el-select>
</template>
<script lang="ts" setup>
import {defaultProps} from '@/utils/tree'
import {oneOfType} from 'vue-types'
import {propTypes} from '@/utils/propTypes'
import {
  DailyActivityTemplateApi,
  DailyActivityTemplateVO
} from "@/api/operation/dailyActivityTemplate";

/** 商品分类选择组件 */
defineOptions({name: 'DailyActivityTemplateSelect'})

const props = defineProps({
  // 选中的ID
  modelValue: oneOfType<number | number[]>([Number, Array<Number>]),
  // 是否多选
  multiple: propTypes.bool.def(true),
  // 上级品类的编号
  parentId: propTypes.number.def(undefined)
})

/** 选中的分类 ID */
const selectTemplateId = computed({
  get: () => {
    return props.modelValue
  },
  set: (val: number | number[]) => {
    emit('update:modelValue', val)
  }
})

/** 分类选择 */
const emit = defineEmits(['update:modelValue'])

/** 初始化 **/
const templateList = ref<DailyActivityTemplateVO[]>([]) // 分类树
onMounted(async () => {
  // 获得分类树
  const data = await DailyActivityTemplateApi.getDailyActivityTemplateList()
  templateList.value = data
})
</script>
