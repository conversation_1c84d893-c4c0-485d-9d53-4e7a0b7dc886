package cn.iocoder.yudao.module.operation.service.player;

import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.operation.controller.app.player.vo.AppPlayerCareerOverviewRespVO;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.SeasonDO;
import cn.iocoder.yudao.module.operation.dal.mysql.player.SeasonMapper;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link SeasonService} 的单元测试类
 */
@Import(SeasonServiceImpl.class)
public class SeasonServiceTest extends BaseDbUnitTest {

    @Resource
    private SeasonService seasonService;

    @MockBean
    private SeasonMapper seasonMapper;

    @Test
    public void testGetAvailableSeasonOptions() {
        // 测试获取可用赛季列表
        List<AppPlayerCareerOverviewRespVO.SeasonOption> seasons = seasonService.getAvailableSeasonOptions();
        
        assertNotNull(seasons);
        assertFalse(seasons.isEmpty());
        
        // 验证赛季选项的基本属性
        for (AppPlayerCareerOverviewRespVO.SeasonOption season : seasons) {
            assertNotNull(season.getName());
            assertNotNull(season.getValue());
            assertNotNull(season.getStatus());
        }
        
        System.out.println("获取到的赛季列表:");
        for (AppPlayerCareerOverviewRespVO.SeasonOption season : seasons) {
            System.out.printf("- %s (%s) - %s%n", 
                season.getName(), season.getValue(), season.getStatus());
        }
    }

    @Test
    public void testGetCurrentSeasonString() {
        // 测试获取当前赛季字符串
        String currentSeason = seasonService.getCurrentSeasonString();
        
        assertNotNull(currentSeason);
        assertFalse(currentSeason.trim().isEmpty());
        
        System.out.println("当前赛季: " + currentSeason);
    }

    @Test
    public void testIsCurrentSeasonString() {
        // 测试判断是否为当前赛季
        String currentSeason = seasonService.getCurrentSeasonString();
        
        assertTrue(seasonService.isCurrentSeasonString(currentSeason));
        assertFalse(seasonService.isCurrentSeasonString("2020"));
        assertFalse(seasonService.isCurrentSeasonString(null));
    }

    @Test
    public void testGetSeasonStatusString() {
        // 测试获取赛季状态
        int currentYear = LocalDate.now().getYear();
        
        String currentStatus = seasonService.getSeasonStatusString(String.valueOf(currentYear));
        String pastStatus = seasonService.getSeasonStatusString("2020");
        String nullStatus = seasonService.getSeasonStatusString(null);
        
        assertEquals("进行中", currentStatus);
        assertEquals("已结束", pastStatus);
        assertEquals("未知", nullStatus);
        
        System.out.println("赛季状态测试:");
        System.out.printf("- %d年: %s%n", currentYear, currentStatus);
        System.out.printf("- 2020年: %s%n", pastStatus);
        System.out.printf("- null: %s%n", nullStatus);
    }

    @Test
    public void testIsValidSeasonString() {
        // 测试验证赛季有效性
        assertFalse(seasonService.isValidSeasonString(null));
        assertFalse(seasonService.isValidSeasonString(""));
        assertFalse(seasonService.isValidSeasonString("   "));
        
        // 注意：由于没有实际的数据库数据，这里会返回false
        // 在实际环境中，如果数据库中有对应的赛季数据，则会返回true
        boolean isValid = seasonService.isValidSeasonString("2024");
        System.out.println("2024赛季是否有效: " + isValid);
    }
}
