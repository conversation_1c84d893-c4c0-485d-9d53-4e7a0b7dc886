package cn.iocoder.yudao.module.operation.service.career;

import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerStatisticsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerCareerStatsDO;
import cn.iocoder.yudao.module.operation.dal.mysql.game.PlayerStatisticsMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.game.PlayerGameRelatedMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.player.PlayerCareerStatsMapper;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * {@link PlayerCareerStatsService} 的单元测试类
 */
@Import({PlayerCareerStatsService.class, PlayerCareerStatsCalculator.class, PlayerStatsCalculatorCommon.class})
public class PlayerCareerStatsServiceTest extends BaseDbUnitTest {

    @Resource
    private PlayerCareerStatsService playerCareerStatsService;

    @MockBean
    private PlayerStatisticsMapper playerStatisticsMapper;

    @MockBean
    private PlayerGameRelatedMapper playerGameRelatedMapper;

    @MockBean
    private PlayerCareerStatsMapper playerCareerStatsMapper;

    @Test
    public void testGamesPlayedCalculation() {
        // 模拟球员在3场比赛中的统计数据
        // 每场比赛有2条记录（比如上下半场），总共6条记录
        List<PlayerStatisticsDO> mockStats = createMockPlayerStats();
        
        when(playerStatisticsMapper.selectList(any())).thenReturn(mockStats);
        when(playerGameRelatedMapper.selectList(any())).thenReturn(new ArrayList<>());
        
        // 执行生涯统计计算
        PlayerCareerStatsDO result = playerCareerStatsService.calculateCareerStats(1L, 0);
        
        // 验证结果
        assertNotNull(result);
        
        // 应该是3场比赛，而不是6条记录
        assertEquals(3, result.getGamesPlayed(), "比赛场次应该是3场，而不是统计记录数量");
        
        // 验证总得分是所有记录的累加
        assertEquals(180, result.getTotalPoints(), "总得分应该是所有记录的累加");
        
        System.out.println("测试结果:");
        System.out.println("- 比赛场次: " + result.getGamesPlayed());
        System.out.println("- 有效比赛: " + result.getValidStatsGames());
        System.out.println("- 总得分: " + result.getTotalPoints());
        System.out.println("- 场均得分: " + result.getAvgPoints());
    }

    /**
     * 创建模拟的球员统计数据
     * 3场比赛，每场比赛2条记录，总共6条记录
     */
    private List<PlayerStatisticsDO> createMockPlayerStats() {
        List<PlayerStatisticsDO> stats = new ArrayList<>();
        
        // 第1场比赛 - 2条记录
        stats.add(createPlayerStat(1L, 1L, 15)); // 比赛1，记录1，15分
        stats.add(createPlayerStat(2L, 1L, 10)); // 比赛1，记录2，10分
        
        // 第2场比赛 - 2条记录  
        stats.add(createPlayerStat(3L, 2L, 20)); // 比赛2，记录1，20分
        stats.add(createPlayerStat(4L, 2L, 25)); // 比赛2，记录2，25分
        
        // 第3场比赛 - 2条记录
        stats.add(createPlayerStat(5L, 3L, 30)); // 比赛3，记录1，30分
        stats.add(createPlayerStat(6L, 3L, 80)); // 比赛3，记录2，80分
        
        return stats;
    }

    /**
     * 创建单条球员统计记录
     */
    private PlayerStatisticsDO createPlayerStat(Long id, Long gameId, Integer points) {
        PlayerStatisticsDO stat = new PlayerStatisticsDO();
        stat.setId(id);
        stat.setGameId(gameId);
        stat.setPlayerId(1L);
        stat.setSection(4); // 全场统计
        stat.setPoints(points);
        stat.setRebounds(5);
        stat.setAssists(3);
        stat.setSteals(1);
        stat.setBlocks(1);
        stat.setTurnovers(2);
        stat.setFouls(2);
        stat.setTwoPointMakes(3);
        stat.setTwoPointAttempts(6);
        stat.setThreePointMakes(1);
        stat.setThreePointAttempts(3);
        stat.setFreeThrowMakes(2);
        stat.setFreeThrowAttempts(3);
        stat.setDeleted(false);
        return stat;
    }
}
