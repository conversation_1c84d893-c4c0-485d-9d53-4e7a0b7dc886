package cn.iocoder.yudao.module.operation.service.career;

import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerStatisticsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.GameDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerGameRelatedDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerCareerStatsDO;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 球员生涯数据一致性测试
 * 使用TDD方式确保数据的一致性
 */
@Import(PlayerCareerDataInitializerV2.class)
public class PlayerCareerDataConsistencyTest extends BaseDbUnitTest {

    /**
     * 测试核心原则：比赛场次 = 胜场 + 负场
     * 这是最基本的数据一致性要求
     */
    @Test
    public void testGamesPlayedEqualsWinsAndLosses() {
        // Given: 创建测试数据
        TestDataSet testData = createConsistentTestData();
        
        // When: 计算生涯统计
        PlayerCareerStatsDO result = calculateCareerStatsFromTestData(testData);
        
        // Then: 验证数据一致性
        assertDataConsistency(result, testData);
    }

    /**
     * 测试边界情况：球员参与比赛但没有统计数据
     */
    @Test
    public void testPlayerWithGameRelationButNoStats() {
        // Given: 球员参与了5场比赛，但只有3场有统计数据
        TestDataSet testData = createTestDataWithMissingStats();
        
        // When: 计算生涯统计
        PlayerCareerStatsDO result = calculateCareerStatsFromTestData(testData);
        
        // Then: 应该只统计有数据的比赛
        assertEquals(3, result.getGamesPlayed(), "应该只统计有统计数据的比赛");
        assertEquals(3, result.getTotalWins() + result.getTotalLosses(), "胜负总数应该等于比赛场次");
    }

    /**
     * 测试边界情况：球员有统计数据但没有比赛关系
     */
    @Test
    public void testPlayerWithStatsButNoGameRelation() {
        // Given: 球员有3场比赛的统计数据，但只有2场有比赛关系
        TestDataSet testData = createTestDataWithMissingRelations();
        
        // When: 计算生涯统计
        PlayerCareerStatsDO result = calculateCareerStatsFromTestData(testData);
        
        // Then: 应该只统计有关系数据的比赛
        assertEquals(2, result.getGamesPlayed(), "应该只统计有比赛关系的比赛");
        assertEquals(2, result.getTotalWins() + result.getTotalLosses(), "胜负总数应该等于比赛场次");
    }

    /**
     * 测试最佳数据不为null
     */
    @Test
    public void testBestStatsNotNull() {
        // Given: 创建有明确最佳数据的测试数据
        TestDataSet testData = createTestDataWithBestStats();
        
        // When: 计算生涯统计
        PlayerCareerStatsDO result = calculateCareerStatsFromTestData(testData);
        
        // Then: 最佳数据不应该为null
        assertNotNull(result.getBestPoints(), "最佳得分不应该为null");
        assertNotNull(result.getBestRebounds(), "最佳篮板不应该为null");
        assertNotNull(result.getBestAssists(), "最佳助攻不应该为null");
        assertTrue(result.getBestPoints() > 0, "最佳得分应该大于0");
    }

    // ========== 辅助方法 ==========

    private TestDataSet createConsistentTestData() {
        TestDataSet data = new TestDataSet();
        
        // 创建3场比赛的完整数据
        for (int i = 1; i <= 3; i++) {
            Long gameId = (long) i;
            
            // 比赛数据
            GameDO game = createGame(gameId, 1L, 2L, 100 + i, 90 + i, 1);
            data.games.put(gameId, game);
            
            // 统计数据（每场比赛2条记录，模拟上下半场）
            data.playerStats.add(createPlayerStat(gameId * 2 - 1, gameId, 1L, 10 + i));
            data.playerStats.add(createPlayerStat(gameId * 2, gameId, 1L, 15 + i));
            
            // 比赛关系数据
            data.gameRelations.add(createGameRelation(gameId, 1L, 1L));
        }
        
        return data;
    }

    private TestDataSet createTestDataWithMissingStats() {
        TestDataSet data = new TestDataSet();
        
        // 5场比赛，但只有3场有统计数据
        for (int i = 1; i <= 5; i++) {
            Long gameId = (long) i;
            
            // 所有比赛都有比赛数据和关系数据
            GameDO game = createGame(gameId, 1L, 2L, 100 + i, 90 + i, 1);
            data.games.put(gameId, game);
            data.gameRelations.add(createGameRelation(gameId, 1L, 1L));
            
            // 只有前3场有统计数据
            if (i <= 3) {
                data.playerStats.add(createPlayerStat(gameId, gameId, 1L, 10 + i));
            }
        }
        
        return data;
    }

    private TestDataSet createTestDataWithMissingRelations() {
        TestDataSet data = new TestDataSet();
        
        // 3场比赛的统计数据，但只有2场有比赛关系
        for (int i = 1; i <= 3; i++) {
            Long gameId = (long) i;
            
            // 所有比赛都有比赛数据和统计数据
            GameDO game = createGame(gameId, 1L, 2L, 100 + i, 90 + i, 1);
            data.games.put(gameId, game);
            data.playerStats.add(createPlayerStat(gameId, gameId, 1L, 10 + i));
            
            // 只有前2场有比赛关系
            if (i <= 2) {
                data.gameRelations.add(createGameRelation(gameId, 1L, 1L));
            }
        }
        
        return data;
    }

    private TestDataSet createTestDataWithBestStats() {
        TestDataSet data = new TestDataSet();
        
        // 创建3场比赛，有明确的最佳数据
        int[] points = {25, 30, 35}; // 最佳得分35
        int[] rebounds = {8, 12, 10}; // 最佳篮板12
        int[] assists = {5, 7, 9}; // 最佳助攻9
        
        for (int i = 0; i < 3; i++) {
            Long gameId = (long) (i + 1);
            
            GameDO game = createGame(gameId, 1L, 2L, 100, 90, 1);
            data.games.put(gameId, game);
            
            PlayerStatisticsDO stat = createPlayerStat(gameId, gameId, 1L, points[i]);
            stat.setOffensiveRebounds(rebounds[i] / 2);
            stat.setDefensiveRebounds(rebounds[i] - rebounds[i] / 2);
            stat.setAssists(assists[i]);
            data.playerStats.add(stat);
            
            data.gameRelations.add(createGameRelation(gameId, 1L, 1L));
        }
        
        return data;
    }

    private PlayerCareerStatsDO calculateCareerStatsFromTestData(TestDataSet testData) {
        // 这里应该调用实际的计算方法
        // 为了测试，我们先创建一个模拟的计算逻辑
        return mockCalculateCareerStats(testData);
    }

    private PlayerCareerStatsDO mockCalculateCareerStats(TestDataSet testData) {
        PlayerCareerStatsDO result = new PlayerCareerStatsDO();
        
        // 计算实际比赛场次（统计数据和比赛关系的交集）
        Set<Long> statsGameIds = testData.playerStats.stream()
                .map(PlayerStatisticsDO::getGameId)
                .collect(Collectors.toSet());
        
        Set<Long> relationGameIds = testData.gameRelations.stream()
                .map(PlayerGameRelatedDO::getGameId)
                .collect(Collectors.toSet());
        
        Set<Long> validGameIds = new HashSet<>(statsGameIds);
        validGameIds.retainAll(relationGameIds);
        
        result.setGamesPlayed(validGameIds.size());
        
        // 计算胜负（只统计有效比赛）
        int wins = 0, losses = 0;
        for (Long gameId : validGameIds) {
            GameDO game = testData.games.get(gameId);
            if (game != null && game.getHomeTeamPoints() > game.getGuestTeamPoints()) {
                wins++; // 假设球员都在主队
            } else {
                losses++;
            }
        }
        
        result.setTotalWins(wins);
        result.setTotalLosses(losses);
        
        // 计算最佳数据
        if (!testData.playerStats.isEmpty()) {
            result.setBestPoints(testData.playerStats.stream()
                    .mapToInt(s -> s.getPoints() != null ? s.getPoints() : 0)
                    .max().orElse(0));
            
            result.setBestRebounds(testData.playerStats.stream()
                    .mapToInt(s -> (s.getOffensiveRebounds() != null ? s.getOffensiveRebounds() : 0) +
                            (s.getDefensiveRebounds() != null ? s.getDefensiveRebounds() : 0))
                    .max().orElse(0));
            
            result.setBestAssists(testData.playerStats.stream()
                    .mapToInt(s -> s.getAssists() != null ? s.getAssists() : 0)
                    .max().orElse(0));
        }
        
        return result;
    }

    private void assertDataConsistency(PlayerCareerStatsDO result, TestDataSet testData) {
        // 核心一致性检查
        assertEquals(result.getGamesPlayed(), result.getTotalWins() + result.getTotalLosses(),
                "比赛场次必须等于胜场加负场");
        
        // 数据合理性检查
        assertTrue(result.getGamesPlayed() > 0, "比赛场次应该大于0");
        assertTrue(result.getTotalWins() >= 0, "胜场不能为负数");
        assertTrue(result.getTotalLosses() >= 0, "负场不能为负数");
    }

    // 辅助数据结构和创建方法
    private static class TestDataSet {
        List<PlayerStatisticsDO> playerStats = new ArrayList<>();
        List<PlayerGameRelatedDO> gameRelations = new ArrayList<>();
        Map<Long, GameDO> games = new HashMap<>();
    }

    private PlayerStatisticsDO createPlayerStat(Long id, Long gameId, Long playerId, Integer points) {
        PlayerStatisticsDO stat = new PlayerStatisticsDO();
        stat.setId(id);
        stat.setGameId(gameId);
        stat.setPlayerId(playerId);
        stat.setSection(4);
        stat.setPoints(points);
        stat.setOffensiveRebounds(3);
        stat.setDefensiveRebounds(5);
        stat.setAssists(4);
        stat.setDeleted(false);
        return stat;
    }

    private PlayerGameRelatedDO createGameRelation(Long gameId, Long playerId, Long teamId) {
        PlayerGameRelatedDO relation = new PlayerGameRelatedDO();
        relation.setGameId(gameId);
        relation.setPlayerId(playerId);
        relation.setTeamId(teamId);
        relation.setAttend(2);
        return relation;
    }

    private GameDO createGame(Long id, Long homeTeamId, Long guestTeamId, Integer homePoints, Integer guestPoints, Integer type) {
        GameDO game = new GameDO();
        game.setId(id);
        game.setHomeTeamId(homeTeamId);
        game.setGuestTeamId(guestTeamId);
        game.setHomeTeamPoints(homePoints);
        game.setGuestTeamPoints(guestPoints);
        game.setStatus(4);
        game.setType(type);
        game.setStartTime(LocalDateTime.now());
        return game;
    }
}
