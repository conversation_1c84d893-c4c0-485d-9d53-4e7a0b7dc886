-- 智能修复 sd_player_career_stats 表缺失字段
-- 自动检测并添加所有缺失的字段

-- 使用存储过程来智能添加字段
DELIMITER $$

CREATE PROCEDURE AddColumnIfNotExists(
    IN tableName VARCHAR(100),
    IN columnName VARCHAR(100), 
    IN columnDefinition TEXT
)
BEGIN
    DECLARE column_count INT DEFAULT 0;
    
    SELECT COUNT(*) INTO column_count 
    FROM information_schema.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = tableName 
    AND COLUMN_NAME = columnName;
    
    IF column_count = 0 THEN
        SET @sql = CONCAT('ALTER TABLE ', tableName, ' ADD COLUMN ', columnName, ' ', columnDefinition);
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        SELECT CONCAT('Added column: ', columnName) AS result;
    ELSE
        SELECT CONCAT('Column already exists: ', columnName) AS result;
    END IF;
END$$

DELIMITER ;

-- 执行字段添加
CALL AddColumnIfNotExists('sd_player_career_stats', 'total_two_points_made', 'INT DEFAULT 0 COMMENT \'总二分命中数\'');
CALL AddColumnIfNotExists('sd_player_career_stats', 'total_two_points_attempted', 'INT DEFAULT 0 COMMENT \'总二分出手数\'');
CALL AddColumnIfNotExists('sd_player_career_stats', 'total_minutes_played', 'DECIMAL(8,2) DEFAULT 0 COMMENT \'总出场时间(分钟)\'');
CALL AddColumnIfNotExists('sd_player_career_stats', 'avg_minutes_played', 'DECIMAL(5,2) DEFAULT 0 COMMENT \'场均出场时间\'');
CALL AddColumnIfNotExists('sd_player_career_stats', 'avg_efficiency', 'DECIMAL(5,2) DEFAULT 0 COMMENT \'场均效率值\'');
CALL AddColumnIfNotExists('sd_player_career_stats', 'two_point_percentage', 'DECIMAL(5,2) DEFAULT 0 COMMENT \'二分命中率\'');
CALL AddColumnIfNotExists('sd_player_career_stats', 'effective_field_goal_percentage', 'DECIMAL(5,2) DEFAULT 0 COMMENT \'有效投篮命中率\'');
CALL AddColumnIfNotExists('sd_player_career_stats', 'offensive_rebound_rate', 'DECIMAL(5,2) DEFAULT 0 COMMENT \'进攻篮板率\'');
CALL AddColumnIfNotExists('sd_player_career_stats', 'defensive_rebound_rate', 'DECIMAL(5,2) DEFAULT 0 COMMENT \'防守篮板率\'');
CALL AddColumnIfNotExists('sd_player_career_stats', 'assist_turnover_ratio', 'DECIMAL(5,2) DEFAULT 0 COMMENT \'助攻失误比\'');
CALL AddColumnIfNotExists('sd_player_career_stats', 'player_efficiency_rating', 'DECIMAL(5,2) DEFAULT 0 COMMENT \'球员效率指数(PER)\'');
CALL AddColumnIfNotExists('sd_player_career_stats', 'usage_rate', 'DECIMAL(5,2) DEFAULT 0 COMMENT \'使用率\'');
CALL AddColumnIfNotExists('sd_player_career_stats', 'plus_minus', 'DECIMAL(5,2) DEFAULT 0 COMMENT \'净胜分\'');

-- 清理存储过程
DROP PROCEDURE AddColumnIfNotExists;

-- 最终检查表结构
DESCRIBE sd_player_career_stats;