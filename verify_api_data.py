#!/usr/bin/env python3
"""
验证API返回数据的脚本
用于确认后端返回的确切数据
"""

import requests
import json

def verify_app_api():
    """验证APP API返回的数据"""
    url = "http://localhost:48080/app-api/league/player/career/overview?playerId=1&gameType=0"
    
    print("🔍 验证APP API返回数据...")
    print("URL:", url)
    print("=" * 60)
    
    try:
        response = requests.get(url, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("\n📊 完整响应数据:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 提取场均得分
            if data.get('code') == 0 and data.get('data'):
                core_stats = data['data'].get('coreStats')
                if core_stats and core_stats.get('basicStats'):
                    for stat in core_stats['basicStats']:
                        if stat.get('label') == '场均得分':
                            avg_points = stat.get('value')
                            print(f"\n✅ 场均得分: {avg_points}")
                            return avg_points
            
            print("\n❌ 未找到场均得分数据")
            return None
        else:
            print(f"❌ API调用失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def verify_admin_api():
    """验证Admin API返回的数据"""
    url = "http://localhost:48080/admin-api/operation/player-career/get?playerId=1"
    
    print("\n🔍 验证Admin API返回数据...")
    print("URL:", url)
    print("=" * 60)
    
    try:
        response = requests.get(url, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("\n📊 Admin API响应数据结构:")
            
            # 只显示关键部分
            if data.get('code') == 0 and data.get('data'):
                overall_stats = data['data'].get('overallStats')
                if overall_stats:
                    avg_points = overall_stats.get('avgPoints')
                    print(f"overallStats.avgPoints: {avg_points}")
                    return avg_points
            
            print("❌ 未找到场均得分数据")
            return None
        else:
            print(f"❌ API调用失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

if __name__ == "__main__":
    print("🚀 开始验证API数据")
    
    # 验证APP API
    app_avg_points = verify_app_api()
    
    # 验证Admin API
    admin_avg_points = verify_admin_api()
    
    # 比较结果
    print("\n" + "=" * 60)
    print("📊 最终比较结果:")
    print(f"APP API场均得分:   {app_avg_points}")
    print(f"Admin API场均得分: {admin_avg_points}")
    
    if app_avg_points and admin_avg_points:
        if str(app_avg_points) == str(admin_avg_points):
            print("✅ 后端数据一致！问题可能在前端。")
        else:
            print("❌ 后端数据仍不一致！")
    else:
        print("⚠️ 无法获取完整数据进行比较")
    
    print("\n💡 如果后端数据一致但前端显示不一致，请检查：")
    print("1. 前端缓存是否需要清除")
    print("2. 前端是否从正确的字段获取数据")
    print("3. 前端是否有额外的数据处理逻辑")
    print("4. 浏览器开发者工具中的网络请求是否返回正确数据")
