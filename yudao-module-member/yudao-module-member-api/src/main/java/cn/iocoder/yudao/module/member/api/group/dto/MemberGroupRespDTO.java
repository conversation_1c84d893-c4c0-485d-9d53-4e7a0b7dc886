package cn.iocoder.yudao.module.member.api.group.dto;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.validation.InEnum;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class MemberGroupRespDTO {
    private Long id;

    private String name;

    private LocalDateTime createTime;

    private String remark;

    @InEnum(CommonStatusEnum.class)
    private Integer status;
}
