# 数据不一致问题修复验证

## 修复内容

### 问题根源
两个API使用了不同的计算逻辑：
1. **Admin API**: 使用 `PlayerCareerDataInitializerV2.safeDivide()` - 用 `gamesPlayed`（总场次）计算平均值
2. **App API**: 使用 `PlayerStatsCalculatorCommon.calculateAverageStats()` - 用 `validGames`（有得分数据的场次）计算平均值

### 修复方案
修改 `PlayerStatsCalculatorCommon` 类：
1. 在数据累加时，每场比赛都计入 `totalGames`
2. 在计算平均值时，使用 `totalGames` 而不是 `validGames`

## 验证步骤

### 1. 重启应用
```bash
# 重启Spring Boot应用以加载修复后的代码
```

### 2. 清除缓存并重新计算数据
```bash
# 调用修复接口
POST http://localhost:48080/admin-api/operation/player-career/debug/fix-data-inconsistency?playerId=1
```

### 3. 验证两个API返回相同数据
```bash
# 管理端API
GET http://localhost:48080/admin-api/operation/player-career/get?playerId=1

# APP端API  
GET http://localhost:48080/app-api/league/player/career/overview?playerId=1&gameType=0
```

### 4. 使用调试接口验证
```bash
# 比较API数据
GET http://localhost:48080/admin-api/operation/player-career/debug/compare-api-data?playerId=1

# 详细验证数据
GET http://localhost:48080/admin-api/operation/player-career/debug/validate-data?playerId=1&gameType=0
```

## 预期结果

修复后，两个API应该返回相同的场均得分值。

### 修复前
- 管理端API: 18.6 分（使用总场次计算）
- APP端API: 25.6 分（使用有得分数据的场次计算）

### 修复后
- 管理端API: 18.6 分
- APP端API: 18.6 分（现在也使用总场次计算）

## 日志验证

修复后的日志应该显示：
```
📊 场均得分计算修复: 总得分=XXX, 总场次=YYY, 场均得分=18.6 (之前用有效场次ZZZ会导致不一致)
```

## 技术细节

### 修复的关键代码变更

1. **数据累加逻辑**:
```java
// 修复前：只有有得分数据才计入场次
if (gameStat.getPoints() != null) {
    totalPoints += gameStat.getPoints();
    validGames++; // 只有有得分数据的才算有效比赛
}

// 修复后：每场比赛都计入总场次
totalGames++; // 每场比赛都计入
if (gameStat.getPoints() != null) {
    totalPoints += gameStat.getPoints();
    validGames++; // 保留用于其他统计
}
```

2. **平均值计算逻辑**:
```java
// 修复前：使用有效场次
result.setAvgPoints(BigDecimal.valueOf(result.getTotalPoints())
    .divide(BigDecimal.valueOf(validGames), 2, RoundingMode.HALF_UP));

// 修复后：使用总场次
result.setAvgPoints(BigDecimal.valueOf(result.getTotalPoints())
    .divide(BigDecimal.valueOf(totalGames), 2, RoundingMode.HALF_UP));
```

这样确保了两个API使用相同的计算逻辑，解决了数据不一致问题。
