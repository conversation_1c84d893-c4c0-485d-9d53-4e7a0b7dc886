package cn.iocoder.yudao.module.operation.service.career;

import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerCareerStatsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerStatisticsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerGameRelatedDO;
import cn.iocoder.yudao.module.operation.dal.mysql.player.PlayerCareerStatsMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.game.PlayerStatisticsMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.game.PlayerGameRelatedMapper;
import cn.iocoder.yudao.module.operation.service.career.dto.InitializationResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 球员生涯统计服务测试类
 * 
 * <AUTHOR> Assistant
 */
@ExtendWith(MockitoExtension.class)
class PlayerCareerStatsServiceTest {

    @Mock
    private PlayerCareerStatsMapper playerCareerStatsMapper;
    
    @Mock
    private PlayerStatisticsMapper playerStatisticsMapper;
    
    @Mock
    private PlayerGameRelatedMapper playerGameRelatedMapper;
    
    @Mock
    private PlayerCareerDataInitializerV2 careerDataInitializer;
    
    @Mock
    private PlayerCareerStatsCalculator playerCareerStatsCalculator;

    @InjectMocks
    private PlayerCareerStatsService playerCareerStatsService;

    private PlayerCareerStatsDO mockCareerStats;
    private PlayerStatisticsDO mockGameStats;
    private PlayerGameRelatedDO mockGameRelated;

    @BeforeEach
    void setUp() {
        // 初始化模拟数据
        mockCareerStats = createMockCareerStats();
        mockGameStats = createMockGameStats();
        mockGameRelated = createMockGameRelated();
    }

    @Test
    void testCalculateCareerStats_WithExistingData() {
        // Given
        Long playerId = 1L;
        Integer gameType = 0;
        
        when(playerCareerStatsMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(mockCareerStats);

        // When
        PlayerCareerStatsDO result = playerCareerStatsService.calculateCareerStats(playerId, gameType);

        // Then
        assertNotNull(result);
        assertEquals(playerId, result.getPlayerId());
        assertEquals(gameType, result.getGameType());
        assertEquals(mockCareerStats.getAvgPoints(), result.getAvgPoints());
        
        verify(playerCareerStatsMapper).selectOne(any(LambdaQueryWrapper.class));
        verifyNoInteractions(playerCareerStatsCalculator);
    }

    @Test
    void testCalculateCareerStats_WithoutExistingData() {
        // Given
        Long playerId = 1L;
        Integer gameType = 0;
        List<PlayerStatisticsDO> gameStatsList = Arrays.asList(mockGameStats);
        List<PlayerGameRelatedDO> gameResults = Arrays.asList(mockGameRelated);
        
        when(playerCareerStatsMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(null);
        when(playerStatisticsMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(gameStatsList);
        when(playerGameRelatedMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(gameResults);
        when(playerCareerStatsCalculator.calculateFromGameStats(eq(playerId), eq(gameType), eq(gameStatsList)))
                .thenReturn(mockCareerStats);

        // When
        PlayerCareerStatsDO result = playerCareerStatsService.calculateCareerStats(playerId, gameType);

        // Then
        assertNotNull(result);
        assertEquals(playerId, result.getPlayerId());
        
        verify(playerCareerStatsCalculator).calculateFromGameStats(playerId, gameType, gameStatsList);
        verify(playerCareerStatsCalculator).calculateStreakData(mockCareerStats, gameResults);
        verify(playerCareerStatsMapper).insert(mockCareerStats);
    }

    @Test
    void testCalculateCareerStats_WithNullPlayerId() {
        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> playerCareerStatsService.calculateCareerStats(null, 0)
        );
        assertEquals("球员ID不能为空", exception.getMessage());
    }

    @Test
    void testCalculateCareerStats_WithNullGameType() {
        // Given
        Long playerId = 1L;
        
        when(playerCareerStatsMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(mockCareerStats);

        // When
        PlayerCareerStatsDO result = playerCareerStatsService.calculateCareerStats(playerId, null);

        // Then
        assertNotNull(result);
        assertEquals(playerId, result.getPlayerId());
        
        // 验证默认gameType为0
        verify(playerCareerStatsMapper).selectOne(argThat(wrapper -> {
            // 这里可以验证wrapper中的条件是否包含gameType=0
            return true;
        }));
    }

    @Test
    void testCalculateCareerStats_WithEmptyGameStats() {
        // Given
        Long playerId = 1L;
        Integer gameType = 0;
        
        when(playerCareerStatsMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(null);
        when(playerStatisticsMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // When
        PlayerCareerStatsDO result = playerCareerStatsService.calculateCareerStats(playerId, gameType);

        // Then
        assertNotNull(result);
        assertEquals(playerId, result.getPlayerId());
        assertEquals(gameType, result.getGameType());
        assertEquals(0, result.getGamesPlayed());
        
        verifyNoInteractions(playerCareerStatsCalculator);
    }

    @Test
    void testGetAllValidCareerStats() {
        // Given
        Integer gameType = 0;
        List<PlayerCareerStatsDO> mockStatsList = Arrays.asList(mockCareerStats);
        
        when(playerCareerStatsMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(mockStatsList);

        // When
        List<PlayerCareerStatsDO> result = playerCareerStatsService.getAllValidCareerStats(gameType);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(mockCareerStats.getPlayerId(), result.get(0).getPlayerId());
        
        verify(playerCareerStatsMapper).selectList(any(LambdaQueryWrapper.class));
    }

    @Test
    void testGetAllValidCareerStats_WithException() {
        // Given
        when(playerCareerStatsMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenThrow(new RuntimeException("Database error"));

        // When
        List<PlayerCareerStatsDO> result = playerCareerStatsService.getAllValidCareerStats(0);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testUpdatePlayerCareerStats_WithExistingData() {
        // Given
        Long playerId = 1L;
        Integer gameType = 0;
        List<PlayerStatisticsDO> gameStatsList = Arrays.asList(mockGameStats);
        List<PlayerGameRelatedDO> gameResults = Arrays.asList(mockGameRelated);
        
        when(playerStatisticsMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(gameStatsList);
        when(playerGameRelatedMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(gameResults);
        when(playerCareerStatsCalculator.calculateFromGameStats(eq(playerId), eq(gameType), eq(gameStatsList)))
                .thenReturn(mockCareerStats);
        
        // 第一次查询返回null，第二次查询返回存在的数据
        when(playerCareerStatsMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(null)
                .thenReturn(mockCareerStats);

        // When
        playerCareerStatsService.updatePlayerCareerStats(playerId, gameType);

        // Then
        verify(playerCareerStatsCalculator).calculateFromGameStats(playerId, gameType, gameStatsList);
        verify(playerCareerStatsCalculator).calculateStreakData(mockCareerStats, gameResults);
        verify(playerCareerStatsMapper).updateById(mockCareerStats);
    }

    @Test
    void testUpdatePlayerCareerStats_WithNewData() {
        // Given
        Long playerId = 1L;
        Integer gameType = 0;
        List<PlayerStatisticsDO> gameStatsList = Arrays.asList(mockGameStats);
        List<PlayerGameRelatedDO> gameResults = Arrays.asList(mockGameRelated);
        
        when(playerStatisticsMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(gameStatsList);
        when(playerGameRelatedMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(gameResults);
        when(playerCareerStatsCalculator.calculateFromGameStats(eq(playerId), eq(gameType), eq(gameStatsList)))
                .thenReturn(mockCareerStats);
        
        when(playerCareerStatsMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(null);

        // When
        playerCareerStatsService.updatePlayerCareerStats(playerId, gameType);

        // Then
        verify(playerCareerStatsMapper).insert(mockCareerStats);
    }

    @Test
    void testBatchInitializeCareerStats_WithValidPlayerIds() {
        // Given
        List<Long> playerIds = Arrays.asList(1L, 2L, 3L);
        List<PlayerStatisticsDO> gameStatsList = Arrays.asList(mockGameStats);
        List<PlayerGameRelatedDO> gameResults = Arrays.asList(mockGameRelated);
        
        when(playerStatisticsMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(gameStatsList);
        when(playerGameRelatedMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(gameResults);
        when(playerCareerStatsCalculator.calculateFromGameStats(anyLong(), anyInt(), any()))
                .thenReturn(mockCareerStats);
        when(playerCareerStatsMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(null);

        // When
        InitializationResult result = playerCareerStatsService.batchInitializeCareerStats(playerIds);

        // Then
        assertNotNull(result);
        assertEquals(3, result.getSuccessCount());
        assertEquals(0, result.getFailCount());
        
        verify(playerCareerStatsMapper, times(3)).insert(any(PlayerCareerStatsDO.class));
    }

    @Test
    void testBatchInitializeCareerStats_WithEmptyPlayerIds() {
        // When
        InitializationResult result = playerCareerStatsService.batchInitializeCareerStats(Collections.emptyList());

        // Then
        assertNotNull(result);
        assertEquals(0, result.getSuccessCount());
        assertEquals(0, result.getFailCount());
        assertTrue(result.getMessage().contains("无需处理"));
    }

    @Test
    void testBatchInitializeCareerStats_WithNullPlayerIds() {
        // When
        InitializationResult result = playerCareerStatsService.batchInitializeCareerStats(null);

        // Then
        assertNotNull(result);
        assertEquals(0, result.getSuccessCount());
        assertEquals(0, result.getFailCount());
    }

    @Test
    void testForceRecalculateCareerStats() {
        // Given
        Long playerId = 1L;
        List<PlayerStatisticsDO> gameStatsList = Arrays.asList(mockGameStats);
        List<PlayerGameRelatedDO> gameResults = Arrays.asList(mockGameRelated);
        
        when(playerStatisticsMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(gameStatsList);
        when(playerGameRelatedMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(gameResults);
        when(playerCareerStatsCalculator.calculateFromGameStats(anyLong(), anyInt(), any()))
                .thenReturn(mockCareerStats);
        when(playerCareerStatsMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(mockCareerStats);

        // When
        playerCareerStatsService.forceRecalculateCareerStats(playerId);

        // Then
        // 应该为4种比赛类型都调用更新
        verify(playerCareerStatsMapper, times(4)).updateById(any(PlayerCareerStatsDO.class));
    }

    @Test
    void testForceRecalculateCareerStats_WithNullPlayerId() {
        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> playerCareerStatsService.forceRecalculateCareerStats(null)
        );
        assertEquals("球员ID不能为空", exception.getMessage());
    }

    @Test
    void testUpdateWinLossStats() {
        // Given
        Long playerId = 1L;
        Long gameId = 100L;
        List<PlayerGameRelatedDO> gameResults = Arrays.asList(mockGameRelated);
        
        when(playerGameRelatedMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(gameResults)
                .thenReturn(gameResults); // 为每个比赛类型查询一次
        when(playerCareerStatsMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(mockCareerStats);

        // When
        playerCareerStatsService.updateWinLossStats(playerId, gameId);

        // Then
        verify(playerCareerStatsCalculator, times(4)).calculateStreakData(eq(mockCareerStats), eq(gameResults));
        verify(playerCareerStatsMapper, times(4)).updateById(mockCareerStats);
    }

    @Test
    void testUpdateWinLossStats_WithNullParameters() {
        // When & Then
        assertThrows(IllegalArgumentException.class, 
                () -> playerCareerStatsService.updateWinLossStats(null, 1L));
        
        assertThrows(IllegalArgumentException.class, 
                () -> playerCareerStatsService.updateWinLossStats(1L, null));
    }

    @Test
    void testUpdateGameTimeRelatedStats() {
        // Given
        Long playerId = 1L;
        List<PlayerStatisticsDO> gameStatsList = Arrays.asList(mockGameStats);
        
        when(playerStatisticsMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(gameStatsList);
        when(playerCareerStatsMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(mockCareerStats);

        // When
        playerCareerStatsService.updateGameTimeRelatedStats(playerId);

        // Then
        verify(playerCareerStatsCalculator, times(4))
                .calculateTimeRelatedFields(eq(mockCareerStats), eq(gameStatsList));
        verify(playerCareerStatsMapper, times(4)).updateById(mockCareerStats);
    }

    @Test
    void testUpdateGameTimeRelatedStats_WithEmptyGameStats() {
        // Given
        Long playerId = 1L;
        
        when(playerStatisticsMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // When
        playerCareerStatsService.updateGameTimeRelatedStats(playerId);

        // Then
        verifyNoInteractions(playerCareerStatsCalculator);
        verifyNoInteractions(playerCareerStatsMapper);
    }

    // Helper methods to create mock data
    private PlayerCareerStatsDO createMockCareerStats() {
        PlayerCareerStatsDO stats = new PlayerCareerStatsDO();
        stats.setId(1L);
        stats.setPlayerId(1L);
        stats.setGameType(0);
        stats.setGamesPlayed(10);
        stats.setTotalPoints(200);
        stats.setAvgPoints(BigDecimal.valueOf(20.0));
        stats.setTotalRebounds(50);
        stats.setAvgRebounds(BigDecimal.valueOf(5.0));
        stats.setTotalAssists(30);
        stats.setAvgAssists(BigDecimal.valueOf(3.0));
        stats.setWins(7);
        stats.setLosses(3);
        stats.setWinRate(BigDecimal.valueOf(70.0));
        stats.setFirstGameDate(LocalDate.now().minusDays(30));
        stats.setLatestGameDate(LocalDate.now());
        return stats;
    }

    private PlayerStatisticsDO createMockGameStats() {
        PlayerStatisticsDO stats = new PlayerStatisticsDO();
        stats.setId(1L);
        stats.setPlayerId(1L);
        stats.setGameId(100L);
        stats.setSection(0);
        stats.setPoints(20);
        stats.setRebounds(5);
        stats.setAssists(3);
        stats.setSteals(1);
        stats.setBlocks(1);
        stats.setTurnovers(2);
        stats.setFouls(2);
        stats.setFieldGoalsMade(8);
        stats.setFieldGoalsAttempted(15);
        stats.setThreePointsMade(2);
        stats.setThreePointsAttempted(5);
        stats.setFreeThrowsMade(2);
        stats.setFreeThrowsAttempted(2);
        stats.setMinutesPlayed(BigDecimal.valueOf(30.0));
        stats.setCreateTime(LocalDateTime.now());
        return stats;
    }

    private PlayerGameRelatedDO createMockGameRelated() {
        PlayerGameRelatedDO related = new PlayerGameRelatedDO();
        related.setId(1L);
        related.setPlayerId(1L);
        related.setGameId(100L);
        related.setIsWin(true);
        related.setCreateTime(LocalDateTime.now());
        return related;
    }
}