package cn.iocoder.yudao.module.operation.service.career;

import cn.iocoder.yudao.framework.test.core.ut.BaseMockitoUnitTest;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerCareerStatsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerStatisticsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerGameRelatedDO;
import cn.iocoder.yudao.module.operation.dal.mysql.player.PlayerCareerStatsMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.game.PlayerStatisticsMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.game.PlayerGameRelatedMapper;
import cn.iocoder.yudao.module.operation.service.career.dto.InitializationResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 球员生涯统计服务测试类 - 简化版本
 * 
 * <AUTHOR> Assistant
 */
class PlayerCareerStatsServiceSimpleTest extends BaseMockitoUnitTest {

    @Mock
    private PlayerCareerStatsMapper playerCareerStatsMapper;
    
    @Mock
    private PlayerStatisticsMapper playerStatisticsMapper;
    
    @Mock
    private PlayerGameRelatedMapper playerGameRelatedMapper;
    
    @Mock
    private PlayerCareerDataInitializerV2 careerDataInitializer;
    
    @Mock
    private PlayerCareerStatsCalculator playerCareerStatsCalculator;

    @InjectMocks
    private PlayerCareerStatsService playerCareerStatsService;

    private PlayerCareerStatsDO mockCareerStats;
    private PlayerStatisticsDO mockGameStats;
    private PlayerGameRelatedDO mockGameRelated;

    @BeforeEach
    void setUp() {
        // 初始化模拟数据
        mockCareerStats = createMockCareerStats();
        mockGameStats = createMockGameStats();
        mockGameRelated = createMockGameRelated();
    }

    @Test
    void testCalculateCareerStats_WithExistingData() {
        // Given
        Long playerId = 1L;
        Integer gameType = 0;
        
        when(playerCareerStatsMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(mockCareerStats);

        // When
        PlayerCareerStatsDO result = playerCareerStatsService.calculateCareerStats(playerId, gameType);

        // Then
        assertNotNull(result);
        assertEquals(playerId, result.getPlayerId());
        assertEquals(gameType, result.getGameType());
        assertEquals(mockCareerStats.getAvgPoints(), result.getAvgPoints());
        
        verify(playerCareerStatsMapper).selectOne(any(LambdaQueryWrapper.class));
        verifyNoInteractions(playerCareerStatsCalculator);
    }

    @Test
    void testCalculateCareerStats_WithNullPlayerId() {
        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> playerCareerStatsService.calculateCareerStats(null, 0)
        );
        assertEquals("球员ID不能为空", exception.getMessage());
    }

    @Test
    void testGetAllValidCareerStats() {
        // Given
        Integer gameType = 0;
        List<PlayerCareerStatsDO> mockStatsList = Arrays.asList(mockCareerStats);
        
        when(playerCareerStatsMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(mockStatsList);

        // When
        List<PlayerCareerStatsDO> result = playerCareerStatsService.getAllValidCareerStats(gameType);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(mockCareerStats.getPlayerId(), result.get(0).getPlayerId());
        
        verify(playerCareerStatsMapper).selectList(any(LambdaQueryWrapper.class));
    }

    @Test
    void testBatchInitializeCareerStats_WithValidPlayerIds() {
        // Given
        List<Long> playerIds = Arrays.asList(1L, 2L, 3L);
        List<PlayerStatisticsDO> gameStatsList = Arrays.asList(mockGameStats);
        List<PlayerGameRelatedDO> gameResults = Arrays.asList(mockGameRelated);
        
        when(playerStatisticsMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(gameStatsList);
        when(playerGameRelatedMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(gameResults);
        when(playerCareerStatsCalculator.calculateFromGameStats(anyLong(), anyInt(), any()))
                .thenReturn(mockCareerStats);
        when(playerCareerStatsMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(null);

        // When
        InitializationResult result = playerCareerStatsService.batchInitializeCareerStats(playerIds);

        // Then
        assertNotNull(result);
        assertEquals(3, result.getSuccessCount());
        assertEquals(0, result.getFailCount());
        
        verify(playerCareerStatsMapper, times(3)).insert(any(PlayerCareerStatsDO.class));
    }

    @Test
    void testBatchInitializeCareerStats_WithEmptyPlayerIds() {
        // When
        InitializationResult result = playerCareerStatsService.batchInitializeCareerStats(Collections.emptyList());

        // Then
        assertNotNull(result);
        assertEquals(0, result.getSuccessCount());
        assertEquals(0, result.getFailCount());
        assertTrue(result.getMessage().contains("无需处理"));
    }

    // Helper methods to create mock data
    private PlayerCareerStatsDO createMockCareerStats() {
        PlayerCareerStatsDO stats = new PlayerCareerStatsDO();
        stats.setId(1L);
        stats.setPlayerId(1L);
        stats.setGameType(0);
        stats.setGamesPlayed(10);
        stats.setTotalPoints(200);
        stats.setAvgPoints(BigDecimal.valueOf(20.0));
        stats.setTotalRebounds(50);
        stats.setAvgRebounds(BigDecimal.valueOf(5.0));
        stats.setTotalAssists(30);
        stats.setAvgAssists(BigDecimal.valueOf(3.0));
        stats.setWins(7);
        stats.setLosses(3);
        stats.setWinRate(BigDecimal.valueOf(70.0));
        stats.setFirstGameDate(LocalDate.now().minusDays(30));
        stats.setLatestGameDate(LocalDate.now());
        return stats;
    }

    private PlayerStatisticsDO createMockGameStats() {
        PlayerStatisticsDO stats = new PlayerStatisticsDO();
        stats.setId(1L);
        stats.setPlayerId(1L);
        stats.setGameId(100L);
        stats.setSection(0);
        stats.setPoints(20);
        stats.setRebounds(5);
        stats.setAssists(3);
        stats.setSteals(1);
        stats.setBlocks(1);
        stats.setTurnovers(2);
        stats.setFouls(2);
        stats.setFieldGoalsMade(8);
        stats.setFieldGoalsAttempted(15);
        stats.setThreePointsMade(2);
        stats.setThreePointsAttempted(5);
        stats.setFreeThrowsMade(2);
        stats.setFreeThrowsAttempted(2);
        stats.setMinutesPlayed(BigDecimal.valueOf(30.0));
        stats.setCreateTime(LocalDateTime.now());
        return stats;
    }

    private PlayerGameRelatedDO createMockGameRelated() {
        PlayerGameRelatedDO related = new PlayerGameRelatedDO();
        related.setId(1L);
        related.setPlayerId(1L);
        related.setGameId(100L);
        related.setIsWin(true);
        related.setCreateTime(LocalDateTime.now());
        return related;
    }
}