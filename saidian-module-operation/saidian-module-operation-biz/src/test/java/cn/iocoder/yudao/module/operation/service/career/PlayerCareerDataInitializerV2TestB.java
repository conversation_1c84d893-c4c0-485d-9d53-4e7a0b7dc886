package cn.iocoder.yudao.module.operation.service.career;

import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerStatisticsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.GameDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerGameRelatedDO;
import cn.iocoder.yudao.module.operation.service.game.GameServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link PlayerCareerDataInitializerV2} 的单元测试类
 * 主要测试比赛场次计算的准确性
 */
@Import({PlayerCareerDataInitializerV2.class})
public class PlayerCareerDataInitializerV2Test extends BaseDbUnitTest {

    @Test
    public void testGamesPlayedCalculationConsistency() {
        // 创建测试数据：球员在3场比赛中有6条统计记录
        List<PlayerStatisticsDO> playerStats = createMockPlayerStats();
        List<PlayerGameRelatedDO> gameRelations = createMockGameRelations();
        Map<Long, GameDO> gameMap = createMockGameMap();
        
        // 使用反射调用私有方法进行测试
        PlayerCareerDataInitializerV2 initializer = new PlayerCareerDataInitializerV2();
        
        // 测试比赛场次计算的一致性
        testGamesPlayedConsistency(playerStats, gameRelations, gameMap);
    }

    /**
     * 测试比赛场次计算的一致性
     */
    private void testGamesPlayedConsistency(
            List<PlayerStatisticsDO> playerStats, 
            List<PlayerGameRelatedDO> gameRelations, 
            Map<Long, GameDO> gameMap) {
        
        // 1. 测试统计数据中的比赛场次计算
        long uniqueGamesFromStats = playerStats.stream()
                .map(PlayerStatisticsDO::getGameId)
                .distinct()
                .count();
        
        // 2. 测试胜负数据中的比赛场次计算
        long uniqueGamesFromRelations = gameRelations.stream()
                .map(PlayerGameRelatedDO::getGameId)
                .distinct()
                .count();
        
        // 3. 验证一致性
        assertEquals(uniqueGamesFromStats, uniqueGamesFromRelations, 
                "统计数据和胜负数据中的比赛场次应该一致");
        
        // 4. 验证实际期望值
        assertEquals(3, uniqueGamesFromStats, "应该有3场不同的比赛");
        assertEquals(6, playerStats.size(), "应该有6条统计记录");
        assertEquals(3, gameRelations.size(), "应该有3条比赛关系记录");
        
        System.out.println("测试结果:");
        System.out.println("- 统计记录数量: " + playerStats.size());
        System.out.println("- 比赛关系记录数量: " + gameRelations.size());
        System.out.println("- 从统计数据计算的比赛场次: " + uniqueGamesFromStats);
        System.out.println("- 从关系数据计算的比赛场次: " + uniqueGamesFromRelations);
        
        // 5. 模拟胜负计算
        int wins = 0, losses = 0;
        for (PlayerGameRelatedDO relation : gameRelations) {
            GameDO game = gameMap.get(relation.getGameId());
            if (game != null && game.getHomeTeamPoints() != null && game.getGuestTeamPoints() != null) {
                boolean playerTeamIsHome = relation.getTeamId().equals(game.getHomeTeamId());
                if (playerTeamIsHome) {
                    if (game.getHomeTeamPoints() > game.getGuestTeamPoints()) {
                        wins++;
                    } else {
                        losses++;
                    }
                } else {
                    if (game.getGuestTeamPoints() > game.getHomeTeamPoints()) {
                        wins++;
                    } else {
                        losses++;
                    }
                }
            }
        }
        
        System.out.println("- 胜场: " + wins);
        System.out.println("- 负场: " + losses);
        System.out.println("- 胜负总场次: " + (wins + losses));
        
        // 6. 验证胜负总场次与比赛场次一致
        assertEquals(uniqueGamesFromStats, wins + losses, 
                "胜负总场次应该等于实际比赛场次");
    }

    /**
     * 创建模拟的球员统计数据
     * 3场比赛，每场比赛2条记录，总共6条记录
     */
    private List<PlayerStatisticsDO> createMockPlayerStats() {
        List<PlayerStatisticsDO> stats = new ArrayList<>();
        
        // 第1场比赛 - 2条记录
        stats.add(createPlayerStat(1L, 1L, 15));
        stats.add(createPlayerStat(2L, 1L, 10));
        
        // 第2场比赛 - 2条记录  
        stats.add(createPlayerStat(3L, 2L, 20));
        stats.add(createPlayerStat(4L, 2L, 25));
        
        // 第3场比赛 - 2条记录
        stats.add(createPlayerStat(5L, 3L, 30));
        stats.add(createPlayerStat(6L, 3L, 35));
        
        return stats;
    }

    /**
     * 创建模拟的比赛关系数据
     * 3场比赛，每场比赛1条关系记录
     */
    private List<PlayerGameRelatedDO> createMockGameRelations() {
        List<PlayerGameRelatedDO> relations = new ArrayList<>();
        
        relations.add(createGameRelation(1L, 1L, 1L)); // 比赛1，球员在队伍1
        relations.add(createGameRelation(2L, 2L, 1L)); // 比赛2，球员在队伍1
        relations.add(createGameRelation(3L, 3L, 2L)); // 比赛3，球员在队伍2
        
        return relations;
    }

    /**
     * 创建模拟的比赛数据
     */
    private Map<Long, GameDO> createMockGameMap() {
        Map<Long, GameDO> gameMap = new HashMap<>();
        
        gameMap.put(1L, createGame(1L, 1L, 2L, 100, 95)); // 比赛1：队伍1胜队伍2
        gameMap.put(2L, createGame(2L, 1L, 2L, 90, 95));  // 比赛2：队伍1负队伍2
        gameMap.put(3L, createGame(3L, 1L, 2L, 85, 90));  // 比赛3：队伍1负队伍2（球员在队伍2，所以是胜）
        
        return gameMap;
    }

    private PlayerStatisticsDO createPlayerStat(Long id, Long gameId, Integer points) {
        PlayerStatisticsDO stat = new PlayerStatisticsDO();
        stat.setId(id);
        stat.setGameId(gameId);
        stat.setPlayerId(1L);
        stat.setSection(4);
        stat.setPoints(points);
        stat.setDeleted(false);
        return stat;
    }

    private PlayerGameRelatedDO createGameRelation(Long gameId, Long playerId, Long teamId) {
        PlayerGameRelatedDO relation = new PlayerGameRelatedDO();
        relation.setGameId(gameId);
        relation.setPlayerId(playerId);
        relation.setTeamId(teamId);
        relation.setAttend(2); // 出席
        return relation;
    }

    private GameDO createGame(Long id, Long homeTeamId, Long guestTeamId, Integer homePoints, Integer guestPoints) {
        GameDO game = new GameDO();
        game.setId(id);
        game.setHomeTeamId(homeTeamId);
        game.setGuestTeamId(guestTeamId);
        game.setHomeTeamPoints(homePoints);
        game.setGuestTeamPoints(guestPoints);
        game.setStatus(4); // 已结束
        game.setType(1); // 排位赛
        game.setStartTime(LocalDateTime.now());
        return game;
    }
}
