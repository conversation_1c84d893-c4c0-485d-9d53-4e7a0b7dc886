package cn.iocoder.yudao.module.operation.dal.mysql.player;

import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerCareerStatsDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 球员生涯聚合统计Mapper测试类
 * 
 * <AUTHOR> Assistant
 */
@Import({PlayerCareerStatsMapper.class})
class PlayerCareerStatsMapperTest extends BaseDbUnitTest {

    @Resource
    private PlayerCareerStatsMapper playerCareerStatsMapper;

    @Test
    void testInsert() {
        // Given
        PlayerCareerStatsDO careerStats = createMockCareerStats(1L, 0);

        // When
        int result = playerCareerStatsMapper.insert(careerStats);

        // Then
        assertEquals(1, result);
        assertNotNull(careerStats.getId());
        assertTrue(careerStats.getId() > 0);
    }

    @Test
    void testSelectById() {
        // Given
        PlayerCareerStatsDO careerStats = createMockCareerStats(1L, 0);
        playerCareerStatsMapper.insert(careerStats);

        // When
        PlayerCareerStatsDO result = playerCareerStatsMapper.selectById(careerStats.getId());

        // Then
        assertNotNull(result);
        assertEquals(careerStats.getPlayerId(), result.getPlayerId());
        assertEquals(careerStats.getGameType(), result.getGameType());
        assertEquals(careerStats.getGamesPlayed(), result.getGamesPlayed());
        assertEquals(careerStats.getTotalPoints(), result.getTotalPoints());
        assertEquals(0, careerStats.getAvgPoints().compareTo(result.getAvgPoints()));
    }

    @Test
    void testSelectOne_ByPlayerIdAndGameType() {
        // Given
        PlayerCareerStatsDO careerStats = createMockCareerStats(1L, 0);
        playerCareerStatsMapper.insert(careerStats);

        LambdaQueryWrapper<PlayerCareerStatsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PlayerCareerStatsDO::getPlayerId, 1L)
               .eq(PlayerCareerStatsDO::getGameType, 0)
               .eq(PlayerCareerStatsDO::getDeleted, false);

        // When
        PlayerCareerStatsDO result = playerCareerStatsMapper.selectOne(wrapper);

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getPlayerId());
        assertEquals(0, result.getGameType());
        assertFalse(result.getDeleted());
    }

    @Test
    void testSelectList_FilterByGameType() {
        // Given
        PlayerCareerStatsDO careerStats1 = createMockCareerStats(1L, 0);
        PlayerCareerStatsDO careerStats2 = createMockCareerStats(2L, 0);
        PlayerCareerStatsDO careerStats3 = createMockCareerStats(3L, 1); // 不同比赛类型
        
        playerCareerStatsMapper.insert(careerStats1);
        playerCareerStatsMapper.insert(careerStats2);
        playerCareerStatsMapper.insert(careerStats3);

        LambdaQueryWrapper<PlayerCareerStatsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PlayerCareerStatsDO::getGameType, 0)
               .eq(PlayerCareerStatsDO::getDeleted, false)
               .ge(PlayerCareerStatsDO::getGamesPlayed, 5) // 至少5场比赛
               .orderByDesc(PlayerCareerStatsDO::getAvgPoints);

        // When
        List<PlayerCareerStatsDO> result = playerCareerStatsMapper.selectList(wrapper);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size()); // 只有gameType=0的两条记录
        
        // 验证排序（按场均得分倒序）
        assertTrue(result.get(0).getAvgPoints().compareTo(result.get(1).getAvgPoints()) >= 0);
        
        // 验证每条记录都满足条件
        for (PlayerCareerStatsDO stats : result) {
            assertEquals(0, stats.getGameType());
            assertFalse(stats.getDeleted());
            assertTrue(stats.getGamesPlayed() >= 5);
        }
    }

    @Test
    void testUpdateById() {
        // Given
        PlayerCareerStatsDO careerStats = createMockCareerStats(1L, 0);
        playerCareerStatsMapper.insert(careerStats);

        // 修改数据
        careerStats.setGamesPlayed(15);
        careerStats.setTotalPoints(300);
        careerStats.setAvgPoints(BigDecimal.valueOf(20.0));
        careerStats.setUpdater("test-updater");

        // When
        int result = playerCareerStatsMapper.updateById(careerStats);

        // Then
        assertEquals(1, result);

        // 验证更新后的数据
        PlayerCareerStatsDO updated = playerCareerStatsMapper.selectById(careerStats.getId());
        assertEquals(15, updated.getGamesPlayed());
        assertEquals(300, updated.getTotalPoints());
        assertEquals(0, BigDecimal.valueOf(20.0).compareTo(updated.getAvgPoints()));
        assertEquals("test-updater", updated.getUpdater());
    }

    @Test
    void testDeleteById() {
        // Given
        PlayerCareerStatsDO careerStats = createMockCareerStats(1L, 0);
        playerCareerStatsMapper.insert(careerStats);
        
        Long id = careerStats.getId();
        assertNotNull(playerCareerStatsMapper.selectById(id));

        // When
        int result = playerCareerStatsMapper.deleteById(id);

        // Then
        assertEquals(1, result);
        
        // 验证逻辑删除（deleted字段变为true）
        PlayerCareerStatsDO deleted = playerCareerStatsMapper.selectById(id);
        // 如果使用逻辑删除，这里应该是null或者deleted=true
        // 具体行为取决于MyBatis Plus的配置
    }

    @Test
    void testSelectList_EmptyResult() {
        // Given
        LambdaQueryWrapper<PlayerCareerStatsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PlayerCareerStatsDO::getPlayerId, 999L); // 不存在的球员ID

        // When
        List<PlayerCareerStatsDO> result = playerCareerStatsMapper.selectList(wrapper);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testSelectList_MultipleGameTypes() {
        // Given
        PlayerCareerStatsDO stats1 = createMockCareerStats(1L, 0); // 全部比赛
        PlayerCareerStatsDO stats2 = createMockCareerStats(1L, 1); // 排位赛
        PlayerCareerStatsDO stats3 = createMockCareerStats(1L, 2); // 友谊赛
        PlayerCareerStatsDO stats4 = createMockCareerStats(1L, 3); // 联赛
        
        playerCareerStatsMapper.insert(stats1);
        playerCareerStatsMapper.insert(stats2);
        playerCareerStatsMapper.insert(stats3);
        playerCareerStatsMapper.insert(stats4);

        LambdaQueryWrapper<PlayerCareerStatsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PlayerCareerStatsDO::getPlayerId, 1L)
               .eq(PlayerCareerStatsDO::getDeleted, false)
               .orderByAsc(PlayerCareerStatsDO::getGameType);

        // When
        List<PlayerCareerStatsDO> result = playerCareerStatsMapper.selectList(wrapper);

        // Then
        assertNotNull(result);
        assertEquals(4, result.size());
        
        // 验证排序
        for (int i = 0; i < result.size(); i++) {
            assertEquals(i, result.get(i).getGameType());
            assertEquals(1L, result.get(i).getPlayerId());
        }
    }

    @Test
    void testSelectList_WithComplexConditions() {
        // Given
        PlayerCareerStatsDO highScorerStats = createMockCareerStats(1L, 0);
        highScorerStats.setGamesPlayed(20);
        highScorerStats.setAvgPoints(BigDecimal.valueOf(25.0));
        
        PlayerCareerStatsDO lowScorerStats = createMockCareerStats(2L, 0);
        lowScorerStats.setGamesPlayed(8);
        lowScorerStats.setAvgPoints(BigDecimal.valueOf(10.0));
        
        PlayerCareerStatsDO ineligibleStats = createMockCareerStats(3L, 0);
        ineligibleStats.setGamesPlayed(3); // 少于5场，不符合条件
        ineligibleStats.setAvgPoints(BigDecimal.valueOf(30.0));
        
        playerCareerStatsMapper.insert(highScorerStats);
        playerCareerStatsMapper.insert(lowScorerStats);
        playerCareerStatsMapper.insert(ineligibleStats);

        LambdaQueryWrapper<PlayerCareerStatsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PlayerCareerStatsDO::getGameType, 0)
               .eq(PlayerCareerStatsDO::getDeleted, false)
               .ge(PlayerCareerStatsDO::getGamesPlayed, 5) // 至少5场比赛
               .ge(PlayerCareerStatsDO::getAvgPoints, BigDecimal.valueOf(15.0)) // 场均至少15分
               .orderByDesc(PlayerCareerStatsDO::getAvgPoints);

        // When
        List<PlayerCareerStatsDO> result = playerCareerStatsMapper.selectList(wrapper);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size()); // 只有高分球员符合条件
        assertEquals(1L, result.get(0).getPlayerId());
        assertEquals(0, BigDecimal.valueOf(25.0).compareTo(result.get(0).getAvgPoints()));
    }

    @Test
    void testDeleteAll() {
        // Given
        playerCareerStatsMapper.insert(createMockCareerStats(1L, 0));
        playerCareerStatsMapper.insert(createMockCareerStats(2L, 0));
        playerCareerStatsMapper.insert(createMockCareerStats(3L, 1));

        // When
        playerCareerStatsMapper.deleteAll();

        // Then
        List<PlayerCareerStatsDO> allStats = playerCareerStatsMapper.selectList(
                new LambdaQueryWrapper<PlayerCareerStatsDO>()
                        .eq(PlayerCareerStatsDO::getDeleted, false));
        
        assertTrue(allStats.isEmpty());
    }

    @Test
    void testUniqueIndex_PlayerGameType() {
        // Given
        PlayerCareerStatsDO stats1 = createMockCareerStats(1L, 0);
        playerCareerStatsMapper.insert(stats1);

        // When & Then
        PlayerCareerStatsDO duplicateStats = createMockCareerStats(1L, 0);
        
        // 尝试插入重复的player_id和game_type组合应该失败
        assertThrows(Exception.class, () -> {
            playerCareerStatsMapper.insert(duplicateStats);
        });
    }

    // Helper method to create mock data
    private PlayerCareerStatsDO createMockCareerStats(Long playerId, Integer gameType) {
        PlayerCareerStatsDO stats = new PlayerCareerStatsDO();
        stats.setPlayerId(playerId);
        stats.setGameType(gameType);
        stats.setStatScope("career");
        stats.setTotalSeasons(1);
        stats.setFirstGameDate(LocalDate.now().minusDays(30));
        stats.setLatestGameDate(LocalDate.now());
        stats.setGamesPlayed(10);
        stats.setWins(7);
        stats.setLosses(3);
        stats.setWinRate(BigDecimal.valueOf(70.0));
        stats.setTotalPoints(200);
        stats.setAvgPoints(BigDecimal.valueOf(20.0));
        stats.setTotalRebounds(50);
        stats.setAvgRebounds(BigDecimal.valueOf(5.0));
        stats.setTotalAssists(30);
        stats.setAvgAssists(BigDecimal.valueOf(3.0));
        stats.setTotalSteals(15);
        stats.setAvgSteals(BigDecimal.valueOf(1.5));
        stats.setTotalBlocks(8);
        stats.setAvgBlocks(BigDecimal.valueOf(0.8));
        stats.setTotalTurnovers(25);
        stats.setAvgTurnovers(BigDecimal.valueOf(2.5));
        stats.setTotalFouls(30);
        stats.setAvgFouls(BigDecimal.valueOf(3.0));
        stats.setTotalPlayingTime(18000); // 300分钟 = 18000秒
        stats.setAvgPlayingTime(BigDecimal.valueOf(30.0));
        stats.setTotalFieldGoalsAttempted(150);
        stats.setTotalFieldGoalsMade(75);
        stats.setFieldGoalPercentage(BigDecimal.valueOf(50.0));
        stats.setTotalThreePointsAttempted(50);
        stats.setTotalThreePointsMade(20);
        stats.setThreePointPercentage(BigDecimal.valueOf(40.0));
        stats.setTotalTwoPointsAttempted(100);
        stats.setTotalTwoPointsMade(55);
        stats.setTwoPointPercentage(BigDecimal.valueOf(55.0));
        stats.setTotalFreeThrowsAttempted(25);
        stats.setTotalFreeThrowsMade(20);
        stats.setFreeThrowPercentage(BigDecimal.valueOf(80.0));
        stats.setTotalOffensiveRebounds(20);
        stats.setAvgOffensiveRebounds(BigDecimal.valueOf(2.0));
        stats.setTotalDefensiveRebounds(30);
        stats.setAvgDefensiveRebounds(BigDecimal.valueOf(3.0));
        stats.setEfficiencyRating(BigDecimal.valueOf(15.0));
        stats.setTrueShootingPercentage(BigDecimal.valueOf(55.0));
        stats.setPlayerEfficiencyRating(BigDecimal.valueOf(18.5));
        stats.setUsageRate(BigDecimal.valueOf(25.0));
        stats.setAssistRate(BigDecimal.valueOf(20.0));
        stats.setTurnoverRate(BigDecimal.valueOf(15.0));
        stats.setStealRate(BigDecimal.valueOf(2.5));
        stats.setBlockRate(BigDecimal.valueOf(1.8));
        stats.setCurrentStreak(3);
        stats.setMaxWinStreak(5);
        stats.setMaxLossStreak(2);
        stats.setDoubleDoubles(3);
        stats.setTripleDoubles(1);
        stats.setNearTripleDoubles(2);
        stats.setBestPoints(35);
        stats.setBestRebounds(12);
        stats.setBestAssists(8);
        stats.setBestSteals(4);
        stats.setBestBlocks(3);
        stats.setPointsRank(10);
        stats.setReboundsRank(15);
        stats.setAssistsRank(8);
        stats.setEfficiencyRank(12);
        stats.setOverallRank(5);
        stats.setCreator("test");
        stats.setUpdater("test");
        stats.setDeleted(false);
        return stats;
    }
}