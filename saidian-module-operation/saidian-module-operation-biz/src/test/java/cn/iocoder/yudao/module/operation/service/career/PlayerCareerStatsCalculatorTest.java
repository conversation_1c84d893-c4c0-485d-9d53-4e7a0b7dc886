package cn.iocoder.yudao.module.operation.service.career;

import cn.iocoder.yudao.framework.test.core.ut.BaseMockitoUnitTest;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerCareerStatsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerStatisticsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerGameRelatedDO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * 球员生涯统计计算器测试
 * 
 * TDD测试：先写测试，后实现功能
 * 
 * <AUTHOR> Developer
 */
class PlayerCareerStatsCalculatorTest extends BaseMockitoUnitTest {

    @InjectMocks
    private PlayerCareerStatsCalculator playerCareerStatsCalculator;
    
    @Mock
    private PlayerStatsCalculatorCommon commonCalculator;

    private List<PlayerStatisticsDO> sampleGameStats;
    private List<PlayerGameRelatedDO> sampleGameResults;

    @BeforeEach
    void setUp() {
        sampleGameStats = createSampleGameStats();
        sampleGameResults = createSampleGameResults();
        
        // Mock CommonCalculator的聚合统计结果
        PlayerStatsCalculatorCommon.StatsAggregationResult mockAggregationResult = createMockAggregationResult();
        when(commonCalculator.aggregateStats(any())).thenReturn(mockAggregationResult);
        
        // Mock CommonCalculator的连胜数据结果
        PlayerStatsCalculatorCommon.StreakDataResult mockStreakResult = createMockStreakResult();
        when(commonCalculator.calculateStreakData(any())).thenReturn(mockStreakResult);
    }

    /**
     * 测试：从比赛统计数据计算生涯数据 - 正常情况
     * 
     * 🟢 绿阶段：方法已存在，测试应该通过
     */
    @Test
    void testCalculateFromGameStats_ShouldCalculateCorrectly() {
        // Given
        Long playerId = 1L;
        Integer gameType = 0;
        
        // When
        PlayerCareerStatsDO result = playerCareerStatsCalculator.calculateFromGameStats(
            playerId, gameType, sampleGameStats);
        
        // Then
        assertNotNull(result);
        assertEquals(playerId, result.getPlayerId());
        assertEquals(gameType, result.getGameType());
        
        System.out.println("🟢 绿阶段：calculateFromGameStats方法正常工作");
    }

    /**
     * 测试：计算连胜数据
     * 
     * 🟢 绿阶段：方法已存在，测试应该通过
     */
    @Test
    void testCalculateStreakData_ShouldCalculateWinStreak() {
        // Given
        PlayerCareerStatsDO careerStats = new PlayerCareerStatsDO();
        careerStats.setPlayerId(1L);
        
        // When
        playerCareerStatsCalculator.calculateStreakData(careerStats, sampleGameResults);
        
        // Then
        assertNotNull(careerStats.getCurrentStreak());
        assertNotNull(careerStats.getTotalWins());
        assertNotNull(careerStats.getTotalLosses());
        
        System.out.println("🟢 绿阶段：calculateStreakData方法正常工作");
    }

    /**
     * 测试：计算时间相关字段
     * 
     * 🟢 绿阶段：方法已存在，测试应该通过
     */
    @Test
    void testCalculateTimeRelatedFields_ShouldSetDateFields() {
        // Given
        PlayerCareerStatsDO careerStats = new PlayerCareerStatsDO();
        careerStats.setPlayerId(1L);
        
        // When
        playerCareerStatsCalculator.calculateTimeRelatedFields(careerStats, sampleGameStats);
        
        // Then
        assertNotNull(careerStats.getFirstGameDate());
        assertNotNull(careerStats.getLatestGameDate());
        assertTrue(careerStats.getFirstGameDate().isBefore(careerStats.getLatestGameDate()) ||
                  careerStats.getFirstGameDate().isEqual(careerStats.getLatestGameDate()));
        
        System.out.println("🟢 绿阶段：calculateTimeRelatedFields方法正常工作");
    }

    /**
     * 测试：空数据处理
     */
    @Test
    void testCalculateFromGameStats_WithEmptyData_ShouldReturnEmptyStats() {
        // Given
        Long playerId = 1L;
        Integer gameType = 0;
        List<PlayerStatisticsDO> emptyStats = Collections.emptyList();
        
        // When
        PlayerCareerStatsDO result = playerCareerStatsCalculator.calculateFromGameStats(
            playerId, gameType, emptyStats);
        
        // Then
        assertNotNull(result);
        assertEquals(playerId, result.getPlayerId());
        assertEquals(gameType, result.getGameType());
        assertEquals(0, result.getGamesPlayed().intValue());
        
        System.out.println("🟢 绿阶段：空数据处理正常");
    }

    /**
     * TDD 红阶段：测试命中率计算 - 应该失败，推动实现
     * 
     * 🔴 红阶段：这个测试会失败，因为方法还未实现
     */
    @Test
    void testCalculateShootingPercentages_ShouldCalculateCorrectly() {
        // Given
        PlayerCareerStatsDO careerStats = new PlayerCareerStatsDO();
        careerStats.setTotalTwoPointsMade(100);
        careerStats.setTotalTwoPointsAttempted(200);
        careerStats.setTotalThreePointsMade(30);
        careerStats.setTotalThreePointsAttempted(100);
        careerStats.setTotalFreeThrowsMade(80);
        careerStats.setTotalFreeThrowsAttempted(100);
        
        // When
        playerCareerStatsCalculator.calculateShootingPercentages(careerStats);
        
        // Then
        assertEquals(new BigDecimal("0.5000"), careerStats.getTwoPointPercentage());
        assertEquals(new BigDecimal("0.3000"), careerStats.getThreePointPercentage());
        assertEquals(new BigDecimal("0.8000"), careerStats.getFreeThrowPercentage());
        
        // 计算综合命中率 (100+30)/(200+100) = 130/300 = 0.4333
        assertEquals(new BigDecimal("0.4333"), careerStats.getFieldGoalPercentage());
        
        System.out.println("🔴 红阶段：命中率计算测试 - 应该失败，推动实现");
    }

    /**
     * TDD 红阶段：测试真实命中率计算 - 应该失败
     * 
     * 🔴 红阶段：测试高级统计指标计算
     */
    @Test
    void testCalculateTrueShootingPercentage_ShouldCalculateCorrectly() {
        // Given
        PlayerCareerStatsDO careerStats = new PlayerCareerStatsDO();
        careerStats.setTotalPoints(1000);
        careerStats.setTotalTwoPointsAttempted(200);
        careerStats.setTotalThreePointsAttempted(100);
        careerStats.setTotalFreeThrowsAttempted(100);
        
        // 真实命中率公式：总得分 / (2 * (投篮出手 + 0.44 * 罚球出手))
        // 预期结果：1000 / (2 * (300 + 0.44 * 100)) = 1000 / (2 * 344) = 1000 / 688 ≈ 0.4535
        
        // When
        playerCareerStatsCalculator.calculateAdvancedStats(careerStats);
        
        // Then
        assertEquals(new BigDecimal("0.4535"), careerStats.getTrueShootingPercentage());
        
        System.out.println("🔴 红阶段：真实命中率计算测试 - 应该失败");
    }

    /**
     * TDD 红阶段：测试球员效率值计算 - 应该失败
     * 
     * 🔴 红阶段：PER(球员效率值)是复杂的综合指标
     */
    @Test
    void testCalculatePlayerEfficiencyRating_ShouldCalculateCorrectly() {
        // Given
        PlayerCareerStatsDO careerStats = new PlayerCareerStatsDO();
        careerStats.setGamesPlayed(50);
        careerStats.setTotalPoints(1000);
        careerStats.setTotalRebounds(300);
        careerStats.setTotalAssists(200);
        careerStats.setTotalSteals(50);
        careerStats.setTotalBlocks(30);
        careerStats.setTotalTurnovers(100);
        careerStats.setTotalFouls(80);
        careerStats.setTotalTwoPointsMade(300);
        careerStats.setTotalTwoPointsAttempted(600);
        careerStats.setTotalThreePointsMade(50);
        careerStats.setTotalThreePointsAttempted(150);
        careerStats.setTotalFreeThrowsMade(200);
        careerStats.setTotalFreeThrowsAttempted(250);
        
        // When
        playerCareerStatsCalculator.calculateAdvancedStats(careerStats);
        
        // Then
        // PER的标准平均值是15.0，优秀球员通常在20以上
        assertNotNull(careerStats.getPlayerEfficiencyRating());
        assertTrue(careerStats.getPlayerEfficiencyRating().compareTo(BigDecimal.ZERO) > 0);
        
        System.out.println("🔴 红阶段：PER计算测试 - 应该失败");
    }

    /**
     * TDD 红阶段：测试场均数据计算 - 应该失败
     * 
     * 🔴 红阶段：场均数据是基础但重要的统计
     */
    @Test
    void testCalculateAverageStats_ShouldCalculateCorrectly() {
        // Given
        PlayerCareerStatsDO careerStats = new PlayerCareerStatsDO();
        careerStats.setGamesPlayed(20);
        careerStats.setTotalPoints(400);
        careerStats.setTotalRebounds(160);
        careerStats.setTotalAssists(100);
        careerStats.setTotalSteals(40);
        careerStats.setTotalBlocks(20);
        
        // When
        playerCareerStatsCalculator.calculateAverageStats(careerStats);
        
        // Then
        assertEquals(new BigDecimal("20.0"), careerStats.getAvgPoints());
        assertEquals(new BigDecimal("8.0"), careerStats.getAvgRebounds());
        assertEquals(new BigDecimal("5.0"), careerStats.getAvgAssists());
        assertEquals(new BigDecimal("2.0"), careerStats.getAvgSteals());
        assertEquals(new BigDecimal("1.0"), careerStats.getAvgBlocks());
        
        System.out.println("🔴 红阶段：场均数据计算测试 - 应该失败");
    }

    /**
     * TDD 红阶段：测试胜率计算 - 应该失败
     * 
     * 🔴 红阶段：胜率计算是用户最关心的数据之一
     */
    @Test
    void testCalculateWinRate_ShouldCalculateCorrectly() {
        // Given
        PlayerCareerStatsDO careerStats = new PlayerCareerStatsDO();
        careerStats.setTotalWins(15);
        careerStats.setTotalLosses(5);
        
        // When
        playerCareerStatsCalculator.calculateWinRate(careerStats);
        
        // Then
        // 15/(15+5) = 15/20 = 0.75 = 75%
        assertEquals(new BigDecimal("0.7500"), careerStats.getWinRate());
        
        System.out.println("🔴 红阶段：胜率计算测试 - 应该失败");
    }

    /**
     * TDD 红阶段：测试连胜数据计算的边界情况 - 应该失败
     * 
     * 🔴 红阶段：连胜计算需要处理复杂的时间序列逻辑
     */
    @Test
    void testCalculateStreakData_WithComplexPattern_ShouldCalculateCorrectly() {
        // Given
        PlayerCareerStatsDO careerStats = new PlayerCareerStatsDO();
        careerStats.setPlayerId(1L);
        
        // 创建复杂的胜负模式：胜-胜-胜-负-胜-胜-负-胜 (当前2连胜，最长3连胜)
        List<PlayerGameRelatedDO> complexGameResults = Arrays.asList(
            createGameResultWithDate(1L, 1, LocalDateTime.now().minusDays(8)), // 胜
            createGameResultWithDate(2L, 1, LocalDateTime.now().minusDays(7)), // 胜  
            createGameResultWithDate(3L, 1, LocalDateTime.now().minusDays(6)), // 胜 (最长连胜3)
            createGameResultWithDate(4L, 0, LocalDateTime.now().minusDays(5)), // 负
            createGameResultWithDate(5L, 1, LocalDateTime.now().minusDays(4)), // 胜
            createGameResultWithDate(6L, 1, LocalDateTime.now().minusDays(3)), // 胜 (当前连胜2)
            createGameResultWithDate(7L, 0, LocalDateTime.now().minusDays(2)), // 负
            createGameResultWithDate(8L, 1, LocalDateTime.now().minusDays(1))  // 胜 (当前连胜1)
        );
        
        // When
        playerCareerStatsCalculator.calculateStreakData(careerStats, complexGameResults);
        
        // Then
        assertEquals(Integer.valueOf(1), careerStats.getCurrentStreak()); // 当前1连胜
        assertEquals(Integer.valueOf(3), careerStats.getMaxWinStreak()); // 最长3连胜
        assertEquals(Integer.valueOf(5), careerStats.getTotalWins()); // 总胜场5
        assertEquals(Integer.valueOf(3), careerStats.getTotalLosses()); // 总负场3
        
        System.out.println("🔴 红阶段：复杂连胜模式计算测试 - 应该失败");
    }

    /**
     * TDD 红阶段：测试数据验证 - 应该失败
     * 
     * 🔴 红阶段：数据验证确保计算结果的合理性
     */
    @Test
    void testValidateCalculatedData_ShouldDetectInconsistencies() {
        // Given
        PlayerCareerStatsDO careerStats = new PlayerCareerStatsDO();
        careerStats.setTotalTwoPointsMade(100);
        careerStats.setTotalTwoPointsAttempted(80); // 错误：命中数大于出手数
        
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            playerCareerStatsCalculator.validateCalculatedData(careerStats);
        });
        
        System.out.println("🔴 红阶段：数据验证测试 - 应该失败");
    }

    // 辅助方法：创建带时间的比赛结果
    private PlayerGameRelatedDO createGameResultWithDate(Long gameId, Integer attend, LocalDateTime createTime) {
        PlayerGameRelatedDO result = new PlayerGameRelatedDO();
        result.setId(gameId);
        result.setGameId(gameId);
        result.setPlayerId(1L);
        result.setAttend(attend);
        result.setCreateTime(createTime);
        return result;
    }

    // 辅助方法：创建样本比赛统计数据
    private List<PlayerStatisticsDO> createSampleGameStats() {
        return Arrays.asList(
            createGameStat(1L, 20, 4, 2, 1, 10, 20, 2, 5),
            createGameStat(2L, 25, 6, 3, 2, 12, 22, 3, 8),
            createGameStat(3L, 15, 2, 1, 0, 8, 18, 1, 3)
        );
    }

    // 辅助方法：创建单个比赛统计 - 使用正确的字段名
    private PlayerStatisticsDO createGameStat(Long gameId, Integer points, Integer assists, 
                                            Integer steals, Integer blocks,
                                            Integer twoPointMakes, Integer twoPointAttempts, 
                                            Integer threePointMakes, Integer threePointAttempts) {
        PlayerStatisticsDO stat = new PlayerStatisticsDO();
        stat.setId(gameId);
        stat.setGameId(gameId);
        stat.setPlayerId(1L);
        stat.setSection(0); // 第0节表示全场汇总
        stat.setPoints(points);
        stat.setOffensiveRebounds(2); // 前场篮板
        stat.setDefensiveRebounds(3); // 后场篮板
        stat.setAssists(assists);
        stat.setSteals(steals);
        stat.setBlocks(blocks);
        stat.setTwoPointMakes(twoPointMakes);
        stat.setTwoPointAttempts(twoPointAttempts);
        stat.setThreePointMakes(threePointMakes);
        stat.setThreePointAttempts(threePointAttempts);
        stat.setCreateTime(LocalDateTime.now().minusDays(gameId));
        return stat;
    }

    // 辅助方法：创建样本比赛结果数据
    private List<PlayerGameRelatedDO> createSampleGameResults() {
        return Arrays.asList(
            createGameResult(1L, 1),  // 第一场比赛
            createGameResult(2L, 2),  // 第二场比赛
            createGameResult(3L, 1)   // 第三场比赛
        );
    }

    // 辅助方法：创建单个比赛结果 - 使用正确的字段名
    private PlayerGameRelatedDO createGameResult(Long gameId, Integer attend) {
        PlayerGameRelatedDO result = new PlayerGameRelatedDO();
        result.setId(gameId);
        result.setGameId(gameId);
        result.setPlayerId(1L);
        result.setAttend(attend); // 使用attend字段而非isWin
        result.setCreateTime(LocalDateTime.now().minusDays(4 - gameId)); // 按时间顺序
        return result;
    }
    
    // 辅助方法：创建Mock聚合统计结果
    private PlayerStatsCalculatorCommon.StatsAggregationResult createMockAggregationResult() {
        PlayerStatsCalculatorCommon.StatsAggregationResult result = new PlayerStatsCalculatorCommon.StatsAggregationResult();
        result.setTotalGames(3);
        result.setValidGames(3);
        result.setTotalPoints(60); // 3场比赛总计60分
        result.setTotalRebounds(15); // 总计篮板
        result.setTotalOffensiveRebounds(6); // 前场篮板
        result.setTotalDefensiveRebounds(9); // 后场篮板
        result.setTotalAssists(12); // 总计助攻
        result.setTotalSteals(3); // 总计抢断
        result.setTotalBlocks(3); // 总计盖帽
        result.setTotalTurnovers(0); // 失误
        result.setTotalFouls(0); // 犯规
        result.setTotalFieldGoalsMade(36); // 总命中数
        result.setTotalFieldGoalsAttempted(60); // 总出手数
        result.setTotalTwoPointsMade(30); // 二分命中
        result.setTotalTwoPointsAttempted(50); // 二分出手
        result.setTotalThreePointsMade(6); // 三分命中
        result.setTotalThreePointsAttempted(16); // 三分出手
        result.setTotalFreeThrowsMade(0); // 罚球命中
        result.setTotalFreeThrowsAttempted(0); // 罚球出手
        result.setTotalMinutes(BigDecimal.valueOf(90)); // 总时间
        result.setTotalEfficiency(BigDecimal.valueOf(60)); // 总效率值
        
        // 设置计算好的平均值
        result.setAvgPoints(BigDecimal.valueOf(20)); // 场均20分
        result.setAvgRebounds(BigDecimal.valueOf(5)); // 场均5篮板
        result.setAvgAssists(BigDecimal.valueOf(4)); // 场均4助攻
        
        return result;
    }
    
    // 辅助方法：创建Mock连胜数据结果
    private PlayerStatsCalculatorCommon.StreakDataResult createMockStreakResult() {
        PlayerStatsCalculatorCommon.StreakDataResult result = new PlayerStatsCalculatorCommon.StreakDataResult();
        result.setCurrentStreak(2); // 当前2连胜
        result.setMaxWinStreak(3); // 最大3连胜
        result.setMaxLoseStreak(1); // 最大1连败
        result.setStreakStartDate(LocalDate.now().minusDays(3)); // 连胜开始日期
        result.setTotalWins(5); // 总胜场
        result.setTotalLosses(3); // 总负场
        result.setWinRate(BigDecimal.valueOf(0.625)); // 胜率62.5%
        return result;
    }
}