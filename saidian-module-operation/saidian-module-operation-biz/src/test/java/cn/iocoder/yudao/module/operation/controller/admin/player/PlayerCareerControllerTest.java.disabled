package cn.iocoder.yudao.module.operation.controller.admin.player;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseRedisUnitTest;
import cn.iocoder.yudao.module.operation.controller.admin.player.vo.PlayerCareerVO;
import cn.iocoder.yudao.module.operation.controller.admin.player.vo.AbilityTrendVO;
import cn.iocoder.yudao.module.operation.service.career.PlayerCareerService;
import cn.iocoder.yudao.module.operation.service.career.PlayerCareerStatsService;
import cn.iocoder.yudao.module.operation.service.career.RadarChartCacheService;
import cn.iocoder.yudao.module.operation.service.career.dto.InitializationResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.http.MediaType;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 球员生涯数据Controller测试类
 * 
 * <AUTHOR> Assistant
 */
class PlayerCareerControllerTest extends BaseRedisUnitTest {

    @MockBean
    private PlayerCareerService playerCareerService;
    
    @MockBean
    private PlayerCareerStatsService playerCareerStatsService;
    
    @MockBean
    private RadarChartCacheService radarChartCacheService;

    private PlayerCareerController playerCareerController;
    private MockMvc mockMvc;
    
    private PlayerCareerVO mockPlayerCareerVO;
    private AbilityTrendVO mockAbilityTrendVO;
    private InitializationResult mockInitializationResult;

    @BeforeEach
    void setUp() {
        playerCareerController = new PlayerCareerController();
        // 通过反射注入mock依赖
        injectMockDependencies();
        
        mockMvc = MockMvcBuilders.standaloneSetup(playerCareerController).build();
        
        // 初始化模拟数据
        mockPlayerCareerVO = createMockPlayerCareerVO();
        mockAbilityTrendVO = createMockAbilityTrendVO();
        mockInitializationResult = createMockInitializationResult();
    }

    @Test
    void testGetPlayerCareer_Success() {
        // Given
        Long playerId = 1L;
        when(playerCareerService.getPlayerCareer(playerId)).thenReturn(mockPlayerCareerVO);

        // When
        CommonResult<PlayerCareerVO> result = playerCareerController.getPlayerCareer(playerId);

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(mockPlayerCareerVO, result.getData());
        assertEquals(playerId, result.getData().getPlayerId());
        
        verify(playerCareerService).getPlayerCareer(playerId);
    }

    @Test
    void testGetPlayerCareer_WithException() {
        // Given
        Long playerId = 1L;
        when(playerCareerService.getPlayerCareer(playerId))
                .thenThrow(new RuntimeException("Service error"));

        // When & Then
        assertThrows(RuntimeException.class, () -> 
                playerCareerController.getPlayerCareer(playerId));
        
        verify(playerCareerService).getPlayerCareer(playerId);
    }

    @Test
    void testRefreshPlayerCareerStats_Success() {
        // Given
        Long playerId = 1L;
        when(playerCareerService.refreshPlayerCareerStats(playerId)).thenReturn(true);

        // When
        CommonResult<Boolean> result = playerCareerController.refreshPlayerCareerStats(playerId);

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertTrue(result.getData());
        
        verify(playerCareerService).refreshPlayerCareerStats(playerId);
    }

    @Test
    void testRefreshPlayerCareerStats_Failed() {
        // Given
        Long playerId = 1L;
        when(playerCareerService.refreshPlayerCareerStats(playerId)).thenReturn(false);

        // When
        CommonResult<Boolean> result = playerCareerController.refreshPlayerCareerStats(playerId);

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess()); // CommonResult本身成功
        assertFalse(result.getData()); // 但业务结果失败
        
        verify(playerCareerService).refreshPlayerCareerStats(playerId);
    }

    @Test
    void testRefreshPlayerCareerStatsByGameType_Success() {
        // Given
        Long playerId = 1L;
        Integer gameType = 1;
        when(playerCareerService.refreshPlayerCareerStatsByGameType(playerId, gameType))
                .thenReturn(true);

        // When
        CommonResult<Boolean> result = playerCareerController
                .refreshPlayerCareerStatsByGameType(playerId, gameType);

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertTrue(result.getData());
        
        verify(playerCareerService).refreshPlayerCareerStatsByGameType(playerId, gameType);
    }

    @Test
    void testGetPlayerAbilityTrend_Success() {
        // Given
        Long playerId = 1L;
        List<AbilityTrendVO> trendList = Arrays.asList(mockAbilityTrendVO);
        when(playerCareerService.getPlayerAbilityTrend(playerId)).thenReturn(trendList);

        // When
        CommonResult<List<AbilityTrendVO>> result = playerCareerController
                .getPlayerAbilityTrend(playerId);

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(1, result.getData().size());
        assertEquals(mockAbilityTrendVO, result.getData().get(0));
        
        verify(playerCareerService).getPlayerAbilityTrend(playerId);
    }

    @Test
    void testGetPlayerAbilityTrend_EmptyResult() {
        // Given
        Long playerId = 1L;
        when(playerCareerService.getPlayerAbilityTrend(playerId))
                .thenReturn(Collections.emptyList());

        // When
        CommonResult<List<AbilityTrendVO>> result = playerCareerController
                .getPlayerAbilityTrend(playerId);

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertTrue(result.getData().isEmpty());
        
        verify(playerCareerService).getPlayerAbilityTrend(playerId);
    }

    @Test
    void testRefreshAllPlayersCareerStats_Success() {
        // Given
        when(playerCareerService.refreshAllPlayersCareerStats()).thenReturn(true);

        // When
        CommonResult<Boolean> result = playerCareerController.refreshAllPlayersCareerStats();

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertTrue(result.getData());
        
        verify(playerCareerService).refreshAllPlayersCareerStats();
    }

    @Test
    void testBatchInitializeCareerStats_Success() {
        // Given
        List<Long> playerIds = Arrays.asList(1L, 2L, 3L);
        when(playerCareerStatsService.batchInitializeCareerStats(playerIds))
                .thenReturn(mockInitializationResult);

        // When
        CommonResult<InitializationResult> result = playerCareerController
                .batchInitializeCareerStats(playerIds);

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(mockInitializationResult, result.getData());
        assertEquals(3, result.getData().getSuccessCount());
        
        verify(playerCareerStatsService).batchInitializeCareerStats(playerIds);
        verify(radarChartCacheService).evictAllCache();
    }

    @Test
    void testBatchInitializeCareerStats_WithEmptyList() {
        // Given
        List<Long> emptyPlayerIds = Collections.emptyList();
        InitializationResult emptyResult = InitializationResult.success("无需处理", 0, 0);
        when(playerCareerStatsService.batchInitializeCareerStats(emptyPlayerIds))
                .thenReturn(emptyResult);

        // When
        CommonResult<InitializationResult> result = playerCareerController
                .batchInitializeCareerStats(emptyPlayerIds);

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0, result.getData().getSuccessCount());
        assertEquals(0, result.getData().getFailCount());
        
        verify(playerCareerStatsService).batchInitializeCareerStats(emptyPlayerIds);
        verify(radarChartCacheService).evictAllCache();
    }

    @Test
    void testForceRefreshPlayerCareerStats_Success() {
        // Given
        Long playerId = 1L;
        doNothing().when(playerCareerStatsService).forceRecalculateCareerStats(playerId);

        // When
        CommonResult<Boolean> result = playerCareerController
                .forceRefreshPlayerCareerStats(playerId);

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertTrue(result.getData());
        
        verify(playerCareerStatsService).forceRecalculateCareerStats(playerId);
    }

    @Test
    void testForceRefreshPlayerCareerStats_WithException() {
        // Given
        Long playerId = 1L;
        doThrow(new RuntimeException("Force refresh error"))
                .when(playerCareerStatsService).forceRecalculateCareerStats(playerId);

        // When
        CommonResult<Boolean> result = playerCareerController
                .forceRefreshPlayerCareerStats(playerId);

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess()); // CommonResult本身成功
        assertFalse(result.getData()); // 但业务结果失败
        
        verify(playerCareerStatsService).forceRecalculateCareerStats(playerId);
    }

    @Test
    void testSyncAggregatedData_Success() {
        // When
        CommonResult<Boolean> result = playerCareerController.syncAggregatedData();

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertTrue(result.getData());
    }

    @Test
    void testClearPlayerRadarChartCache_Success() {
        // Given
        Long playerId = 1L;
        doNothing().when(radarChartCacheService).evictPlayerRadarChartCache(playerId);

        // When
        CommonResult<Boolean> result = playerCareerController
                .clearPlayerRadarChartCache(playerId);

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertTrue(result.getData());
        
        verify(radarChartCacheService).evictPlayerRadarChartCache(playerId);
    }

    @Test
    void testClearPlayerRadarChartCache_WithException() {
        // Given
        Long playerId = 1L;
        doThrow(new RuntimeException("Cache error"))
                .when(radarChartCacheService).evictPlayerRadarChartCache(playerId);

        // When
        CommonResult<Boolean> result = playerCareerController
                .clearPlayerRadarChartCache(playerId);

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertFalse(result.getData());
        
        verify(radarChartCacheService).evictPlayerRadarChartCache(playerId);
    }

    @Test
    void testClearLeagueBestStatsCache_Success() {
        // Given
        doNothing().when(radarChartCacheService).evictLeagueBestStatsCache();

        // When
        CommonResult<Boolean> result = playerCareerController.clearLeagueBestStatsCache();

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertTrue(result.getData());
        
        verify(radarChartCacheService).evictLeagueBestStatsCache();
    }

    @Test
    void testClearAllRadarChartCache_Success() {
        // Given
        doNothing().when(radarChartCacheService).evictAllCache();

        // When
        CommonResult<Boolean> result = playerCareerController.clearAllRadarChartCache();

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertTrue(result.getData());
        
        verify(radarChartCacheService).evictAllCache();
    }

    @Test
    void testGetCacheStats_Success() {
        // Given
        String mockStats = "缓存统计: 命中率90%, 总请求100次";
        when(radarChartCacheService.getCacheStats()).thenReturn(mockStats);

        // When
        CommonResult<String> result = playerCareerController.getCacheStats();

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(mockStats, result.getData());
        
        verify(radarChartCacheService).getCacheStats();
    }

    @Test
    void testGetCacheStats_WithException() {
        // Given
        when(radarChartCacheService.getCacheStats())
                .thenThrow(new RuntimeException("Stats error"));

        // When
        CommonResult<String> result = playerCareerController.getCacheStats();

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals("获取缓存统计信息失败", result.getData());
        
        verify(radarChartCacheService).getCacheStats();
    }

    @Test
    void testGetPlayerCareerInternal_Success() {
        // Given
        Long playerId = 1L;
        when(playerCareerService.getPlayerCareer(playerId)).thenReturn(mockPlayerCareerVO);

        // When
        CommonResult<PlayerCareerVO> result = playerCareerController
                .getPlayerCareerInternal(playerId);

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(mockPlayerCareerVO, result.getData());
        
        verify(playerCareerService).getPlayerCareer(playerId);
    }

    @Test
    void testRefreshPlayerCareerStatsInternal_Success() {
        // Given
        Long playerId = 1L;
        when(playerCareerService.refreshPlayerCareerStats(playerId)).thenReturn(true);

        // When
        CommonResult<Boolean> result = playerCareerController
                .refreshPlayerCareerStatsInternal(playerId);

        // Then
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertTrue(result.getData());
        
        verify(playerCareerService).refreshPlayerCareerStats(playerId);
    }

    // Helper methods to create mock data
    private PlayerCareerVO createMockPlayerCareerVO() {
        PlayerCareerVO vo = new PlayerCareerVO();
        vo.setPlayerId(1L);
        vo.setPlayerName("测试球员");
        vo.setGamesPlayed(10);
        vo.setAvgPoints(BigDecimal.valueOf(20.5));
        vo.setAvgRebounds(BigDecimal.valueOf(8.2));
        vo.setAvgAssists(BigDecimal.valueOf(4.1));
        vo.setFieldGoalPercentage(BigDecimal.valueOf(48.5));
        vo.setThreePointPercentage(BigDecimal.valueOf(36.8));
        vo.setFreeThrowPercentage(BigDecimal.valueOf(82.3));
        vo.setWins(7);
        vo.setLosses(3);
        vo.setWinRate(BigDecimal.valueOf(70.0));
        vo.setFirstGameDate(LocalDate.now().minusDays(30));
        vo.setLatestGameDate(LocalDate.now());
        return vo;
    }

    private AbilityTrendVO createMockAbilityTrendVO() {
        AbilityTrendVO vo = new AbilityTrendVO();
        vo.setDate(LocalDate.now());
        vo.setOffensiveAbility(BigDecimal.valueOf(85.5));
        vo.setDefensiveAbility(BigDecimal.valueOf(78.2));
        vo.setPhysicalAbility(BigDecimal.valueOf(82.1));
        vo.setTechnicalAbility(BigDecimal.valueOf(88.7));
        vo.setMentalAbility(BigDecimal.valueOf(75.3));
        vo.setOverallRating(BigDecimal.valueOf(81.9));
        return vo;
    }

    private InitializationResult createMockInitializationResult() {
        return InitializationResult.success("批量初始化完成", 3, 0);
    }

    private void injectMockDependencies() {
        try {
            // 使用反射注入mock对象
            java.lang.reflect.Field playerCareerServiceField = 
                PlayerCareerController.class.getDeclaredField("playerCareerService");
            playerCareerServiceField.setAccessible(true);
            playerCareerServiceField.set(playerCareerController, playerCareerService);

            java.lang.reflect.Field playerCareerStatsServiceField = 
                PlayerCareerController.class.getDeclaredField("playerCareerStatsService");
            playerCareerStatsServiceField.setAccessible(true);
            playerCareerStatsServiceField.set(playerCareerController, playerCareerStatsService);

            java.lang.reflect.Field radarChartCacheServiceField = 
                PlayerCareerController.class.getDeclaredField("radarChartCacheService");
            radarChartCacheServiceField.setAccessible(true);
            radarChartCacheServiceField.set(playerCareerController, radarChartCacheService);
        } catch (Exception e) {
            throw new RuntimeException("Failed to inject mock dependencies", e);
        }
    }
}