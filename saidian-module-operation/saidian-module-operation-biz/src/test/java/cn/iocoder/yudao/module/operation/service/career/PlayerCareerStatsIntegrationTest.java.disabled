package cn.iocoder.yudao.module.operation.service.career;

import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerCareerStatsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerStatisticsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerGameRelatedDO;
import cn.iocoder.yudao.module.operation.dal.mysql.player.PlayerCareerStatsMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.game.PlayerStatisticsMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.game.PlayerGameRelatedMapper;
import cn.iocoder.yudao.module.operation.service.career.dto.InitializationResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 球员生涯统计模块集成测试
 * 验证从比赛数据到生涯统计的完整流程
 * 
 * <AUTHOR> Assistant
 */
@Import({
    PlayerCareerStatsService.class,
    PlayerCareerStatsCalculator.class,
    PlayerStatsCalculatorCommon.class,
    PlayerCareerDataInitializerV2.class,
    PlayerCareerStatsMapper.class,
    PlayerStatisticsMapper.class,
    PlayerGameRelatedMapper.class
})
@Transactional
class PlayerCareerStatsIntegrationTest extends BaseDbUnitTest {

    @Resource
    private PlayerCareerStatsService playerCareerStatsService;
    
    @Resource
    private PlayerCareerStatsMapper playerCareerStatsMapper;
    
    @Resource
    private PlayerStatisticsMapper playerStatisticsMapper;
    
    @Resource
    private PlayerGameRelatedMapper playerGameRelatedMapper;

    private Long testPlayerId = 1001L;
    private Integer testGameType = 0;

    @BeforeEach
    void setUp() {
        // 清理测试数据
        cleanTestData();
        
        // 准备测试数据
        prepareTestGameData();
    }

    @Test
    void testCompleteCareerStatsFlow_FromGameDataToCareerStats() {
        // When: 计算球员生涯统计
        PlayerCareerStatsDO careerStats = playerCareerStatsService
                .calculateCareerStats(testPlayerId, testGameType);

        // Then: 验证生涯统计数据正确性
        assertNotNull(careerStats);
        assertEquals(testPlayerId, careerStats.getPlayerId());
        assertEquals(testGameType, careerStats.getGameType());
        
        // 验证基础统计数据
        assertEquals(3, careerStats.getGamesPlayed()); // 3场比赛
        assertEquals(75, careerStats.getTotalPoints()); // 25+30+20=75
        assertEquals(0, BigDecimal.valueOf(25.0).compareTo(careerStats.getAvgPoints())); // 75/3=25.0
        
        assertEquals(18, careerStats.getTotalRebounds()); // 6+8+4=18
        assertEquals(0, BigDecimal.valueOf(6.0).compareTo(careerStats.getAvgRebounds())); // 18/3=6.0
        
        assertEquals(12, careerStats.getTotalAssists()); // 4+5+3=12
        assertEquals(0, BigDecimal.valueOf(4.0).compareTo(careerStats.getAvgAssists())); // 12/3=4.0
        
        // 验证胜负记录
        assertEquals(2, careerStats.getWins()); // 2胜
        assertEquals(1, careerStats.getLosses()); // 1负
        assertEquals(0, BigDecimal.valueOf(66.67).compareTo(careerStats.getWinRate())); // 胜率约66.67%
        
        // 验证数据已保存到聚合表
        PlayerCareerStatsDO savedStats = playerCareerStatsMapper.selectById(careerStats.getId());
        assertNotNull(savedStats);
        assertEquals(careerStats.getPlayerId(), savedStats.getPlayerId());
        assertEquals(careerStats.getTotalPoints(), savedStats.getTotalPoints());
    }

    @Test
    void testCareerStatsUpdate_WhenNewGameAdded() {
        // Given: 先计算初始生涯统计
        PlayerCareerStatsDO initialStats = playerCareerStatsService
                .calculateCareerStats(testPlayerId, testGameType);
        
        assertEquals(3, initialStats.getGamesPlayed());
        assertEquals(75, initialStats.getTotalPoints());
        
        // When: 添加新的比赛数据
        addNewGameData(testPlayerId, 104L, 28, 7, 6, true);
        
        // 更新球员生涯统计
        playerCareerStatsService.updatePlayerCareerStats(testPlayerId, testGameType);

        // Then: 验证统计数据已更新
        PlayerCareerStatsDO updatedStats = playerCareerStatsService
                .calculateCareerStats(testPlayerId, testGameType);
        
        assertEquals(4, updatedStats.getGamesPlayed()); // 增加到4场
        assertEquals(103, updatedStats.getTotalPoints()); // 75+28=103
        assertEquals(0, BigDecimal.valueOf(25.75).compareTo(updatedStats.getAvgPoints())); // 103/4=25.75
        
        assertEquals(25, updatedStats.getTotalRebounds()); // 18+7=25
        assertEquals(18, updatedStats.getTotalAssists()); // 12+6=18
        
        // 验证胜负记录
        assertEquals(3, updatedStats.getWins()); // 增加到3胜
        assertEquals(1, updatedStats.getLosses()); // 仍然1负
    }

    @Test
    void testBatchInitializeCareerStats_MultiplePlayersSuccess() {
        // Given: 准备多个球员的比赛数据
        Long player2Id = 1002L;
        Long player3Id = 1003L;
        
        prepareTestGameDataForPlayer(player2Id, Arrays.asList(
                createGameStats(player2Id, 201L, 15, 8, 2),
                createGameStats(player2Id, 202L, 22, 6, 4)
        ), Arrays.asList(
                createGameResult(player2Id, 201L, true),
                createGameResult(player2Id, 202L, false)
        ));
        
        prepareTestGameDataForPlayer(player3Id, Arrays.asList(
                createGameStats(player3Id, 301L, 18, 5, 7)
        ), Arrays.asList(
                createGameResult(player3Id, 301L, true)
        ));

        List<Long> playerIds = Arrays.asList(testPlayerId, player2Id, player3Id);

        // When: 批量初始化生涯数据
        InitializationResult result = playerCareerStatsService
                .batchInitializeCareerStats(playerIds);

        // Then: 验证初始化结果
        assertNotNull(result);
        assertEquals(3, result.getSuccessCount());
        assertEquals(0, result.getFailCount());
        assertTrue(result.getMessage().contains("成功"));

        // 验证每个球员的生涯数据都已正确创建
        PlayerCareerStatsDO player1Stats = playerCareerStatsService
                .calculateCareerStats(testPlayerId, testGameType);
        assertEquals(3, player1Stats.getGamesPlayed());
        assertEquals(75, player1Stats.getTotalPoints());

        PlayerCareerStatsDO player2Stats = playerCareerStatsService
                .calculateCareerStats(player2Id, testGameType);
        assertEquals(2, player2Stats.getGamesPlayed());
        assertEquals(37, player2Stats.getTotalPoints()); // 15+22=37

        PlayerCareerStatsDO player3Stats = playerCareerStatsService
                .calculateCareerStats(player3Id, testGameType);
        assertEquals(1, player3Stats.getGamesPlayed());
        assertEquals(18, player3Stats.getTotalPoints());
    }

    @Test
    void testGetAllValidCareerStats_FilteringAndSorting() {
        // Given: 准备多个球员数据，包括不符合条件的
        Long highScorerPlayerId = 2001L;
        Long lowGameCountPlayerId = 2002L;
        
        // 高分球员，符合条件
        prepareTestGameDataForPlayer(highScorerPlayerId, 
                createMultipleGameStats(highScorerPlayerId, 8, 30), // 8场比赛，场均30分
                createMultipleGameResults(highScorerPlayerId, 8, 6)); // 6胜2负
        
        // 比赛场次少的球员，不符合条件
        prepareTestGameDataForPlayer(lowGameCountPlayerId,
                createMultipleGameStats(lowGameCountPlayerId, 3, 35), // 仅3场比赛
                createMultipleGameResults(lowGameCountPlayerId, 3, 3)); // 3胜0负
        
        // 初始化所有球员数据
        playerCareerStatsService.updatePlayerCareerStats(testPlayerId, testGameType);
        playerCareerStatsService.updatePlayerCareerStats(highScorerPlayerId, testGameType);
        playerCareerStatsService.updatePlayerCareerStats(lowGameCountPlayerId, testGameType);

        // When: 获取所有有效的生涯统计数据
        List<PlayerCareerStatsDO> validStats = playerCareerStatsService
                .getAllValidCareerStats(testGameType);

        // Then: 验证过滤和排序结果
        assertNotNull(validStats);
        assertEquals(2, validStats.size()); // 只有2个球员符合条件（至少5场比赛）
        
        // 验证按场均得分倒序排列
        assertTrue(validStats.get(0).getAvgPoints()
                .compareTo(validStats.get(1).getAvgPoints()) >= 0);
        
        // 验证所有记录都符合条件
        for (PlayerCareerStatsDO stats : validStats) {
            assertTrue(stats.getGamesPlayed() >= 5);
            assertEquals(testGameType, stats.getGameType());
            assertFalse(stats.getDeleted());
        }
    }

    @Test
    void testForceRecalculateCareerStats_AllGameTypes() {
        // Given: 确保球员已有生涯数据
        playerCareerStatsService.updatePlayerCareerStats(testPlayerId, 0);
        playerCareerStatsService.updatePlayerCareerStats(testPlayerId, 1);

        // When: 强制重新计算所有比赛类型的生涯数据
        playerCareerStatsService.forceRecalculateCareerStats(testPlayerId);

        // Then: 验证所有比赛类型的数据都已更新
        for (int gameType = 0; gameType <= 3; gameType++) {
            PlayerCareerStatsDO stats = playerCareerStatsService
                    .calculateCareerStats(testPlayerId, gameType);
            assertNotNull(stats);
            assertEquals(testPlayerId, stats.getPlayerId());
            assertEquals(gameType, stats.getGameType());
        }
    }

    @Test
    void testCareerStatsFlow_WithEmptyGameData() {
        // Given: 一个没有比赛数据的球员
        Long emptyPlayerId = 9999L;

        // When: 计算生涯统计
        PlayerCareerStatsDO emptyStats = playerCareerStatsService
                .calculateCareerStats(emptyPlayerId, testGameType);

        // Then: 验证返回空的统计数据
        assertNotNull(emptyStats);
        assertEquals(emptyPlayerId, emptyStats.getPlayerId());
        assertEquals(testGameType, emptyStats.getGameType());
        assertEquals(0, emptyStats.getGamesPlayed());
        assertEquals(0, emptyStats.getTotalPoints());
        assertNull(emptyStats.getAvgPoints());
    }

    // ==================== Helper Methods ====================

    private void cleanTestData() {
        // 清理测试相关的数据
        playerCareerStatsMapper.deleteAll();
    }

    private void prepareTestGameData() {
        prepareTestGameDataForPlayer(testPlayerId, 
                createTestGameStatsList(), 
                createTestGameResultsList());
    }

    private void prepareTestGameDataForPlayer(Long playerId, 
                                              List<PlayerStatisticsDO> gameStats, 
                                              List<PlayerGameRelatedDO> gameResults) {
        // 插入比赛统计数据
        for (PlayerStatisticsDO stats : gameStats) {
            stats.setPlayerId(playerId);
            playerStatisticsMapper.insert(stats);
        }
        
        // 插入比赛结果数据
        for (PlayerGameRelatedDO result : gameResults) {
            result.setPlayerId(playerId);
            playerGameRelatedMapper.insert(result);
        }
    }

    private List<PlayerStatisticsDO> createTestGameStatsList() {
        return Arrays.asList(
                createGameStats(testPlayerId, 101L, 25, 6, 4), // 第1场
                createGameStats(testPlayerId, 102L, 30, 8, 5), // 第2场
                createGameStats(testPlayerId, 103L, 20, 4, 3)  // 第3场
        );
    }

    private List<PlayerGameRelatedDO> createTestGameResultsList() {
        return Arrays.asList(
                createGameResult(testPlayerId, 101L, true),  // 胜
                createGameResult(testPlayerId, 102L, true),  // 胜
                createGameResult(testPlayerId, 103L, false)  // 负
        );
    }

    private PlayerStatisticsDO createGameStats(Long playerId, Long gameId, 
                                               int points, int rebounds, int assists) {
        PlayerStatisticsDO stats = new PlayerStatisticsDO();
        stats.setPlayerId(playerId);
        stats.setGameId(gameId);
        stats.setSection(0); // 第0节表示全场汇总
        stats.setPoints(points);
        stats.setRebounds(rebounds);
        stats.setOffensiveRebounds(rebounds / 3);
        stats.setDefensiveRebounds(rebounds * 2 / 3);
        stats.setAssists(assists);
        stats.setSteals(1);
        stats.setBlocks(1);
        stats.setTurnovers(2);
        stats.setFouls(3);
        stats.setFieldGoalsMade(points / 3);
        stats.setFieldGoalsAttempted(points / 2);
        stats.setThreePointsMade(2);
        stats.setThreePointsAttempted(6);
        stats.setTwoPointsMade(points / 3 - 2);
        stats.setTwoPointsAttempted(points / 2 - 6);
        stats.setFreeThrowsMade(3);
        stats.setFreeThrowsAttempted(4);
        stats.setMinutesPlayed(BigDecimal.valueOf(32.0));
        stats.setCreateTime(LocalDateTime.now());
        stats.setDeleted(false);
        return stats;
    }

    private PlayerGameRelatedDO createGameResult(Long playerId, Long gameId, boolean isWin) {
        PlayerGameRelatedDO result = new PlayerGameRelatedDO();
        result.setPlayerId(playerId);
        result.setGameId(gameId);
        result.setIsWin(isWin);
        result.setCreateTime(LocalDateTime.now());
        result.setDeleted(false);
        return result;
    }

    private void addNewGameData(Long playerId, Long gameId, 
                                int points, int rebounds, int assists, boolean isWin) {
        PlayerStatisticsDO newStats = createGameStats(playerId, gameId, points, rebounds, assists);
        playerStatisticsMapper.insert(newStats);
        
        PlayerGameRelatedDO newResult = createGameResult(playerId, gameId, isWin);
        playerGameRelatedMapper.insert(newResult);
    }

    private List<PlayerStatisticsDO> createMultipleGameStats(Long playerId, int gameCount, int avgPoints) {
        List<PlayerStatisticsDO> statsList = new java.util.ArrayList<>();
        for (int i = 0; i < gameCount; i++) {
            long gameId = 3000L + i;
            int points = avgPoints + (i % 3 - 1) * 5; // 稍微变化分数
            int rebounds = 5 + (i % 2);
            int assists = 3 + (i % 3);
            statsList.add(createGameStats(playerId, gameId, points, rebounds, assists));
        }
        return statsList;
    }

    private List<PlayerGameRelatedDO> createMultipleGameResults(Long playerId, int gameCount, int winCount) {
        List<PlayerGameRelatedDO> resultsList = new java.util.ArrayList<>();
        for (int i = 0; i < gameCount; i++) {
            long gameId = 3000L + i;
            boolean isWin = i < winCount;
            resultsList.add(createGameResult(playerId, gameId, isWin));
        }
        return resultsList;
    }
}