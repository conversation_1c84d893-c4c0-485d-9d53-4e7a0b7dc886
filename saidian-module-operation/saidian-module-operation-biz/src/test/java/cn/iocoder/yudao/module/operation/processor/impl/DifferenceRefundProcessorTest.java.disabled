package cn.iocoder.yudao.module.operation.processor.impl;

import cn.iocoder.yudao.framework.test.core.ut.BaseMockitoUnitTest;
import cn.iocoder.yudao.module.operation.dal.dataobject.activity.ActivityDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.registration.RegistrationDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.registration.RegistrationLogDO;
import cn.iocoder.yudao.module.operation.dal.mysql.activity.ActivityMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.registration.RegistrationLogMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.registration.RegistrationMapper;
import cn.iocoder.yudao.module.operation.enums.activity.ActivityTypeEnum;
import cn.iocoder.yudao.module.operation.enums.activity.FriendlyPaymentTypeEnum;
import cn.iocoder.yudao.module.operation.enums.refund.RefundScenario;
import cn.iocoder.yudao.module.operation.enums.registration.RegistrationLogTypeEnum;
import cn.iocoder.yudao.module.operation.enums.registration.RegistrationStatusEnum;
import cn.iocoder.yudao.module.operation.service.refund.calculator.RefundCalculator;
import cn.iocoder.yudao.module.operation.service.refund.calculator.RefundCalculatorFactory;
import cn.iocoder.yudao.module.pay.api.refund.PayRefundApi;
import cn.iocoder.yudao.module.pay.api.refund.dto.PayRefundCreateReqDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * {@link DifferenceRefundProcessor} 的单元测试
 */
public class DifferenceRefundProcessorTest extends BaseMockitoUnitTest {

    @InjectMocks
    private DifferenceRefundProcessor differenceRefundProcessor;
    
    @Mock
    private ActivityMapper activityMapper;
    
    @Mock
    private RegistrationMapper registrationMapper;
    
    @Mock
    private RegistrationLogMapper registrationLogMapper;
    
    @Mock
    private RefundCalculatorFactory refundCalculatorFactory;
    
    @Mock
    private PayRefundApi payRefundApi;
    
    @Mock
    private TransactionTemplate transactionTemplate;
    
    private ActivityDO activity;
    private RegistrationDO registration;
    
    @BeforeEach
    public void setUp() {
        // 初始化测试数据
        activity = mock(ActivityDO.class);
        when(activity.getId()).thenReturn(1L);
        when(activity.getType()).thenReturn(ActivityTypeEnum.FRIENDLY.getType());
        
        registration = mock(RegistrationDO.class);
        when(registration.getId()).thenReturn(1L);
        when(registration.getActivityId()).thenReturn(1L);
        when(registration.getTeamId()).thenReturn(123L);
        when(registration.getStatus()).thenReturn(RegistrationStatusEnum.SUCCESSFUL.getStatus());
        when(registration.getPaymentType()).thenReturn(FriendlyPaymentTypeEnum.TEAM_AA.getType());
        when(registration.getPayOrderId()).thenReturn(999L);
        when(registration.getShouldPayPrice()).thenReturn(1000);
        when(registration.getActualPayPrice()).thenReturn(1000);
        
        // 模拟TransactionTemplate的行为
        doAnswer(invocation -> {
            TransactionCallback<?> callback = invocation.getArgument(0);
            return callback.doInTransaction(mock(TransactionStatus.class));
        }).when(transactionTemplate).execute(any());
    }
    
    @Test
    public void testDoValidateRefund() {
        // 差额退款处理器不会抛出异常
        assertDoesNotThrow(() -> {
            differenceRefundProcessor.doValidateRefund(registration, 0L);
        });
    }
    
    @Test
    public void testGetActivity() {
        // 模拟ActivityMapper的行为
        when(activityMapper.selectById(eq(1L))).thenReturn(activity);
        
        // 验证getActivity方法
        ActivityDO result = differenceRefundProcessor.getActivity(1L);
        assertNotNull(result);
        assertEquals(1L, result.getId());
        
        // 验证调用
        verify(activityMapper).selectById(eq(1L));
    }
    
    @Test
    public void testCalculateAmount_WithCalculator() {
        // 模拟RefundCalculatorFactory的行为
        RefundCalculator mockCalculator = mock(RefundCalculator.class);
        when(refundCalculatorFactory.getCalculator(eq(ActivityTypeEnum.FRIENDLY)))
                .thenReturn(mockCalculator);
        
        // 模拟计算器计算退款金额
        when(mockCalculator.calculateDifferenceRefundAmount(eq(activity), eq(registration), anyInt()))
                .thenReturn(500); // 假设退款5元
        
        // 验证计算方法
        Integer amount = differenceRefundProcessor.calculateAmount(activity, registration);
        assertEquals(500, amount);
        
        // 验证调用
        verify(refundCalculatorFactory).getCalculator(eq(ActivityTypeEnum.FRIENDLY));
        verify(mockCalculator).calculateDifferenceRefundAmount(eq(activity), eq(registration), anyInt());
    }
    
    @Test
    public void testCalculateAmount_NoCalculator() {
        // 模拟RefundCalculatorFactory的行为 - 返回null表示没找到计算器
        when(refundCalculatorFactory.getCalculator(eq(ActivityTypeEnum.FRIENDLY)))
                .thenReturn(null);
        
        // 验证计算方法 - 没有计算器应该返回0
        Integer amount = differenceRefundProcessor.calculateAmount(activity, registration);
        assertEquals(0, amount);
        
        // 验证调用
        verify(refundCalculatorFactory).getCalculator(eq(ActivityTypeEnum.FRIENDLY));
    }
    
    @Test
    public void testGetRefundReason() {
        // 验证退款原因
        String reason = differenceRefundProcessor.getRefundReason();
        assertEquals("活动报名差额退款", reason);
    }
    
    @Test
    public void testGetSupportedScenario() {
        // 验证支持的场景
        RefundScenario scenario = differenceRefundProcessor.getSupportedScenario();
        assertEquals(RefundScenario.DIFFERENCE, scenario);
    }
    
    @Test
    public void testGetLogType() {
        // 验证日志类型
        RegistrationLogTypeEnum logType = differenceRefundProcessor.getLogType();
        assertEquals(RegistrationLogTypeEnum.DIFFERENCE_REFUND, logType);
    }
    
    @Test
    public void testProcessRefund() {
        // 模拟RegistrationMapper的行为
        when(registrationMapper.selectById(eq(1L))).thenReturn(registration);
        
        // 模拟ActivityMapper的行为
        when(activityMapper.selectById(eq(1L))).thenReturn(activity);
        
        // 模拟RefundCalculatorFactory的行为
        RefundCalculator mockCalculator = mock(RefundCalculator.class);
        when(refundCalculatorFactory.getCalculator(eq(ActivityTypeEnum.FRIENDLY)))
                .thenReturn(mockCalculator);
        
        // 模拟计算器计算退款金额
        when(mockCalculator.calculateDifferenceRefundAmount(eq(activity), eq(registration), anyInt()))
                .thenReturn(500); // 假设退款5元
        
        // 模拟支付API
        PayRefundCreateReqDTO reqDTO = mock(PayRefundCreateReqDTO.class);
        when(reqDTO.getPrice()).thenReturn(500);
        doAnswer(invocation -> {
            PayRefundCreateReqDTO dto = invocation.getArgument(0);
            // 验证调用参数
            assertEquals(500, dto.getPrice());
            return 888L;
        }).when(payRefundApi).createRefund(any(PayRefundCreateReqDTO.class));
        
        // 执行退款处理
        Long operatorId = 0L; // 系统操作
        boolean result = differenceRefundProcessor.processRefund(1L, operatorId);
        
        // 验证结果
        assertTrue(result);
        
        // 验证支付API调用
        verify(payRefundApi).createRefund(any(PayRefundCreateReqDTO.class));
        
        // 验证数据库更新
        verify(registrationMapper).updateById(any(RegistrationDO.class));
        verify(registrationLogMapper).insert(any(RegistrationLogDO.class));
    }
} 