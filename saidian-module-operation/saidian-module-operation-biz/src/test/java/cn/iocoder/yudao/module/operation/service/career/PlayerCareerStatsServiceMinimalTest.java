package cn.iocoder.yudao.module.operation.service.career;

import cn.iocoder.yudao.framework.test.core.ut.BaseMockitoUnitTest;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerCareerStatsDO;
import cn.iocoder.yudao.module.operation.dal.mysql.player.PlayerCareerStatsMapper;
import cn.iocoder.yudao.module.operation.service.career.dto.InitializationResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 球员生涯统计服务最简测试版本
 * 
 * <AUTHOR> Assistant
 */
class PlayerCareerStatsServiceMinimalTest extends BaseMockitoUnitTest {

    @Mock
    private PlayerCareerStatsMapper playerCareerStatsMapper;

    @InjectMocks
    private PlayerCareerStatsService playerCareerStatsService;

    private PlayerCareerStatsDO mockCareerStats;

    @BeforeEach
    void setUp() {
        mockCareerStats = createMockCareerStats();
    }

    @Test
    void testCalculateCareerStats_WithExistingData() {
        // Given
        Long playerId = 1L;
        Integer gameType = 0;
        
        when(playerCareerStatsMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(mockCareerStats);

        // When
        PlayerCareerStatsDO result = playerCareerStatsService.calculateCareerStats(playerId, gameType);

        // Then
        assertNotNull(result);
        assertEquals(playerId, result.getPlayerId());
        assertEquals(gameType, result.getGameType());
        
        verify(playerCareerStatsMapper).selectOne(any(LambdaQueryWrapper.class));
    }

    @Test
    void testCalculateCareerStats_WithNullPlayerId() {
        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> playerCareerStatsService.calculateCareerStats(null, 0)
        );
        assertEquals("球员ID不能为空", exception.getMessage());
    }

    @Test
    void testGetAllValidCareerStats() {
        // Given
        Integer gameType = 0;
        List<PlayerCareerStatsDO> mockStatsList = Arrays.asList(mockCareerStats);
        
        when(playerCareerStatsMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(mockStatsList);

        // When
        List<PlayerCareerStatsDO> result = playerCareerStatsService.getAllValidCareerStats(gameType);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(mockCareerStats.getPlayerId(), result.get(0).getPlayerId());
        
        verify(playerCareerStatsMapper).selectList(any(LambdaQueryWrapper.class));
    }

    @Test
    void testBatchInitializeCareerStats_WithEmptyPlayerIds() {
        // When
        InitializationResult result = playerCareerStatsService.batchInitializeCareerStats(Collections.emptyList());

        // Then
        assertNotNull(result);
        assertEquals(0, result.getSuccessCount());
        assertEquals(0, result.getFailCount());
        assertTrue(result.getMessage().contains("无需处理"));
    }

    @Test
    void testBatchInitializeCareerStats_WithNullPlayerIds() {
        // When
        InitializationResult result = playerCareerStatsService.batchInitializeCareerStats(null);

        // Then
        assertNotNull(result);
        assertEquals(0, result.getSuccessCount());
        assertEquals(0, result.getFailCount());
    }

    // Helper method to create mock data with correct field names
    private PlayerCareerStatsDO createMockCareerStats() {
        return PlayerCareerStatsDO.builder()
                .id(1L)
                .playerId(1L)
                .gameType(0)
                .gamesPlayed(10)
                .totalPoints(200)
                .avgPoints(BigDecimal.valueOf(20.0))
                .totalRebounds(50)
                .avgRebounds(BigDecimal.valueOf(5.0))
                .totalAssists(30)
                .avgAssists(BigDecimal.valueOf(3.0))
                .firstGameDate(LocalDate.now().minusDays(30))
                .latestGameDate(LocalDate.now())
                .build();
    }
}