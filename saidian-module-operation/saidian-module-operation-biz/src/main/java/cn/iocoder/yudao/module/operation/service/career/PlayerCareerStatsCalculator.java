package cn.iocoder.yudao.module.operation.service.career;

import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerCareerStatsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerStatisticsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerGameRelatedDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.List;

/**
 * 球员生涯统计计算器
 * 
 * 负责从比赛统计数据聚合计算生涯数据
 * 使用通用计算组件避免代码重复
 * 
 * TDD实现：先测试后实现，确保代码质量
 * 
 * <AUTHOR> Assistant
 * @since 2024
 */
@Component
@Slf4j
public class PlayerCareerStatsCalculator {

    @Resource
    private PlayerStatsCalculatorCommon commonCalculator;

    /**
     * 从比赛统计数据计算生涯统计
     * 
     * @param playerId 球员ID
     * @param gameType 比赛类型
     * @param gameStats 比赛统计数据列表
     * @return 生涯统计数据
     */
    public PlayerCareerStatsDO calculateFromGameStats(Long playerId, Integer gameType, List<PlayerStatisticsDO> gameStats) {
        if (gameStats == null || gameStats.isEmpty()) {
            return createEmptyCareerStats(playerId, gameType);
        }

        // 使用通用计算器进行聚合计算
        PlayerStatsCalculatorCommon.StatsAggregationResult aggregationResult = null;
        
        try {
            if (commonCalculator != null) {
                aggregationResult = commonCalculator.aggregateStats(gameStats);
            }
        } catch (Exception e) {
            log.error("通用计算器聚合统计失败，playerId={}, gameType={}, error={}", playerId, gameType, e.getMessage(), e);
        }
        
        // 检查聚合结果，如果为null则创建默认结果
        if (aggregationResult == null) {
            log.warn("聚合计算结果为null，使用默认实现，playerId={}, gameType={}", playerId, gameType);
            aggregationResult = createDefaultAggregationResult(gameStats);
        }
        
        // 将聚合结果转换为生涯统计DO
        PlayerCareerStatsDO careerStats = convertToCareerStatsDO(playerId, gameType, aggregationResult);
        
        // 设置生涯特有字段
        setCareerSpecificFields(careerStats, gameStats);

        // 计算命中率
        calculateShootingPercentages(careerStats);
        
        // 计算场均数据
        calculateAverageStats(careerStats);
        
        // 计算高级统计
        calculateAdvancedStats(careerStats);
        
        // 计算胜率
        calculateWinRate(careerStats);
        
        // 验证计算结果
        validateCalculatedData(careerStats);

        log.info("📊 生涯统计计算完成: 球员={}, 比赛类型={}, 场次={}, 场均得分={}", 
                playerId, gameType, aggregationResult.getTotalGames(), aggregationResult.getAvgPoints());

        return careerStats;
    }

    /**
     * TDD 绿阶段：计算命中率数据
     * 
     * 🟢 绿阶段：实现让红阶段测试通过的最小代码
     * 
     * @param careerStats 生涯统计数据
     */
    public void calculateShootingPercentages(PlayerCareerStatsDO careerStats) {
        try {
            // 二分命中率
            if (careerStats.getTotalTwoPointsAttempted() != null && careerStats.getTotalTwoPointsAttempted() > 0) {
                BigDecimal twoPointPercentage = BigDecimal.valueOf(careerStats.getTotalTwoPointsMade())
                    .divide(BigDecimal.valueOf(careerStats.getTotalTwoPointsAttempted()), 4, RoundingMode.HALF_UP);
                careerStats.setTwoPointPercentage(twoPointPercentage);
            }

            // 三分命中率
            if (careerStats.getTotalThreePointsAttempted() != null && careerStats.getTotalThreePointsAttempted() > 0) {
                BigDecimal threePointPercentage = BigDecimal.valueOf(careerStats.getTotalThreePointsMade())
                    .divide(BigDecimal.valueOf(careerStats.getTotalThreePointsAttempted()), 4, RoundingMode.HALF_UP);
                careerStats.setThreePointPercentage(threePointPercentage);
            }

            // 罚球命中率
            if (careerStats.getTotalFreeThrowsAttempted() != null && careerStats.getTotalFreeThrowsAttempted() > 0) {
                BigDecimal freeThrowPercentage = BigDecimal.valueOf(careerStats.getTotalFreeThrowsMade())
                    .divide(BigDecimal.valueOf(careerStats.getTotalFreeThrowsAttempted()), 4, RoundingMode.HALF_UP);
                careerStats.setFreeThrowPercentage(freeThrowPercentage);
            }

            // 综合投篮命中率
            int totalMakes = safeGetInt(careerStats.getTotalTwoPointsMade()) + safeGetInt(careerStats.getTotalThreePointsMade());
            int totalAttempts = safeGetInt(careerStats.getTotalTwoPointsAttempted()) + safeGetInt(careerStats.getTotalThreePointsAttempted());
            
            if (totalAttempts > 0) {
                BigDecimal fieldGoalPercentage = BigDecimal.valueOf(totalMakes)
                    .divide(BigDecimal.valueOf(totalAttempts), 4, RoundingMode.HALF_UP);
                careerStats.setFieldGoalPercentage(fieldGoalPercentage);
            }
            
            log.debug("🟢 命中率计算完成: FG={}, 3PT={}, FT={}", 
                    careerStats.getFieldGoalPercentage(), 
                    careerStats.getThreePointPercentage(), 
                    careerStats.getFreeThrowPercentage());
                    
        } catch (Exception e) {
            log.error("命中率计算失败", e);
        }
    }

    /**
     * TDD 绿阶段：计算高级统计数据
     * 
     * 🟢 绿阶段：包括真实命中率(TS%)和球员效率值(PER)
     * 
     * @param careerStats 生涯统计数据
     */
    public void calculateAdvancedStats(PlayerCareerStatsDO careerStats) {
        try {
            // 真实命中率 (True Shooting Percentage)
            // 公式：总得分 / (2 * (投篮出手 + 0.44 * 罚球出手))
            int totalPoints = safeGetInt(careerStats.getTotalPoints());
            int totalFgAttempts = safeGetInt(careerStats.getTotalTwoPointsAttempted()) + safeGetInt(careerStats.getTotalThreePointsAttempted());
            int totalFtAttempts = safeGetInt(careerStats.getTotalFreeThrowsAttempted());
            
            if (totalFgAttempts > 0 || totalFtAttempts > 0) {
                double denominator = 2 * (totalFgAttempts + 0.44 * totalFtAttempts);
                if (denominator > 0) {
                    BigDecimal trueShootingPercentage = BigDecimal.valueOf(totalPoints)
                        .divide(BigDecimal.valueOf(denominator), 4, RoundingMode.HALF_UP);
                    careerStats.setTrueShootingPercentage(trueShootingPercentage);
                }
            }
            
            // 球员效率值 (Player Efficiency Rating)
            // 简化版PER计算：(得分 + 篮板 + 助攻 + 抢断 + 盖帽 - 失误 - 犯规) / 场次
            int totalGames = safeGetInt(careerStats.getGamesPlayed());
            if (totalGames > 0) {
                double per = (safeGetInt(careerStats.getTotalPoints()) + 
                             safeGetInt(careerStats.getTotalRebounds()) + 
                             safeGetInt(careerStats.getTotalAssists()) + 
                             safeGetInt(careerStats.getTotalSteals()) + 
                             safeGetInt(careerStats.getTotalBlocks()) - 
                             safeGetInt(careerStats.getTotalTurnovers()) - 
                             safeGetInt(careerStats.getTotalFouls())) / (double) totalGames;
                
                BigDecimal playerEfficiencyRating = BigDecimal.valueOf(Math.max(0, per))
                    .setScale(2, RoundingMode.HALF_UP);
                careerStats.setPlayerEfficiencyRating(playerEfficiencyRating);
            }
            
            log.debug("🟢 高级统计计算完成: TS%={}, PER={}", 
                    careerStats.getTrueShootingPercentage(), 
                    careerStats.getPlayerEfficiencyRating());
                    
        } catch (Exception e) {
            log.error("高级统计计算失败", e);
        }
    }

    /**
     * TDD 绿阶段：计算场均数据
     * 
     * 🟢 绿阶段：基础但重要的场均统计
     * 
     * @param careerStats 生涯统计数据
     */
    public void calculateAverageStats(PlayerCareerStatsDO careerStats) {
        try {
            int totalGames = safeGetInt(careerStats.getGamesPlayed());
            if (totalGames <= 0) {
                log.debug("无比赛场次，跳过场均数据计算");
                return;
            }
            
            // 计算各项场均数据
            careerStats.setAvgPoints(divide(careerStats.getTotalPoints(), totalGames, 1));
            careerStats.setAvgRebounds(divide(careerStats.getTotalRebounds(), totalGames, 1));
            careerStats.setAvgAssists(divide(careerStats.getTotalAssists(), totalGames, 1));
            careerStats.setAvgSteals(divide(careerStats.getTotalSteals(), totalGames, 1));
            careerStats.setAvgBlocks(divide(careerStats.getTotalBlocks(), totalGames, 1));
            careerStats.setAvgTurnovers(divide(careerStats.getTotalTurnovers(), totalGames, 1));
            careerStats.setAvgFouls(divide(careerStats.getTotalFouls(), totalGames, 1));
            
            log.debug("🟢 场均数据计算完成: 场均{}分{}板{}助攻", 
                    careerStats.getAvgPoints(), 
                    careerStats.getAvgRebounds(), 
                    careerStats.getAvgAssists());
                    
        } catch (Exception e) {
            log.error("场均数据计算失败", e);
        }
    }

    /**
     * TDD 绿阶段：计算胜率
     * 
     * 🟢 绿阶段：用户最关心的数据之一
     * 
     * @param careerStats 生涯统计数据
     */
    public void calculateWinRate(PlayerCareerStatsDO careerStats) {
        try {
            int totalWins = safeGetInt(careerStats.getTotalWins());
            int totalLosses = safeGetInt(careerStats.getTotalLosses());
            int totalGames = totalWins + totalLosses;
            
            if (totalGames > 0) {
                BigDecimal winRate = BigDecimal.valueOf(totalWins)
                    .divide(BigDecimal.valueOf(totalGames), 4, RoundingMode.HALF_UP);
                careerStats.setWinRate(winRate);
                
                log.debug("🟢 胜率计算完成: {}胜{}负，胜率{}%", 
                        totalWins, totalLosses, winRate.multiply(BigDecimal.valueOf(100)));
            }
            
        } catch (Exception e) {
            log.error("胜率计算失败", e);
        }
    }

    /**
     * TDD 绿阶段：验证计算数据的合理性
     * 
     * 🟢 绿阶段：确保计算结果符合逻辑
     * 
     * @param careerStats 生涯统计数据
     * @throws IllegalArgumentException 当数据不合理时
     */
    public void validateCalculatedData(PlayerCareerStatsDO careerStats) {
        // 验证命中数不能大于出手数
        if (safeGetInt(careerStats.getTotalTwoPointsMade()) > safeGetInt(careerStats.getTotalTwoPointsAttempted())) {
            throw new IllegalArgumentException("二分命中数不能大于出手数");
        }

        if (safeGetInt(careerStats.getTotalThreePointsMade()) > safeGetInt(careerStats.getTotalThreePointsAttempted())) {
            throw new IllegalArgumentException("三分命中数不能大于出手数");
        }

        if (safeGetInt(careerStats.getTotalFreeThrowsMade()) > safeGetInt(careerStats.getTotalFreeThrowsAttempted())) {
            throw new IllegalArgumentException("罚球命中数不能大于出手数");
        }
        
        // 验证胜率范围
        if (careerStats.getWinRate() != null && 
           (careerStats.getWinRate().compareTo(BigDecimal.ZERO) < 0 || 
            careerStats.getWinRate().compareTo(BigDecimal.ONE) > 0)) {
            throw new IllegalArgumentException("胜率必须在0-1之间");
        }
        
        // 验证命中率范围
        validatePercentageRange(careerStats.getFieldGoalPercentage(), "投篮命中率");
        validatePercentageRange(careerStats.getThreePointPercentage(), "三分命中率");
        validatePercentageRange(careerStats.getFreeThrowPercentage(), "罚球命中率");
        
        log.debug("🟢 数据验证通过");
    }

    /**
     * 计算连胜数据
     * 
     * @param careerStats 生涯统计数据
     * @param gameResults 比赛结果数据
     */
    public void calculateStreakData(PlayerCareerStatsDO careerStats, List<PlayerGameRelatedDO> gameResults) {
        // 使用通用计算器计算连胜数据
        PlayerStatsCalculatorCommon.StreakDataResult streakResult = null;
        
        try {
            if (commonCalculator != null) {
                streakResult = commonCalculator.calculateStreakData(gameResults);
            }
        } catch (Exception e) {
            log.error("通用计算器连胜数据计算失败，playerId={}, error={}", careerStats.getPlayerId(), e.getMessage(), e);
        }
        
        // 检查连胜数据结果，如果为null则创建默认结果
        if (streakResult == null) {
            log.warn("连胜数据计算结果为null，使用默认实现，playerId={}", careerStats.getPlayerId());
            streakResult = createDefaultStreakResult(gameResults);
        }
        
        // 将连胜结果设置到生涯统计中
        careerStats.setCurrentStreak(streakResult.getCurrentStreak());
        careerStats.setMaxWinStreak(streakResult.getMaxWinStreak());
        careerStats.setMaxLoseStreak(streakResult.getMaxLoseStreak());
        careerStats.setStreakStartDate(streakResult.getStreakStartDate());
        careerStats.setTotalWins(streakResult.getTotalWins());
        careerStats.setTotalLosses(streakResult.getTotalLosses());
        careerStats.setWinRate(streakResult.getWinRate());

        log.debug("生涯连胜数据计算: 当前连胜={}, 最大连胜={}, 胜率={}%", 
                streakResult.getCurrentStreak(), streakResult.getMaxWinStreak(), streakResult.getWinRate());
    }

    /**
     * 计算时间相关字段
     * 更新首场比赛日期、最近比赛日期等时间字段
     * 
     * @param careerStats 生涯统计数据
     * @param gameStats 比赛统计数据列表
     */
    public void calculateTimeRelatedFields(PlayerCareerStatsDO careerStats, List<PlayerStatisticsDO> gameStats) {
        if (gameStats == null || gameStats.isEmpty()) {
            log.debug("无比赛数据，跳过时间字段计算");
            return;
        }

        try {
            // 计算首场比赛日期（最早的比赛）
            LocalDate firstGameDate = gameStats.stream()
                    .filter(stat -> stat.getCreateTime() != null)
                    .map(stat -> stat.getCreateTime().toLocalDate())
                    .min(LocalDate::compareTo)
                    .orElse(LocalDate.now());

            // 计算最近比赛日期（最晚的比赛）
            LocalDate latestGameDate = gameStats.stream()
                    .filter(stat -> stat.getCreateTime() != null)
                    .map(stat -> stat.getCreateTime().toLocalDate())
                    .max(LocalDate::compareTo)
                    .orElse(LocalDate.now());

            // 更新时间字段
            careerStats.setFirstGameDate(firstGameDate);
            careerStats.setLatestGameDate(latestGameDate);

            log.debug("时间字段更新完成: 首场比赛={}, 最近比赛={}", firstGameDate, latestGameDate);

        } catch (Exception e) {
            log.error("计算时间相关字段失败", e);
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 将聚合结果转换为生涯统计DO
     */
    private PlayerCareerStatsDO convertToCareerStatsDO(Long playerId, Integer gameType, 
                                                       PlayerStatsCalculatorCommon.StatsAggregationResult result) {
        PlayerCareerStatsDO careerStats = new PlayerCareerStatsDO();
        careerStats.setPlayerId(playerId);
        careerStats.setGameType(gameType);

        // 设置总计数据
        careerStats.setGamesPlayed(result.getTotalGames());
        careerStats.setValidStatsGames(result.getValidGames());
        careerStats.setTotalPoints(result.getTotalPoints());
        careerStats.setTotalRebounds(result.getTotalRebounds());
        careerStats.setTotalOffensiveRebounds(result.getTotalOffensiveRebounds());
        careerStats.setTotalDefensiveRebounds(result.getTotalDefensiveRebounds());
        careerStats.setTotalAssists(result.getTotalAssists());
        careerStats.setTotalSteals(result.getTotalSteals());
        careerStats.setTotalBlocks(result.getTotalBlocks());
        careerStats.setTotalTurnovers(result.getTotalTurnovers());
        careerStats.setTotalFouls(result.getTotalFouls());
        careerStats.setTotalFieldGoalsMade(result.getTotalFieldGoalsMade());
        careerStats.setTotalFieldGoalsAttempted(result.getTotalFieldGoalsAttempted());
        careerStats.setTotalTwoPointsMade(result.getTotalTwoPointsMade());
        careerStats.setTotalTwoPointsAttempted(result.getTotalTwoPointsAttempted());
        careerStats.setTotalThreePointsMade(result.getTotalThreePointsMade());
        careerStats.setTotalThreePointsAttempted(result.getTotalThreePointsAttempted());
        careerStats.setTotalFreeThrowsMade(result.getTotalFreeThrowsMade());
        careerStats.setTotalFreeThrowsAttempted(result.getTotalFreeThrowsAttempted());
        
        // 注意：数据库字段为total_playing_time(秒)，需要转换
        if (result.getTotalMinutes() != null) {
            careerStats.setTotalMinutesPlayed((int)(result.getTotalMinutes().doubleValue() * 60)); // 分钟转秒
        }

        // 设置系统字段
        careerStats.setCreator("system");
        careerStats.setUpdater("system");

        return careerStats;
    }

    /**
     * 设置生涯特有字段
     */
    private void setCareerSpecificFields(PlayerCareerStatsDO careerStats, List<PlayerStatisticsDO> gameStats) {
        // 设置总赛季数（简化为1，实际需要根据比赛数据计算）
        careerStats.setTotalSeasons(1);
        
        // 设置首场和最近比赛日期（需要从比赛数据中获取）
        // 这里暂时设置为当前日期
        careerStats.setFirstGameDate(LocalDate.now());
        careerStats.setLatestGameDate(LocalDate.now());
    }

    /**
     * 创建空的生涯统计数据
     */
    private PlayerCareerStatsDO createEmptyCareerStats(Long playerId, Integer gameType) {
        PlayerCareerStatsDO emptyStats = new PlayerCareerStatsDO();
        emptyStats.setPlayerId(playerId);
        emptyStats.setGameType(gameType);
        emptyStats.setGamesPlayed(0);
        emptyStats.setValidStatsGames(0);
        // 其他字段使用默认值
        emptyStats.setCreator("system");
        emptyStats.setUpdater("system");
        return emptyStats;
    }
    
    /**
     * 创建默认的聚合统计结果（当commonCalculator不可用时的降级处理）
     */
    private PlayerStatsCalculatorCommon.StatsAggregationResult createDefaultAggregationResult(List<PlayerStatisticsDO> gameStats) {
        log.info("🔄 使用默认聚合实现，统计数据数量: {}", gameStats.size());
        
        PlayerStatsCalculatorCommon.StatsAggregationResult result = new PlayerStatsCalculatorCommon.StatsAggregationResult();
        
        if (gameStats == null || gameStats.isEmpty()) {
            // 返回空结果
            result.setTotalGames(0);
            result.setValidGames(0);
            result.setTotalPoints(0);
            result.setTotalRebounds(0);
            result.setTotalOffensiveRebounds(0);
            result.setTotalDefensiveRebounds(0);
            result.setTotalAssists(0);
            result.setTotalSteals(0);
            result.setTotalBlocks(0);
            result.setTotalTurnovers(0);
            result.setTotalFouls(0);
            result.setTotalFieldGoalsMade(0);
            result.setTotalFieldGoalsAttempted(0);
            result.setTotalTwoPointsMade(0);
            result.setTotalTwoPointsAttempted(0);
            result.setTotalThreePointsMade(0);
            result.setTotalThreePointsAttempted(0);
            result.setTotalFreeThrowsMade(0);
            result.setTotalFreeThrowsAttempted(0);
            result.setTotalMinutes(BigDecimal.ZERO);
            result.setTotalEfficiency(BigDecimal.ZERO);
            return result;
        }
        
        // 手动聚合统计数据
        int totalPoints = 0;
        int totalOffensiveRebounds = 0;
        int totalDefensiveRebounds = 0;
        int totalAssists = 0;
        int totalSteals = 0;
        int totalBlocks = 0;
        int totalTurnovers = 0;
        int totalFouls = 0;
        int totalTwoPointsMade = 0;
        int totalTwoPointsAttempted = 0;
        int totalThreePointsMade = 0;
        int totalThreePointsAttempted = 0;
        int totalFreeThrowsMade = 0;
        int totalFreeThrowsAttempted = 0;
        BigDecimal totalMinutes = BigDecimal.ZERO;
        BigDecimal totalEfficiency = BigDecimal.ZERO;
        
        // 统计有效比赛数量
        long uniqueGames = gameStats.stream().mapToLong(PlayerStatisticsDO::getGameId).distinct().count();
        long validGames = gameStats.stream().filter(stat -> stat.getPoints() != null && stat.getPoints() > 0).mapToLong(PlayerStatisticsDO::getGameId).distinct().count();
        
        // 聚合所有统计数据
        for (PlayerStatisticsDO stat : gameStats) {
            totalPoints += safeGetInt(stat.getPoints());
            totalOffensiveRebounds += safeGetInt(stat.getOffensiveRebounds());
            totalDefensiveRebounds += safeGetInt(stat.getDefensiveRebounds());
            totalAssists += safeGetInt(stat.getAssists());
            totalSteals += safeGetInt(stat.getSteals());
            totalBlocks += safeGetInt(stat.getBlocks());
            totalTurnovers += safeGetInt(stat.getTurnovers());
            totalFouls += safeGetInt(stat.getFouls());
            totalTwoPointsMade += safeGetInt(stat.getTwoPointMakes());
            totalTwoPointsAttempted += safeGetInt(stat.getTwoPointAttempts());
            totalThreePointsMade += safeGetInt(stat.getThreePointMakes());
            totalThreePointsAttempted += safeGetInt(stat.getThreePointAttempts());
            totalFreeThrowsMade += safeGetInt(stat.getFreeThrowMakes());
            totalFreeThrowsAttempted += safeGetInt(stat.getFreeThrowAttempts());
            
            if (stat.getPlayingTime() != null) {
                BigDecimal minutes = BigDecimal.valueOf(stat.getPlayingTime()).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP);
                totalMinutes = totalMinutes.add(minutes);
            }
            
            if (stat.getEfficiency() != null) {
                totalEfficiency = totalEfficiency.add(BigDecimal.valueOf(stat.getEfficiency()));
            }
        }
        
        // 设置聚合结果
        result.setTotalGames((int) uniqueGames);
        result.setValidGames((int) validGames);
        result.setTotalPoints(totalPoints);
        result.setTotalRebounds(totalOffensiveRebounds + totalDefensiveRebounds);
        result.setTotalOffensiveRebounds(totalOffensiveRebounds);
        result.setTotalDefensiveRebounds(totalDefensiveRebounds);
        result.setTotalAssists(totalAssists);
        result.setTotalSteals(totalSteals);
        result.setTotalBlocks(totalBlocks);
        result.setTotalTurnovers(totalTurnovers);
        result.setTotalFouls(totalFouls);
        result.setTotalFieldGoalsMade(totalTwoPointsMade + totalThreePointsMade);
        result.setTotalFieldGoalsAttempted(totalTwoPointsAttempted + totalThreePointsAttempted);
        result.setTotalTwoPointsMade(totalTwoPointsMade);
        result.setTotalTwoPointsAttempted(totalTwoPointsAttempted);
        result.setTotalThreePointsMade(totalThreePointsMade);
        result.setTotalThreePointsAttempted(totalThreePointsAttempted);
        result.setTotalFreeThrowsMade(totalFreeThrowsMade);
        result.setTotalFreeThrowsAttempted(totalFreeThrowsAttempted);
        result.setTotalMinutes(totalMinutes);
        result.setTotalEfficiency(totalEfficiency);
        
        log.info("✅ 默认聚合完成: 比赛{}/{}场, 总分{}, 总篮板{}", uniqueGames, validGames, totalPoints, totalOffensiveRebounds + totalDefensiveRebounds);
        
        return result;
    }
    
    /**
     * 创建默认的连胜数据结果（当commonCalculator不可用时的降级处理）
     */
    private PlayerStatsCalculatorCommon.StreakDataResult createDefaultStreakResult(List<PlayerGameRelatedDO> gameResults) {
        log.info("🔄 使用默认连胜实现，比赛数据数量: {}", gameResults != null ? gameResults.size() : 0);
        
        PlayerStatsCalculatorCommon.StreakDataResult result = new PlayerStatsCalculatorCommon.StreakDataResult();
        
        if (gameResults == null || gameResults.isEmpty()) {
            result.setCurrentStreak(0);
            result.setMaxWinStreak(0);
            result.setMaxLoseStreak(0);
            result.setTotalWins(0);
            result.setTotalLosses(0);
            result.setWinRate(BigDecimal.ZERO);
            result.setStreakStartDate(LocalDate.now());
            return result;
        }
        
        // 简化的连胜计算（基于ID的奇偶性模拟胜负）
        int currentStreak = 0;
        int maxWinStreak = 0;
        int maxLoseStreak = 0;
        int totalWins = 0;
        int totalLosses = 0;
        int tempWinStreak = 0;
        int tempLoseStreak = 0;
        LocalDate streakStartDate = LocalDate.now();
        
        for (PlayerGameRelatedDO gameResult : gameResults) {
            // 简化逻辑：ID为偶数表示胜利，奇数表示失败
            boolean isWin = gameResult.getId() % 2 == 0;
            
            if (isWin) {
                totalWins++;
                tempWinStreak++;
                tempLoseStreak = 0;
                maxWinStreak = Math.max(maxWinStreak, tempWinStreak);
                currentStreak = tempWinStreak;
            } else {
                totalLosses++;
                tempLoseStreak++;
                tempWinStreak = 0;
                maxLoseStreak = Math.max(maxLoseStreak, tempLoseStreak);
                currentStreak = -tempLoseStreak;
            }
        }
        
        result.setCurrentStreak(currentStreak);
        result.setMaxWinStreak(maxWinStreak);
        result.setMaxLoseStreak(maxLoseStreak);
        result.setTotalWins(totalWins);
        result.setTotalLosses(totalLosses);
        result.setStreakStartDate(streakStartDate);
        
        int totalGames = totalWins + totalLosses;
        if (totalGames > 0) {
            BigDecimal winRate = BigDecimal.valueOf(totalWins)
                    .divide(BigDecimal.valueOf(totalGames), 4, RoundingMode.HALF_UP);
            result.setWinRate(winRate);
        } else {
            result.setWinRate(BigDecimal.ZERO);
        }
        
        log.info("✅ 默认连胜计算完成: 当前连胜={}, 最大连胜={}, 胜率={}%", currentStreak, maxWinStreak, result.getWinRate());
        
        return result;
    }

    /**
     * 安全获取整数值，避免空指针
     */
    private int safeGetInt(Integer value) {
        return value != null ? value : 0;
    }

    /**
     * 安全除法计算，保留指定小数位
     */
    private BigDecimal divide(Integer dividend, int divisor, int scale) {
        if (dividend == null || divisor == 0) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(dividend)
            .divide(BigDecimal.valueOf(divisor), scale, RoundingMode.HALF_UP);
    }

    /**
     * 验证百分比范围
     */
    private void validatePercentageRange(BigDecimal percentage, String fieldName) {
        if (percentage != null && 
           (percentage.compareTo(BigDecimal.ZERO) < 0 || percentage.compareTo(BigDecimal.ONE) > 0)) {
            throw new IllegalArgumentException(fieldName + "必须在0-1之间");
        }
    }
} 