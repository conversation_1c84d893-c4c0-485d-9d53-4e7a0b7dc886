package cn.iocoder.yudao.module.operation.dal.mysql.player;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.operation.controller.admin.player.vo.PlayerPageReqVO;
import cn.iocoder.yudao.module.operation.controller.app.player.vo.AppPlayerMarketPageReqVO;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerDO;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 球员 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PlayerMapper extends BaseMapperX<PlayerDO> {


    default PageResult<PlayerDO> selectPage(PlayerPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PlayerDO>()
                .eqIfPresent(PlayerDO::getMemberUserId, reqVO.getMemberUserId())
                .eqIfPresent(PlayerDO::getNumber, reqVO.getNumber())
                .likeIfPresent(PlayerDO::getName, reqVO.getName())
                .eqIfPresent(PlayerDO::getAvatar, reqVO.getAvatar())
                .eqIfPresent(PlayerDO::getHeight, reqVO.getHeight())
                .eqIfPresent(PlayerDO::getWeight, reqVO.getWeight())
                .eqIfPresent(PlayerDO::getRatings, reqVO.getRatings())
                .eqIfPresent(PlayerDO::getPosition, reqVO.getPosition())
                .eqIfPresent(PlayerDO::getExperience, reqVO.getExperience())
                .eqIfPresent(PlayerDO::getSex, reqVO.getSex())
                .eqIfPresent(PlayerDO::getCreditScore, reqVO.getCreditScore())
                .betweenIfPresent(PlayerDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(PlayerDO::getId));
    }

    default PageResult<PlayerDO> selectMarketPage(AppPlayerMarketPageReqVO reqVO) {
        LambdaQueryWrapperX<PlayerDO> query = new LambdaQueryWrapperX<PlayerDO>()
                .likeIfPresent(PlayerDO::getName, reqVO.getKeyword())
                .eqIfPresent(PlayerDO::getPosition, reqVO.getPosition());

        // 处理 ratings 参数
        String ratingsStr = reqVO.getRatings();
        if (StringUtils.isNotBlank(ratingsStr)) { // 非空且非空白
            String[] ratingsArr = ratingsStr.split("-");
            if (ratingsArr.length == 2) {
                //数据库是存了 *100 的值，所以需要乘以100
                int minRatings = Integer.parseInt(ratingsArr[0]) * 100;
                int maxRatings = Integer.parseInt(ratingsArr[1]) * 100;
                query.between(PlayerDO::getRatings, minRatings, maxRatings);
            }
            if (ratingsArr.length == 1) {
                query.geIfPresent(PlayerDO::getRatings, Integer.parseInt(ratingsArr[0]) * 100);
            }
        }

        // 处理 height 参数
        String heightStr = reqVO.getHeight();
        if (StringUtils.isNotBlank(heightStr)) {
            String[] heightArr = heightStr.split("-");
            if (heightArr.length == 2) {
                int minHeight = Integer.parseInt(heightArr[0]);
                int maxHeight = Integer.parseInt(heightArr[1]);
                query.between(PlayerDO::getHeight, minHeight, maxHeight);
            }
            if (heightArr.length == 1) {
                query.geIfPresent(PlayerDO::getHeight, heightArr[0]);
            }
        }

        query.orderByDesc(PlayerDO::getId);

        return selectPage(reqVO, query);
    }

    PlayerDO getPlayerByMemberId(Long memberId);

    @Select("SELECT AVG(experience) FROM sd_player")
    Integer getPlayerAvgRatings();

    Integer getPlayerP20Experience();

    Integer getPlayerP20Ratings();

    default PageResult<PlayerDO> getRatingsRankPage(PageParam pageVO) {
        LambdaQueryWrapperX<PlayerDO> query = new LambdaQueryWrapperX<PlayerDO>()
                .orderByDesc(PlayerDO::getRatings).orderByDesc(PlayerDO::getId);
        return selectPage(pageVO, query);
    }
}