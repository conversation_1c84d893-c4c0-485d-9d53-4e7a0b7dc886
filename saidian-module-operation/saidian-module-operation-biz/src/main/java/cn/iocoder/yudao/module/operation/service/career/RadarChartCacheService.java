package cn.iocoder.yudao.module.operation.service.career;

import cn.iocoder.yudao.module.operation.controller.app.player.vo.AppPlayerCareerOverviewRespVO;
import cn.iocoder.yudao.module.operation.controller.app.player.AppPlayerController.LeagueBestStats;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.Set;

/**
 * 雷达图缓存服务（Redis版本）
 * 
 * 为了提升雷达图查询性能，使用Redis对联盟最佳数据和雷达图数据进行缓存
 * 
 * <AUTHOR> Assistant
 * @since 2024
 */
@Service
@Slf4j
public class RadarChartCacheService {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    
    // 缓存键前缀
    private static final String LEAGUE_BEST_PREFIX = "career:league_best:";
    private static final String RADAR_CHART_PREFIX = "career:radar_chart:";
    
    // 缓存有效时间
    private static final Duration LEAGUE_BEST_EXPIRE = Duration.ofMinutes(30); // 联盟最佳数据缓存30分钟
    private static final Duration RADAR_CHART_EXPIRE = Duration.ofMinutes(10); // 雷达图数据缓存10分钟

    /**
     * 获取联盟最佳数据（Redis缓存）
     */
    public LeagueBestStats getLeagueBestStats(Integer gameType) {
        String cacheKey = LEAGUE_BEST_PREFIX + gameType;
        try {
            LeagueBestStats cachedStats = (LeagueBestStats) redisTemplate.opsForValue().get(cacheKey);
            if (cachedStats != null) {
                log.debug("🎯 联盟最佳数据Redis缓存命中: gameType={}", gameType);
                return cachedStats;
            }
        } catch (Exception e) {
            log.warn("获取联盟最佳数据缓存失败: gameType={}, error={}", gameType, e.getMessage());
        }
        
        log.debug("📊 联盟最佳数据缓存未命中: gameType={}", gameType);
        return null; // 缓存未命中，需要重新计算
    }

    /**
     * 缓存联盟最佳数据到Redis
     */
    public void cacheLeagueBestStats(Integer gameType, LeagueBestStats bestStats) {
        if (bestStats != null) {
            String cacheKey = LEAGUE_BEST_PREFIX + gameType;
            try {
                redisTemplate.opsForValue().set(cacheKey, bestStats, LEAGUE_BEST_EXPIRE);
                log.debug("💾 联盟最佳数据已缓存到Redis: gameType={}", gameType);
            } catch (Exception e) {
                log.error("缓存联盟最佳数据失败: gameType={}, error={}", gameType, e.getMessage());
            }
        }
    }

    /**
     * 获取雷达图数据（Redis缓存）
     */
    public AppPlayerCareerOverviewRespVO.RadarChartData getRadarChartData(Long playerId, Integer gameType) {
        String cacheKey = buildRadarChartCacheKey(playerId, gameType);
        try {
            AppPlayerCareerOverviewRespVO.RadarChartData cachedData = 
                (AppPlayerCareerOverviewRespVO.RadarChartData) redisTemplate.opsForValue().get(cacheKey);
            if (cachedData != null) {
                log.debug("🎯 雷达图数据Redis缓存命中: playerId={}, gameType={}", playerId, gameType);
                return cachedData;
            }
        } catch (Exception e) {
            log.warn("获取雷达图数据缓存失败: playerId={}, gameType={}, error={}", playerId, gameType, e.getMessage());
        }
        
        log.debug("📊 雷达图数据缓存未命中: playerId={}, gameType={}", playerId, gameType);
        return null; // 缓存未命中，需要重新计算
    }

    /**
     * 缓存雷达图数据到Redis
     */
    public void cacheRadarChartData(Long playerId, Integer gameType, AppPlayerCareerOverviewRespVO.RadarChartData radarChart) {
        if (radarChart != null) {
            String cacheKey = buildRadarChartCacheKey(playerId, gameType);
            try {
                redisTemplate.opsForValue().set(cacheKey, radarChart, RADAR_CHART_EXPIRE);
                log.debug("💾 雷达图数据已缓存到Redis: playerId={}, gameType={}", playerId, gameType);
            } catch (Exception e) {
                log.error("缓存雷达图数据失败: playerId={}, gameType={}, error={}", playerId, gameType, e.getMessage());
            }
        }
    }

    /**
     * 清除指定球员的雷达图缓存
     */
    public void evictPlayerRadarChartCache(Long playerId) {
        try {
            String pattern = RADAR_CHART_PREFIX + "player:" + playerId + ":*";
            Set<String> keys = redisTemplate.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                log.debug("🗑️ 已清除球员雷达图缓存: playerId={}, 清除键数: {}", playerId, keys.size());
            }
        } catch (Exception e) {
            log.error("清除球员雷达图缓存失败: playerId={}, error={}", playerId, e.getMessage());
        }
    }

    /**
     * 清除联盟最佳数据缓存
     */
    public void evictLeagueBestStatsCache() {
        try {
            String pattern = LEAGUE_BEST_PREFIX + "*";
            Set<String> keys = redisTemplate.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                log.debug("🗑️ 已清除联盟最佳数据缓存, 清除键数: {}", keys.size());
            }
        } catch (Exception e) {
            log.error("清除联盟最佳数据缓存失败: error={}", e.getMessage());
        }
    }

    /**
     * 清除所有缓存
     */
    public void evictAllCache() {
        try {
            // 清除联盟最佳数据缓存
            evictLeagueBestStatsCache();
            
            // 清除所有雷达图缓存
            String radarPattern = RADAR_CHART_PREFIX + "*";
            Set<String> radarKeys = redisTemplate.keys(radarPattern);
            if (radarKeys != null && !radarKeys.isEmpty()) {
                redisTemplate.delete(radarKeys);
            }
            
            log.info("🗑️ 已清除所有雷达图相关缓存");
        } catch (Exception e) {
            log.error("清除所有缓存失败: error={}", e.getMessage());
        }
    }

    /**
     * 获取缓存统计信息
     */
    public String getCacheStats() {
        try {
            Set<String> leagueKeys = redisTemplate.keys(LEAGUE_BEST_PREFIX + "*");
            Set<String> radarKeys = redisTemplate.keys(RADAR_CHART_PREFIX + "*");
            
            int leagueCount = leagueKeys != null ? leagueKeys.size() : 0;
            int radarCount = radarKeys != null ? radarKeys.size() : 0;
            
            return String.format("联盟最佳数据缓存: %d 项, 雷达图缓存: %d 项", leagueCount, radarCount);
        } catch (Exception e) {
            log.error("获取缓存统计信息失败: error={}", e.getMessage());
            return "获取缓存统计信息失败: " + e.getMessage();
        }
    }

    /**
     * 构建雷达图缓存键
     */
    private String buildRadarChartCacheKey(Long playerId, Integer gameType) {
        return RADAR_CHART_PREFIX + String.format("player:%d:gameType:%d", playerId, gameType);
    }
}