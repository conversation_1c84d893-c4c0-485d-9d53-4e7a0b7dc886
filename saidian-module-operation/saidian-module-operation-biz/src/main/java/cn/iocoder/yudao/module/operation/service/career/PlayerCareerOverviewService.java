package cn.iocoder.yudao.module.operation.service.career;

import cn.iocoder.yudao.module.operation.controller.app.player.vo.AppPlayerCareerOverviewRespVO;
import cn.iocoder.yudao.module.operation.controller.app.player.vo.AppPlayerCareerQueryReqVO;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerCareerStatsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerSeasonStatsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerDO;
import cn.iocoder.yudao.module.operation.service.player.PlayerService;
import cn.iocoder.yudao.module.operation.service.player.SeasonService;
import cn.iocoder.yudao.module.operation.service.game.PlayerStatisticsService;
import cn.iocoder.yudao.module.operation.service.game.bo.PlayerCareerStatistics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.Optional;

import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * 球员生涯概览服务
 * 
 * 负责提供球员生涯概览数据，包括核心统计、雷达图等
 * 
 * <AUTHOR> Assistant
 * @since 2024
 */
@Service
@Slf4j
public class PlayerCareerOverviewService {

    @Resource
    private PlayerCareerStatsService playerCareerStatsService;
    
    @Resource
    private PlayerSeasonStatsService playerSeasonStatsService;
    
    @Resource
    private PlayerService playerService;

    @Resource
    private PlayerStatisticsService playerStatisticsService;

    @Resource
    private SeasonService seasonService;

    /**
     * 获取球员生涯概览数据
     * 
     * @param reqVO 查询请求
     * @return 生涯概览数据
     */
    public AppPlayerCareerOverviewRespVO getPlayerCareerOverview(AppPlayerCareerQueryReqVO reqVO) {
        log.info("获取球员生涯概览数据，参数：{}", reqVO);
        
        AppPlayerCareerOverviewRespVO response = new AppPlayerCareerOverviewRespVO();
        
        try {
            // 获取球员ID
            Long playerId = resolvePlayerId(reqVO.getPlayerId());
            if (playerId == null) {
                log.warn("无法获取球员ID，返回空数据");
                return createEmptyOverview();
            }
            
            // 设置当前赛季
            String currentSeason = getCurrentSeason();
            response.setCurrentSeason(currentSeason);
            
            // 构建可用赛季列表
            response.setAvailableSeasons(buildAvailableSeasons(currentSeason));
            
            // 构建核心统计数据
            response.setCoreStats(buildCoreStatsData(playerId, reqVO.getGameType(), reqVO.getSeason()));
            
            // 构建雷达图数据
            response.setRadarChart(buildRadarChartData(playerId, reqVO.getGameType(), reqVO.getSeason()));
            
            return response;
        } catch (Exception e) {
            log.error("获取球员生涯概览数据失败", e);
            return createEmptyOverview();
        }
    }

    /**
     * 构建核心统计数据
     */
    private AppPlayerCareerOverviewRespVO.CoreStatsData buildCoreStatsData(Long playerId, Integer gameType, String season) {
        AppPlayerCareerOverviewRespVO.CoreStatsData coreStats = new AppPlayerCareerOverviewRespVO.CoreStatsData();
        
        try {
            // 根据是否指定赛季来获取不同的数据
            if (season != null) {
                // 获取指定赛季的数据
                PlayerSeasonStatsDO seasonStats = getSeasonStats(playerId, season, gameType);
                if (seasonStats != null) {
                    coreStats = buildCoreStatsFromSeasonData(seasonStats);
                } else {
                    coreStats = createDefaultCoreStats();
                }
            } else {
                // 获取生涯数据
                PlayerCareerStatsDO careerStats = playerCareerStatsService.calculateCareerStats(playerId, gameType);
                if (careerStats != null) {
                    coreStats = buildCoreStatsFromCareerData(careerStats);
                } else {
                    coreStats = createDefaultCoreStats();
                }
            }
        } catch (Exception e) {
            log.error("构建核心统计数据失败", e);
            coreStats = createDefaultCoreStats();
        }
        
        return coreStats;
    }

    /**
     * 构建雷达图数据
     */
    private AppPlayerCareerOverviewRespVO.RadarChartData buildRadarChartData(Long playerId, Integer gameType, String season) {
        AppPlayerCareerOverviewRespVO.RadarChartData radarChart = new AppPlayerCareerOverviewRespVO.RadarChartData();
        
        try {
            // 获取球员数据
            PlayerCareerStatsDO careerStats = playerCareerStatsService.calculateCareerStats(playerId, gameType);
            
            if (careerStats != null) {
                // 获取联盟各维度最佳数据（替换联盟平均数据）
                LeagueBestStats leagueBest = getLeagueBestStats(gameType);
                
                // 构建雷达图数据点
                List<AppPlayerCareerOverviewRespVO.RadarDataPoint> dataPoints = buildRadarDataPointsWithBest(careerStats, leagueBest);
                radarChart.setDataPoints(dataPoints);
                
                // 计算综合评分
                double avgRating = dataPoints.stream()
                    .mapToDouble(point -> point.getScore().doubleValue())
                    .average()
                    .orElse(50.0); // 相对于最佳数据的平均比例
                radarChart.setOverallRating(BigDecimal.valueOf(avgRating).setScale(1, RoundingMode.HALF_UP));
                radarChart.setSampleGames(careerStats.getGamesPlayed());
                radarChart.setRatingDescription("基于生涯" + careerStats.getGamesPlayed() + "场比赛数据，与联盟最佳数据对比");
            } else {
                radarChart = createDefaultRadarChart();
            }
        } catch (Exception e) {
            log.error("构建雷达图数据失败", e);
            radarChart = createDefaultRadarChart();
        }
        
        return radarChart;
    }

    /**
     * 从生涯数据构建核心统计
     */
    private AppPlayerCareerOverviewRespVO.CoreStatsData buildCoreStatsFromCareerData(PlayerCareerStatsDO careerStats) {
        AppPlayerCareerOverviewRespVO.CoreStatsData coreStats = new AppPlayerCareerOverviewRespVO.CoreStatsData();
        
        // 基础统计数据
        List<AppPlayerCareerOverviewRespVO.StatItem> basicStats = Arrays.asList(
            createStatItem("场均得分", formatDecimal(careerStats.getAvgPoints()), "分", "number"),
            createStatItem("场均篮板", formatDecimal(careerStats.getAvgRebounds()), "个", "number"),
            createStatItem("场均助攻", formatDecimal(careerStats.getAvgAssists()), "次", "number"),
            createStatItem("场均抢断", formatDecimal(careerStats.getAvgSteals()), "次", "number"),
            createStatItem("场均盖帽", formatDecimal(careerStats.getAvgBlocks()), "次", "number"),
            createStatItem("场均失误", formatDecimal(careerStats.getAvgTurnovers()), "次", "number")
        );
        coreStats.setBasicStats(basicStats);
        
        // 命中率统计数据
        List<AppPlayerCareerOverviewRespVO.StatItem> shootingStats = Arrays.asList(
            createStatItem("投篮命中率", formatPercentage(careerStats.getFieldGoalPercentage()), "%", "percentage"),
            createStatItem("三分命中率", formatPercentage(careerStats.getThreePointPercentage()), "%", "percentage"),
            createStatItem("二分命中率", formatPercentage(careerStats.getTwoPointPercentage()), "%", "percentage"),
            createStatItem("罚球命中率", formatPercentage(careerStats.getFreeThrowPercentage()), "%", "percentage")
        );
        coreStats.setShootingStats(shootingStats);
        
        // 高阶统计数据
        List<AppPlayerCareerOverviewRespVO.StatItem> advancedStats = Arrays.asList(
            createStatItem("效率值", formatDecimal(careerStats.getAvgEfficiency()), "", "number"),
            createStatItem("真实命中率", formatPercentage(careerStats.getTrueShootingPercentage()), "%", "percentage"),
            createStatItem("使用率", formatPercentage(careerStats.getUsageRate()), "%", "percentage"),
            createStatItem("净胜分", formatDecimal(careerStats.getPlusMinus()), "", "number")
        );
        coreStats.setAdvancedStats(advancedStats);
        
        // 连胜统计数据
        List<AppPlayerCareerOverviewRespVO.StatItem> streakStats = Arrays.asList(
            createStatItem("当前连胜", String.valueOf(careerStats.getCurrentStreak()), "场", "number"),
            createStatItem("最大连胜", String.valueOf(careerStats.getMaxWinStreak()), "场", "number"),
            createStatItem("生涯总场次", String.valueOf(careerStats.getGamesPlayed()), "场", "number"),
            createStatItem("胜率", formatPercentage(careerStats.getWinRate()), "%", "percentage")
        );
        coreStats.setStreakStats(streakStats);
        
        return coreStats;
    }

    /**
     * 从赛季数据构建核心统计
     */
    private AppPlayerCareerOverviewRespVO.CoreStatsData buildCoreStatsFromSeasonData(PlayerSeasonStatsDO seasonStats) {
        AppPlayerCareerOverviewRespVO.CoreStatsData coreStats = new AppPlayerCareerOverviewRespVO.CoreStatsData();
        
        // 基础统计数据
        List<AppPlayerCareerOverviewRespVO.StatItem> basicStats = Arrays.asList(
            createStatItem("场均得分", formatDecimal(seasonStats.getAvgPoints()), "分", "number"),
            createStatItem("场均篮板", formatDecimal(seasonStats.getAvgRebounds()), "个", "number"),
            createStatItem("场均助攻", formatDecimal(seasonStats.getAvgAssists()), "次", "number"),
            createStatItem("场均抢断", formatDecimal(seasonStats.getAvgSteals()), "次", "number"),
            createStatItem("场均盖帽", formatDecimal(seasonStats.getAvgBlocks()), "次", "number"),
            createStatItem("场均失误", formatDecimal(seasonStats.getAvgTurnovers()), "次", "number")
        );
        coreStats.setBasicStats(basicStats);
        
        // 命中率统计数据
        List<AppPlayerCareerOverviewRespVO.StatItem> shootingStats = Arrays.asList(
            createStatItem("投篮命中率", formatPercentage(seasonStats.getFieldGoalPercentage()), "%", "percentage"),
            createStatItem("三分命中率", formatPercentage(seasonStats.getThreePointPercentage()), "%", "percentage"),
            createStatItem("二分命中率", formatPercentage(seasonStats.getTwoPointPercentage()), "%", "percentage"),
            createStatItem("罚球命中率", formatPercentage(seasonStats.getFreeThrowPercentage()), "%", "percentage")
        );
        coreStats.setShootingStats(shootingStats);
        
        // 高阶统计数据
        List<AppPlayerCareerOverviewRespVO.StatItem> advancedStats = Arrays.asList(
            createStatItem("效率值", formatDecimal(seasonStats.getEfficiencyRating()), "", "number"),
            createStatItem("真实命中率", formatPercentage(seasonStats.getTrueShootingPercentage()), "%", "percentage"),
            createStatItem("使用率", formatPercentage(seasonStats.getUsageRate()), "%", "percentage"),
            createStatItem("球员效率", formatDecimal(seasonStats.getPlayerEfficiencyRating()), "", "number")
        );
        coreStats.setAdvancedStats(advancedStats);
        
        // 连胜统计数据
        List<AppPlayerCareerOverviewRespVO.StatItem> streakStats = Arrays.asList(
            createStatItem("当前连胜", String.valueOf(seasonStats.getCurrentStreak()), "场", "number"),
            createStatItem("最大连胜", String.valueOf(seasonStats.getMaxWinStreak()), "场", "number"),
            createStatItem("本赛季场次", String.valueOf(seasonStats.getGamesPlayed()), "场", "number"),
            createStatItem("胜率", formatPercentage(seasonStats.getWinRate()), "%", "percentage")
        );
        coreStats.setStreakStats(streakStats);
        
        return coreStats;
    }

    /**
     * 构建雷达图数据点 - 修复版本
     */
    private List<AppPlayerCareerOverviewRespVO.RadarDataPoint> buildRadarDataPointsWithBest(
            PlayerCareerStatsDO careerStats, LeagueBestStats leagueBest) {
        
        List<AppPlayerCareerOverviewRespVO.RadarDataPoint> dataPoints = new ArrayList<>();
        
        // 获取球员数据，如果为null则使用0
        BigDecimal playerAvgPoints = Optional.ofNullable(careerStats.getAvgPoints()).orElse(BigDecimal.ZERO);
        BigDecimal playerAvgRebounds = Optional.ofNullable(careerStats.getAvgRebounds()).orElse(BigDecimal.ZERO);
        BigDecimal playerAvgAssists = Optional.ofNullable(careerStats.getAvgAssists()).orElse(BigDecimal.ZERO);
        BigDecimal playerAvgBlocks = Optional.ofNullable(careerStats.getAvgBlocks()).orElse(BigDecimal.ZERO);
        BigDecimal playerAvgSteals = Optional.ofNullable(careerStats.getAvgSteals()).orElse(BigDecimal.ZERO);
        
        // 得分维度
        dataPoints.add(createRadarDataPoint(
            "得分", 
            playerAvgPoints, 
            leagueBest.getBestAvgPoints(),
            "场均得分与联盟最佳对比"
        ));
        
        // 篮板维度
        dataPoints.add(createRadarDataPoint(
            "篮板", 
            playerAvgRebounds, 
            leagueBest.getBestAvgRebounds(),
            "场均篮板与联盟最佳对比"
        ));
        
        // 助攻维度
        dataPoints.add(createRadarDataPoint(
            "助攻", 
            playerAvgAssists, 
            leagueBest.getBestAvgAssists(),
            "场均助攻与联盟最佳对比"
        ));
        
        // 盖帽维度
        dataPoints.add(createRadarDataPoint(
            "盖帽", 
            playerAvgBlocks, 
            leagueBest.getBestAvgBlocks(),
            "场均盖帽与联盟最佳对比"
        ));
        
        // 抢断维度
        dataPoints.add(createRadarDataPoint(
            "抢断", 
            playerAvgSteals, 
            leagueBest.getBestAvgSteals(),
            "场均抢断与联盟最佳对比"
        ));
        
        return dataPoints;
    }
    
    /**
     * 创建雷达图数据点 - 修复版本
     */
    private AppPlayerCareerOverviewRespVO.RadarDataPoint createRadarDataPoint(
            String dimension, BigDecimal playerValue, BigDecimal bestValue, String description) {
        
        AppPlayerCareerOverviewRespVO.RadarDataPoint dataPoint = new AppPlayerCareerOverviewRespVO.RadarDataPoint();
        dataPoint.setDimension(dimension);
        dataPoint.setDescription(description);
        
        // 处理空值和零值
        double playerVal = playerValue != null ? playerValue.doubleValue() : 0.0;
        double bestVal = bestValue != null ? bestValue.doubleValue() : 1.0; // 避免除零
        
        // 如果最佳值为0，设置为1避免除零错误
        if (bestVal <= 0) {
            bestVal = 1.0;
        }
        
        // 计算相对于最佳值的百分比 (0-100%)
        double percentage = Math.min(100.0, (playerVal / bestVal) * 100.0);
        
        // 设置统一的最大值为100，便于雷达图展示
        dataPoint.setScore(BigDecimal.valueOf(percentage));
        dataPoint.setMaxValue(BigDecimal.valueOf(100.0)); // 统一最大值为100
        
        // 添加调试日志
        log.debug("雷达图数据点 - 维度: {}, 球员值: {}, 最佳值: {}, 百分比: {:.1f}%", 
                dimension, playerVal, bestVal, percentage);
        
        return dataPoint;
    }

    /**
     * 获取联盟平均数据
     */
    private LeagueAverageStats getLeagueAverageStats(Integer gameType) {
        try {
            // 尝试从数据库计算真实的联盟平均数据
            Map<Long, PlayerCareerStatistics> allPlayerStats = playerStatisticsService.getPlayerCareerStatisticsMap();
            
            if (!allPlayerStats.isEmpty()) {
                // 过滤出有效数据的球员（至少参加过5场比赛）
                List<PlayerCareerStatistics> validPlayers = allPlayerStats.values().stream()
                    .filter(stats -> stats != null)
                    .filter(stats -> stats.getTotalGames() != null && stats.getTotalGames() >= 5)
                    .filter(stats -> stats.getBasicStatistics() != null)
                    .collect(Collectors.toList());
                
                if (!validPlayers.isEmpty()) {
                    return calculateLeagueAverageFromPlayers(validPlayers);
                }
            }
        } catch (Exception e) {
            log.warn("获取联盟平均数据失败，使用默认值: {}", e.getMessage());
        }
        
        // 使用默认值
        return createDefaultLeagueAverageStats();
    }

    /**
     * 获取联盟各维度最佳数据
     */
    private LeagueBestStats getLeagueBestStats(Integer gameType) {
        try {
            return calculateLeagueBestFromPlayers(gameType);
        } catch (Exception e) {
            log.error("获取联盟最佳数据失败", e);
            return createDefaultLeagueBestStats();
        }
    }

    /**
     * 从球员数据计算联盟平均值
     */
    private LeagueAverageStats calculateLeagueAverageFromPlayers(List<PlayerCareerStatistics> validPlayers) {
        LeagueAverageStats averageStats = new LeagueAverageStats();
        
        double avgPoints = validPlayers.stream()
            .mapToDouble(stats -> calculatePlayerAverage(stats.getBasicStatistics().getPoints(), stats.getTotalGames()))
            .filter(value -> value > 0)
            .average().orElse(15.0);
        
        double avgRebounds = validPlayers.stream()
            .mapToDouble(stats -> calculatePlayerAverage(stats.getBasicStatistics().getRebounds(), stats.getTotalGames()))
            .filter(value -> value > 0)
            .average().orElse(6.0);
        
        double avgAssists = validPlayers.stream()
            .mapToDouble(stats -> calculatePlayerAverage(stats.getBasicStatistics().getAssists(), stats.getTotalGames()))
            .filter(value -> value > 0)
            .average().orElse(4.0);
        
        double avgBlocks = validPlayers.stream()
            .mapToDouble(stats -> calculatePlayerAverage(stats.getBasicStatistics().getBlocks(), stats.getTotalGames()))
            .filter(value -> value >= 0)
            .average().orElse(0.8);
        
        double avgSteals = validPlayers.stream()
            .mapToDouble(stats -> calculatePlayerAverage(stats.getBasicStatistics().getSteals(), stats.getTotalGames()))
            .filter(value -> value >= 0)
            .average().orElse(1.2);
        
        averageStats.setAvgPoints(BigDecimal.valueOf(avgPoints).setScale(2, RoundingMode.HALF_UP));
        averageStats.setAvgRebounds(BigDecimal.valueOf(avgRebounds).setScale(2, RoundingMode.HALF_UP));
        averageStats.setAvgAssists(BigDecimal.valueOf(avgAssists).setScale(2, RoundingMode.HALF_UP));
        averageStats.setAvgBlocks(BigDecimal.valueOf(avgBlocks).setScale(2, RoundingMode.HALF_UP));
        averageStats.setAvgSteals(BigDecimal.valueOf(avgSteals).setScale(2, RoundingMode.HALF_UP));
        
        log.debug("计算联盟平均数据完成 - 得分:{}, 篮板:{}, 助攻:{}, 盖帽:{}, 抢断:{}, 样本数:{}",
            avgPoints, avgRebounds, avgAssists, avgBlocks, avgSteals, validPlayers.size());
        
        return averageStats;
    }

    /**
     * 从所有球员中计算联盟各维度最佳数据
     */
    private LeagueBestStats calculateLeagueBestFromPlayers(Integer gameType) {
        try {
            // 获取所有球员的生涯统计数据
            Map<Long, PlayerCareerStatistics> allPlayerStats = playerStatisticsService.getPlayerCareerStatisticsMap();
            
            if (!allPlayerStats.isEmpty()) {
                // 过滤出有效数据的球员（至少参加过5场比赛）
                List<PlayerCareerStatistics> validPlayers = allPlayerStats.values().stream()
                    .filter(stats -> stats != null)
                    .filter(stats -> stats.getTotalGames() != null && stats.getTotalGames() >= 5)
                    .filter(stats -> stats.getBasicStatistics() != null)
                    .collect(Collectors.toList());
                
                if (!validPlayers.isEmpty()) {
                    return calculateLeagueBestFromValidPlayers(validPlayers);
                }
            }
        } catch (Exception e) {
            log.warn("获取联盟最佳数据失败，使用默认值: {}", e.getMessage());
        }
        
        return createDefaultLeagueBestStats();
    }

    /**
     * 从有效球员数据计算联盟各维度最佳数据
     */
    private LeagueBestStats calculateLeagueBestFromValidPlayers(List<PlayerCareerStatistics> validPlayers) {
        LeagueBestStats leagueBest = new LeagueBestStats();
        
        // 计算各维度的最佳数据
        leagueBest.setBestAvgPoints(validPlayers.stream()
            .map(stats -> BigDecimal.valueOf(calculatePlayerAverage(stats.getBasicStatistics().getPoints(), stats.getTotalGames())))
            .max(BigDecimal::compareTo)
            .orElse(BigDecimal.valueOf(20.0)));
            
        leagueBest.setBestAvgRebounds(validPlayers.stream()
            .map(stats -> {
                Integer totalRebounds = (stats.getBasicStatistics().getOffensiveRebounds() != null ? stats.getBasicStatistics().getOffensiveRebounds() : 0) +
                                      (stats.getBasicStatistics().getDefensiveRebounds() != null ? stats.getBasicStatistics().getDefensiveRebounds() : 0);
                return BigDecimal.valueOf(calculatePlayerAverage(totalRebounds, stats.getTotalGames()));
            })
            .max(BigDecimal::compareTo)
            .orElse(BigDecimal.valueOf(10.0)));
            
        leagueBest.setBestAvgAssists(validPlayers.stream()
            .map(stats -> BigDecimal.valueOf(calculatePlayerAverage(stats.getBasicStatistics().getAssists(), stats.getTotalGames())))
            .max(BigDecimal::compareTo)
            .orElse(BigDecimal.valueOf(8.0)));
            
        leagueBest.setBestAvgBlocks(validPlayers.stream()
            .map(stats -> BigDecimal.valueOf(calculatePlayerAverage(stats.getBasicStatistics().getBlocks(), stats.getTotalGames())))
            .max(BigDecimal::compareTo)
            .orElse(BigDecimal.valueOf(3.0)));
            
        leagueBest.setBestAvgSteals(validPlayers.stream()
            .map(stats -> BigDecimal.valueOf(calculatePlayerAverage(stats.getBasicStatistics().getSteals(), stats.getTotalGames())))
            .max(BigDecimal::compareTo)
            .orElse(BigDecimal.valueOf(3.0)));

        log.info("联盟最佳数据计算完成 - 得分: {}, 篮板: {}, 助攻: {}, 盖帽: {}, 抢断: {}", 
            leagueBest.getBestAvgPoints(), leagueBest.getBestAvgRebounds(), leagueBest.getBestAvgAssists(),
            leagueBest.getBestAvgBlocks(), leagueBest.getBestAvgSteals());
            
        return leagueBest;
    }

    /**
     * 计算相对于最佳数据的得分比例
     */
    private BigDecimal calculateBestRelativeScore(BigDecimal playerValue, BigDecimal bestValue) {
        if (playerValue == null || bestValue == null || bestValue.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.valueOf(0);
        }
        
        // 计算球员数据相对于最佳数据的百分比，最高100%
        BigDecimal ratio = playerValue.divide(bestValue, 4, RoundingMode.HALF_UP);
        BigDecimal percentage = ratio.multiply(BigDecimal.valueOf(100));
        
        // 限制在0-100范围内
        if (percentage.compareTo(BigDecimal.valueOf(100)) > 0) {
            return BigDecimal.valueOf(100);
        }
        if (percentage.compareTo(BigDecimal.ZERO) < 0) {
            return BigDecimal.ZERO;
        }
        
        return percentage.setScale(1, RoundingMode.HALF_UP);
    }

    // 辅助方法

    /**
     * 解析球员ID
     */
    private Long resolvePlayerId(Long playerId) {
        if (playerId != null) {
            return playerId;
        }
        
        try {
            Long userId = getLoginUserId();
            PlayerDO player = playerService.getPlayerByUserId(userId);
            return player != null ? player.getId() : null;
        } catch (Exception e) {
            log.warn("获取当前用户球员ID失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 获取赛季统计数据
     */
    private PlayerSeasonStatsDO getSeasonStats(Long playerId, String season, Integer gameType) {
        try {
            // 这里需要根据season字符串转换为seasonId
            // 目前简化处理，直接返回null，后续可以扩展
            return null;
        } catch (Exception e) {
            log.error("获取赛季统计数据失败", e);
            return null;
        }
    }

    /**
     * 获取当前赛季
     */
    private String getCurrentSeason() {
        return String.valueOf(LocalDate.now().getYear());
    }

    /**
     * 构建可用赛季列表
     */
    private List<AppPlayerCareerOverviewRespVO.SeasonOption> buildAvailableSeasons(String currentSeason) {
        List<AppPlayerCareerOverviewRespVO.SeasonOption> seasons = new ArrayList<>();

        int currentYear = LocalDate.now().getYear();
        for (int i = 0; i < 3; i++) {
            int year = currentYear - i;
            AppPlayerCareerOverviewRespVO.SeasonOption season = new AppPlayerCareerOverviewRespVO.SeasonOption();
            season.setName(year + "赛季");
            season.setValue(String.valueOf(year));
            season.setCurrent(String.valueOf(year).equals(currentSeason));
            season.setStatus(i == 0 ? "进行中" : "已结束");
            season.setDescription(year + "年度赛季");
            seasons.add(season);
        }

        return seasons;
    }

    /**
     * 创建统计项
     */
    private AppPlayerCareerOverviewRespVO.StatItem createStatItem(String label, String value, String unit, String valueType) {
        AppPlayerCareerOverviewRespVO.StatItem item = new AppPlayerCareerOverviewRespVO.StatItem();
        item.setLabel(label);
        item.setValue(value);
        item.setUnit(unit);
        item.setValueType(valueType);
        return item;
    }



    /**
     * 计算相对于联盟平均值的评分
     */
    private BigDecimal calculateRelativeScore(BigDecimal playerValue, BigDecimal leagueAverage) {
        if (playerValue == null || leagueAverage == null || leagueAverage.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.valueOf(100);
        }
        
        double relativeScore = playerValue.divide(leagueAverage, 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100))
                .doubleValue();
        
        // 限制在合理范围内（最低20，最高200）
        relativeScore = Math.min(200, Math.max(20, relativeScore));
        
        return BigDecimal.valueOf(relativeScore).setScale(1, RoundingMode.HALF_UP);
    }

    /**
     * 计算球员单项数据的场均值
     */
    private double calculatePlayerAverage(Integer totalValue, Integer totalGames) {
        if (totalValue == null || totalGames == null || totalGames <= 0) {
            return 0.0;
        }
        return (double) totalValue / totalGames;
    }

    /**
     * 格式化小数
     */
    private String formatDecimal(BigDecimal value) {
        if (value == null) {
            return "0.0";
        }
        return value.setScale(1, RoundingMode.HALF_UP).toString();
    }

    /**
     * 格式化百分比
     */
    private String formatPercentage(BigDecimal value) {
        if (value == null) {
            return "0.0";
        }
        
        if (value.compareTo(BigDecimal.ONE) > 0) {
            return value.setScale(1, RoundingMode.HALF_UP).toString();
        } else {
            return value.multiply(BigDecimal.valueOf(100)).setScale(1, RoundingMode.HALF_UP).toString();
        }
    }

    // 默认数据创建方法

    /**
     * 创建空的概览数据
     */
    private AppPlayerCareerOverviewRespVO createEmptyOverview() {
        AppPlayerCareerOverviewRespVO response = new AppPlayerCareerOverviewRespVO();
        response.setCurrentSeason(getCurrentSeason());
        response.setAvailableSeasons(buildAvailableSeasons(getCurrentSeason()));
        response.setCoreStats(createDefaultCoreStats());
        response.setRadarChart(createDefaultRadarChart());
        return response;
    }

    /**
     * 创建默认核心统计数据
     */
    private AppPlayerCareerOverviewRespVO.CoreStatsData createDefaultCoreStats() {
        AppPlayerCareerOverviewRespVO.CoreStatsData coreStats = new AppPlayerCareerOverviewRespVO.CoreStatsData();
        coreStats.setBasicStats(createDefaultBasicStats());
        coreStats.setShootingStats(createDefaultShootingStats());
        coreStats.setAdvancedStats(createDefaultAdvancedStats());
        coreStats.setStreakStats(createDefaultStreakStats());
        return coreStats;
    }

    /**
     * 创建默认雷达图数据
     */
    private AppPlayerCareerOverviewRespVO.RadarChartData createDefaultRadarChart() {
        AppPlayerCareerOverviewRespVO.RadarChartData radarChart = new AppPlayerCareerOverviewRespVO.RadarChartData();
        radarChart.setDataPoints(createDefaultRadarDataPoints());
        radarChart.setOverallRating(BigDecimal.valueOf(100.0));
        radarChart.setSampleGames(0);
        radarChart.setRatingDescription("暂无比赛数据");
        return radarChart;
    }

    /**
     * 创建默认基础统计数据
     */
    private List<AppPlayerCareerOverviewRespVO.StatItem> createDefaultBasicStats() {
        return Arrays.asList(
            createStatItem("场均得分", "0.0", "分", "number"),
            createStatItem("场均篮板", "0.0", "个", "number"),
            createStatItem("场均助攻", "0.0", "次", "number"),
            createStatItem("场均抢断", "0.0", "次", "number"),
            createStatItem("场均盖帽", "0.0", "次", "number"),
            createStatItem("场均失误", "0.0", "次", "number")
        );
    }

    /**
     * 创建默认命中率统计数据
     */
    private List<AppPlayerCareerOverviewRespVO.StatItem> createDefaultShootingStats() {
        return Arrays.asList(
            createStatItem("投篮命中率", "0.0", "%", "percentage"),
            createStatItem("三分命中率", "0.0", "%", "percentage"),
            createStatItem("二分命中率", "0.0", "%", "percentage"),
            createStatItem("罚球命中率", "0.0", "%", "percentage")
        );
    }

    /**
     * 创建默认高阶统计数据
     */
    private List<AppPlayerCareerOverviewRespVO.StatItem> createDefaultAdvancedStats() {
        return Arrays.asList(
            createStatItem("效率值", "0.0", "", "number"),
            createStatItem("真实命中率", "0.0", "%", "percentage"),
            createStatItem("使用率", "0.0", "%", "percentage"),
            createStatItem("净胜分", "0.0", "", "number")
        );
    }

    /**
     * 创建默认连胜统计数据
     */
    private List<AppPlayerCareerOverviewRespVO.StatItem> createDefaultStreakStats() {
        return Arrays.asList(
            createStatItem("当前连胜", "0", "场", "number"),
            createStatItem("最大连胜", "0", "场", "number"),
            createStatItem("总场次", "0", "场", "number"),
            createStatItem("胜率", "0.0", "%", "percentage")
        );
    }

    /**
     * 创建默认雷达图数据点
     */
    private List<AppPlayerCareerOverviewRespVO.RadarDataPoint> createDefaultRadarDataPoints() {
        return Arrays.asList(
            createRadarDataPoint("得分", BigDecimal.valueOf(100), BigDecimal.valueOf(200), "场均得分能力评估"),
            createRadarDataPoint("篮板", BigDecimal.valueOf(100), BigDecimal.valueOf(200), "场均篮板能力评估"),
            createRadarDataPoint("助攻", BigDecimal.valueOf(100), BigDecimal.valueOf(200), "场均助攻能力评估"),
            createRadarDataPoint("盖帽", BigDecimal.valueOf(100), BigDecimal.valueOf(200), "场均盖帽能力评估"),
            createRadarDataPoint("抢断", BigDecimal.valueOf(100), BigDecimal.valueOf(200), "场均抢断能力评估")
        );
    }

    /**
     * 创建默认联盟平均数据
     */
    private LeagueAverageStats createDefaultLeagueAverageStats() {
        LeagueAverageStats defaultStats = new LeagueAverageStats();
        defaultStats.setAvgPoints(BigDecimal.valueOf(15.0));
        defaultStats.setAvgRebounds(BigDecimal.valueOf(6.0));
        defaultStats.setAvgAssists(BigDecimal.valueOf(4.0));
        defaultStats.setAvgBlocks(BigDecimal.valueOf(0.8));
        defaultStats.setAvgSteals(BigDecimal.valueOf(1.2));
        return defaultStats;
    }

    /**
     * 联盟平均数据内部类
     */
    private static class LeagueAverageStats {
        private BigDecimal avgPoints;
        private BigDecimal avgRebounds;
        private BigDecimal avgAssists;
        private BigDecimal avgBlocks;
        private BigDecimal avgSteals;

        public BigDecimal getAvgPoints() { return avgPoints; }
        public void setAvgPoints(BigDecimal avgPoints) { this.avgPoints = avgPoints; }

        public BigDecimal getAvgRebounds() { return avgRebounds; }
        public void setAvgRebounds(BigDecimal avgRebounds) { this.avgRebounds = avgRebounds; }

        public BigDecimal getAvgAssists() { return avgAssists; }
        public void setAvgAssists(BigDecimal avgAssists) { this.avgAssists = avgAssists; }

        public BigDecimal getAvgBlocks() { return avgBlocks; }
        public void setAvgBlocks(BigDecimal avgBlocks) { this.avgBlocks = avgBlocks; }

        public BigDecimal getAvgSteals() { return avgSteals; }
        public void setAvgSteals(BigDecimal avgSteals) { this.avgSteals = avgSteals; }
    }

    private LeagueBestStats createDefaultLeagueBestStats() {
        LeagueBestStats defaultStats = new LeagueBestStats();
        defaultStats.setBestAvgPoints(BigDecimal.valueOf(25.0));
        defaultStats.setBestAvgRebounds(BigDecimal.valueOf(12.0));
        defaultStats.setBestAvgAssists(BigDecimal.valueOf(10.0));
        defaultStats.setBestAvgBlocks(BigDecimal.valueOf(3.0));
        defaultStats.setBestAvgSteals(BigDecimal.valueOf(3.0));
        return defaultStats;
    }

    /**
     * 联盟最佳数据统计类
     */
    private static class LeagueBestStats {
        private BigDecimal bestAvgPoints;
        private BigDecimal bestAvgRebounds;
        private BigDecimal bestAvgAssists;
        private BigDecimal bestAvgBlocks;
        private BigDecimal bestAvgSteals;

        public BigDecimal getBestAvgPoints() { return bestAvgPoints; }
        public void setBestAvgPoints(BigDecimal bestAvgPoints) { this.bestAvgPoints = bestAvgPoints; }

        public BigDecimal getBestAvgRebounds() { return bestAvgRebounds; }
        public void setBestAvgRebounds(BigDecimal bestAvgRebounds) { this.bestAvgRebounds = bestAvgRebounds; }

        public BigDecimal getBestAvgAssists() { return bestAvgAssists; }
        public void setBestAvgAssists(BigDecimal bestAvgAssists) { this.bestAvgAssists = bestAvgAssists; }

        public BigDecimal getBestAvgBlocks() { return bestAvgBlocks; }
        public void setBestAvgBlocks(BigDecimal bestAvgBlocks) { this.bestAvgBlocks = bestAvgBlocks; }

        public BigDecimal getBestAvgSteals() { return bestAvgSteals; }
        public void setBestAvgSteals(BigDecimal bestAvgSteals) { this.bestAvgSteals = bestAvgSteals; }
    }
} 