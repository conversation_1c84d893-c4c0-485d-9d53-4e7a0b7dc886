package cn.iocoder.yudao.module.operation.service.career;

import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerStatisticsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerGameRelatedDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 球员统计计算器通用组件
 * 
 * 提供赛季和生涯统计计算的公共逻辑，避免代码重复
 * 
 * <AUTHOR> Assistant
 * @since 2024
 */
@Component
@Slf4j
public class PlayerStatsCalculatorCommon {

    /**
     * 统计数据聚合结果
     */
    public static class StatsAggregationResult {
        // 基础统计
        private int totalGames;
        private int validGames;
        private int totalPoints;
        private int totalRebounds;
        private int totalOffensiveRebounds;
        private int totalDefensiveRebounds;
        private int totalAssists;
        private int totalSteals;
        private int totalBlocks;
        private int totalTurnovers;
        private int totalFouls;
        private int totalFieldGoalsMade;
        private int totalFieldGoalsAttempted;
        private int totalThreePointsMade;
        private int totalThreePointsAttempted;
        private int totalTwoPointsMade;
        private int totalTwoPointsAttempted;
        private int totalFreeThrowsMade;
        private int totalFreeThrowsAttempted;
        private BigDecimal totalMinutes;
        private BigDecimal totalEfficiency;
        
        // 平均统计
        private BigDecimal avgPoints;
        private BigDecimal avgRebounds;
        private BigDecimal avgOffensiveRebounds;
        private BigDecimal avgDefensiveRebounds;
        private BigDecimal avgAssists;
        private BigDecimal avgSteals;
        private BigDecimal avgBlocks;
        private BigDecimal avgTurnovers;
        private BigDecimal avgFouls;
        private BigDecimal avgMinutes;
        private BigDecimal avgEfficiency;
        
        // 命中率统计
        private BigDecimal fieldGoalPercentage;
        private BigDecimal threePointPercentage;
        private BigDecimal twoPointPercentage;
        private BigDecimal freeThrowPercentage;
        private BigDecimal trueShootingPercentage;
        private BigDecimal assistTurnoverRatio;
        
        // Getters and Setters
        public int getTotalGames() { return totalGames; }
        public void setTotalGames(int totalGames) { this.totalGames = totalGames; }
        
        public int getValidGames() { return validGames; }
        public void setValidGames(int validGames) { this.validGames = validGames; }
        
        public int getTotalPoints() { return totalPoints; }
        public void setTotalPoints(int totalPoints) { this.totalPoints = totalPoints; }
        
        public int getTotalRebounds() { return totalRebounds; }
        public void setTotalRebounds(int totalRebounds) { this.totalRebounds = totalRebounds; }
        
        public int getTotalOffensiveRebounds() { return totalOffensiveRebounds; }
        public void setTotalOffensiveRebounds(int totalOffensiveRebounds) { this.totalOffensiveRebounds = totalOffensiveRebounds; }
        
        public int getTotalDefensiveRebounds() { return totalDefensiveRebounds; }
        public void setTotalDefensiveRebounds(int totalDefensiveRebounds) { this.totalDefensiveRebounds = totalDefensiveRebounds; }
        
        public int getTotalAssists() { return totalAssists; }
        public void setTotalAssists(int totalAssists) { this.totalAssists = totalAssists; }
        
        public int getTotalSteals() { return totalSteals; }
        public void setTotalSteals(int totalSteals) { this.totalSteals = totalSteals; }
        
        public int getTotalBlocks() { return totalBlocks; }
        public void setTotalBlocks(int totalBlocks) { this.totalBlocks = totalBlocks; }
        
        public int getTotalTurnovers() { return totalTurnovers; }
        public void setTotalTurnovers(int totalTurnovers) { this.totalTurnovers = totalTurnovers; }
        
        public int getTotalFouls() { return totalFouls; }
        public void setTotalFouls(int totalFouls) { this.totalFouls = totalFouls; }
        
        public int getTotalFieldGoalsMade() { return totalFieldGoalsMade; }
        public void setTotalFieldGoalsMade(int totalFieldGoalsMade) { this.totalFieldGoalsMade = totalFieldGoalsMade; }
        
        public int getTotalFieldGoalsAttempted() { return totalFieldGoalsAttempted; }
        public void setTotalFieldGoalsAttempted(int totalFieldGoalsAttempted) { this.totalFieldGoalsAttempted = totalFieldGoalsAttempted; }
        
        public int getTotalThreePointsMade() { return totalThreePointsMade; }
        public void setTotalThreePointsMade(int totalThreePointsMade) { this.totalThreePointsMade = totalThreePointsMade; }
        
        public int getTotalThreePointsAttempted() { return totalThreePointsAttempted; }
        public void setTotalThreePointsAttempted(int totalThreePointsAttempted) { this.totalThreePointsAttempted = totalThreePointsAttempted; }
        
        public int getTotalTwoPointsMade() { return totalTwoPointsMade; }
        public void setTotalTwoPointsMade(int totalTwoPointsMade) { this.totalTwoPointsMade = totalTwoPointsMade; }
        
        public int getTotalTwoPointsAttempted() { return totalTwoPointsAttempted; }
        public void setTotalTwoPointsAttempted(int totalTwoPointsAttempted) { this.totalTwoPointsAttempted = totalTwoPointsAttempted; }
        
        public int getTotalFreeThrowsMade() { return totalFreeThrowsMade; }
        public void setTotalFreeThrowsMade(int totalFreeThrowsMade) { this.totalFreeThrowsMade = totalFreeThrowsMade; }
        
        public int getTotalFreeThrowsAttempted() { return totalFreeThrowsAttempted; }
        public void setTotalFreeThrowsAttempted(int totalFreeThrowsAttempted) { this.totalFreeThrowsAttempted = totalFreeThrowsAttempted; }
        
        public BigDecimal getTotalMinutes() { return totalMinutes; }
        public void setTotalMinutes(BigDecimal totalMinutes) { this.totalMinutes = totalMinutes; }
        
        public BigDecimal getTotalEfficiency() { return totalEfficiency; }
        public void setTotalEfficiency(BigDecimal totalEfficiency) { this.totalEfficiency = totalEfficiency; }
        
        public BigDecimal getAvgPoints() { return avgPoints; }
        public void setAvgPoints(BigDecimal avgPoints) { this.avgPoints = avgPoints; }
        
        public BigDecimal getAvgRebounds() { return avgRebounds; }
        public void setAvgRebounds(BigDecimal avgRebounds) { this.avgRebounds = avgRebounds; }
        
        public BigDecimal getAvgOffensiveRebounds() { return avgOffensiveRebounds; }
        public void setAvgOffensiveRebounds(BigDecimal avgOffensiveRebounds) { this.avgOffensiveRebounds = avgOffensiveRebounds; }
        
        public BigDecimal getAvgDefensiveRebounds() { return avgDefensiveRebounds; }
        public void setAvgDefensiveRebounds(BigDecimal avgDefensiveRebounds) { this.avgDefensiveRebounds = avgDefensiveRebounds; }
        
        public BigDecimal getAvgAssists() { return avgAssists; }
        public void setAvgAssists(BigDecimal avgAssists) { this.avgAssists = avgAssists; }
        
        public BigDecimal getAvgSteals() { return avgSteals; }
        public void setAvgSteals(BigDecimal avgSteals) { this.avgSteals = avgSteals; }
        
        public BigDecimal getAvgBlocks() { return avgBlocks; }
        public void setAvgBlocks(BigDecimal avgBlocks) { this.avgBlocks = avgBlocks; }
        
        public BigDecimal getAvgTurnovers() { return avgTurnovers; }
        public void setAvgTurnovers(BigDecimal avgTurnovers) { this.avgTurnovers = avgTurnovers; }
        
        public BigDecimal getAvgFouls() { return avgFouls; }
        public void setAvgFouls(BigDecimal avgFouls) { this.avgFouls = avgFouls; }
        
        public BigDecimal getAvgMinutes() { return avgMinutes; }
        public void setAvgMinutes(BigDecimal avgMinutes) { this.avgMinutes = avgMinutes; }
        
        public BigDecimal getAvgEfficiency() { return avgEfficiency; }
        public void setAvgEfficiency(BigDecimal avgEfficiency) { this.avgEfficiency = avgEfficiency; }
        
        public BigDecimal getFieldGoalPercentage() { return fieldGoalPercentage; }
        public void setFieldGoalPercentage(BigDecimal fieldGoalPercentage) { this.fieldGoalPercentage = fieldGoalPercentage; }
        
        public BigDecimal getThreePointPercentage() { return threePointPercentage; }
        public void setThreePointPercentage(BigDecimal threePointPercentage) { this.threePointPercentage = threePointPercentage; }
        
        public BigDecimal getTwoPointPercentage() { return twoPointPercentage; }
        public void setTwoPointPercentage(BigDecimal twoPointPercentage) { this.twoPointPercentage = twoPointPercentage; }
        
        public BigDecimal getFreeThrowPercentage() { return freeThrowPercentage; }
        public void setFreeThrowPercentage(BigDecimal freeThrowPercentage) { this.freeThrowPercentage = freeThrowPercentage; }
        
        public BigDecimal getTrueShootingPercentage() { return trueShootingPercentage; }
        public void setTrueShootingPercentage(BigDecimal trueShootingPercentage) { this.trueShootingPercentage = trueShootingPercentage; }
        
        public BigDecimal getAssistTurnoverRatio() { return assistTurnoverRatio; }
        public void setAssistTurnoverRatio(BigDecimal assistTurnoverRatio) { this.assistTurnoverRatio = assistTurnoverRatio; }
    }

    /**
     * 连胜数据结果
     */
    public static class StreakDataResult {
        private int currentStreak;
        private int maxWinStreak;
        private int maxLoseStreak;
        private LocalDate streakStartDate;
        private int totalWins;
        private int totalLosses;
        private BigDecimal winRate;
        
        // Getters and Setters
        public int getCurrentStreak() { return currentStreak; }
        public void setCurrentStreak(int currentStreak) { this.currentStreak = currentStreak; }
        
        public int getMaxWinStreak() { return maxWinStreak; }
        public void setMaxWinStreak(int maxWinStreak) { this.maxWinStreak = maxWinStreak; }
        
        public int getMaxLoseStreak() { return maxLoseStreak; }
        public void setMaxLoseStreak(int maxLoseStreak) { this.maxLoseStreak = maxLoseStreak; }
        
        public LocalDate getStreakStartDate() { return streakStartDate; }
        public void setStreakStartDate(LocalDate streakStartDate) { this.streakStartDate = streakStartDate; }
        
        public int getTotalWins() { return totalWins; }
        public void setTotalWins(int totalWins) { this.totalWins = totalWins; }
        
        public int getTotalLosses() { return totalLosses; }
        public void setTotalLosses(int totalLosses) { this.totalLosses = totalLosses; }
        
        public BigDecimal getWinRate() { return winRate; }
        public void setWinRate(BigDecimal winRate) { this.winRate = winRate; }
    }

    /**
     * 聚合基础统计数据
     * 
     * @param gameStats 比赛统计数据列表
     * @return 聚合结果
     */
    public StatsAggregationResult aggregateStats(List<PlayerStatisticsDO> gameStats) {
        if (gameStats == null || gameStats.isEmpty()) {
            return createEmptyAggregationResult();
        }

        StatsAggregationResult result = new StatsAggregationResult();

        // 计算实际比赛场次（去重gameId）
        Set<Long> uniqueGameIds = gameStats.stream()
                .map(PlayerStatisticsDO::getGameId)
                .collect(Collectors.toSet());
        int totalGames = uniqueGameIds.size();

        // 计算有效比赛场次（有得分数据的比赛）
        Set<Long> validGameIds = gameStats.stream()
                .filter(stat -> stat.getPoints() != null && stat.getPoints() > 0)
                .map(PlayerStatisticsDO::getGameId)
                .collect(Collectors.toSet());
        int validGames = validGameIds.size();
        int totalPoints = 0;
        int totalOffensiveRebounds = 0;
        int totalDefensiveRebounds = 0;
        int totalAssists = 0;
        int totalSteals = 0;
        int totalBlocks = 0;
        int totalTurnovers = 0;
        int totalFouls = 0;
        int totalTwoPointsMade = 0;
        int totalTwoPointsAttempted = 0;
        int totalThreePointsMade = 0;
        int totalThreePointsAttempted = 0;
        int totalFreeThrowsMade = 0;
        int totalFreeThrowsAttempted = 0;
        BigDecimal totalMinutes = BigDecimal.ZERO;
        BigDecimal totalEfficiency = BigDecimal.ZERO;

        // 遍历所有比赛数据进行累加
        for (PlayerStatisticsDO gameStat : gameStats) {
            if (gameStat.getPoints() != null) {
                totalPoints += gameStat.getPoints();
            }
            
            if (gameStat.getOffensiveRebounds() != null) {
                totalOffensiveRebounds += gameStat.getOffensiveRebounds();
            }
            if (gameStat.getDefensiveRebounds() != null) {
                totalDefensiveRebounds += gameStat.getDefensiveRebounds();
            }
            if (gameStat.getAssists() != null) {
                totalAssists += gameStat.getAssists();
            }
            if (gameStat.getSteals() != null) {
                totalSteals += gameStat.getSteals();
            }
            if (gameStat.getBlocks() != null) {
                totalBlocks += gameStat.getBlocks();
            }
            if (gameStat.getTurnovers() != null) {
                totalTurnovers += gameStat.getTurnovers();
            }
            if (gameStat.getFouls() != null) {
                totalFouls += gameStat.getFouls();
            }
            if (gameStat.getTwoPointMakes() != null) {
                totalTwoPointsMade += gameStat.getTwoPointMakes();
            }
            if (gameStat.getTwoPointAttempts() != null) {
                totalTwoPointsAttempted += gameStat.getTwoPointAttempts();
            }
            if (gameStat.getThreePointMakes() != null) {
                totalThreePointsMade += gameStat.getThreePointMakes();
            }
            if (gameStat.getThreePointAttempts() != null) {
                totalThreePointsAttempted += gameStat.getThreePointAttempts();
            }
            if (gameStat.getFreeThrowMakes() != null) {
                totalFreeThrowsMade += gameStat.getFreeThrowMakes();
            }
            if (gameStat.getFreeThrowAttempts() != null) {
                totalFreeThrowsAttempted += gameStat.getFreeThrowAttempts();
            }
            
            // 上场时间转换为分钟
            if (gameStat.getPlayingTime() != null) {
                BigDecimal minutes = BigDecimal.valueOf(gameStat.getPlayingTime()).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP);
                totalMinutes = totalMinutes.add(minutes);
            }
            
            if (gameStat.getEfficiency() != null) {
                totalEfficiency = totalEfficiency.add(BigDecimal.valueOf(gameStat.getEfficiency()));
            }
        }

        // 计算总投篮数据
        int totalFieldGoalsMade = totalTwoPointsMade + totalThreePointsMade;
        int totalFieldGoalsAttempted = totalTwoPointsAttempted + totalThreePointsAttempted;
        int totalRebounds = totalOffensiveRebounds + totalDefensiveRebounds;

        // 设置总计数据
        result.setTotalGames(totalGames);
        result.setValidGames(validGames);
        result.setTotalPoints(totalPoints);
        result.setTotalRebounds(totalRebounds);
        result.setTotalOffensiveRebounds(totalOffensiveRebounds);
        result.setTotalDefensiveRebounds(totalDefensiveRebounds);
        result.setTotalAssists(totalAssists);
        result.setTotalSteals(totalSteals);
        result.setTotalBlocks(totalBlocks);
        result.setTotalTurnovers(totalTurnovers);
        result.setTotalFouls(totalFouls);
        result.setTotalFieldGoalsMade(totalFieldGoalsMade);
        result.setTotalFieldGoalsAttempted(totalFieldGoalsAttempted);
        result.setTotalTwoPointsMade(totalTwoPointsMade);
        result.setTotalTwoPointsAttempted(totalTwoPointsAttempted);
        result.setTotalThreePointsMade(totalThreePointsMade);
        result.setTotalThreePointsAttempted(totalThreePointsAttempted);
        result.setTotalFreeThrowsMade(totalFreeThrowsMade);
        result.setTotalFreeThrowsAttempted(totalFreeThrowsAttempted);
        result.setTotalMinutes(totalMinutes);
        result.setTotalEfficiency(totalEfficiency);

        // 计算平均数据
        calculateAverageStats(result);
        
        // 计算命中率
        calculateShootingPercentages(result);
        
        // 计算高阶统计
        calculateAdvancedStats(result);

        return result;
    }

    /**
     * 计算连胜数据
     * 
     * @param gameResults 比赛结果列表
     * @return 连胜数据结果
     */
    public StreakDataResult calculateStreakData(List<PlayerGameRelatedDO> gameResults) {
        StreakDataResult result = new StreakDataResult();
        
        if (gameResults == null || gameResults.isEmpty()) {
            // 设置默认值
            result.setCurrentStreak(0);
            result.setMaxWinStreak(0);
            result.setMaxLoseStreak(0);
            result.setTotalWins(0);
            result.setTotalLosses(0);
            result.setWinRate(BigDecimal.ZERO);
            return result;
        }

        int currentStreak = 0;
        int maxWinStreak = 0;
        int maxLoseStreak = 0;
        int totalWins = 0;
        int totalLosses = 0;
        LocalDate streakStartDate = null;
        int tempWinStreak = 0;
        int tempLoseStreak = 0;

        // 遍历比赛结果计算连胜数据
        for (PlayerGameRelatedDO gameResult : gameResults) {
            // TODO: 实际实现需要根据比赛结果判断胜负
            // 这里暂时使用简化逻辑
            boolean isWin = determineGameResult(gameResult);
            
            if (isWin) {
                totalWins++;
                tempWinStreak++;
                tempLoseStreak = 0;
                maxWinStreak = Math.max(maxWinStreak, tempWinStreak);
                currentStreak = tempWinStreak;
                if (tempWinStreak == 1) {
                    streakStartDate = gameResult.getCreateTime().toLocalDate();
                }
            } else {
                totalLosses++;
                tempLoseStreak++;
                tempWinStreak = 0;
                maxLoseStreak = Math.max(maxLoseStreak, tempLoseStreak);
                currentStreak = -tempLoseStreak; // 负数表示连败
                if (tempLoseStreak == 1) {
                    streakStartDate = gameResult.getCreateTime().toLocalDate();
                }
            }
        }

        // 设置连胜数据
        result.setCurrentStreak(currentStreak);
        result.setMaxWinStreak(maxWinStreak);
        result.setMaxLoseStreak(maxLoseStreak);
        result.setStreakStartDate(streakStartDate);
        result.setTotalWins(totalWins);
        result.setTotalLosses(totalLosses);

        // 计算胜率 - 存储为小数格式（0-1），前端显示时再乘以100
        int totalGames = totalWins + totalLosses;
        if (totalGames > 0) {
            BigDecimal winRate = BigDecimal.valueOf(totalWins)
                    .divide(BigDecimal.valueOf(totalGames), 4, RoundingMode.HALF_UP);
            result.setWinRate(winRate);
        } else {
            result.setWinRate(BigDecimal.ZERO);
        }

        return result;
    }

    /**
     * 计算平均统计
     */
    private void calculateAverageStats(StatsAggregationResult result) {
        int validGames = result.getValidGames();
        if (validGames == 0) {
            // 设置默认值
            result.setAvgPoints(BigDecimal.ZERO);
            result.setAvgRebounds(BigDecimal.ZERO);
            result.setAvgOffensiveRebounds(BigDecimal.ZERO);
            result.setAvgDefensiveRebounds(BigDecimal.ZERO);
            result.setAvgAssists(BigDecimal.ZERO);
            result.setAvgSteals(BigDecimal.ZERO);
            result.setAvgBlocks(BigDecimal.ZERO);
            result.setAvgTurnovers(BigDecimal.ZERO);
            result.setAvgFouls(BigDecimal.ZERO);
            result.setAvgMinutes(BigDecimal.ZERO);
            result.setAvgEfficiency(BigDecimal.ZERO);
            return;
        }

        result.setAvgPoints(BigDecimal.valueOf(result.getTotalPoints()).divide(BigDecimal.valueOf(validGames), 2, RoundingMode.HALF_UP));
        result.setAvgRebounds(BigDecimal.valueOf(result.getTotalRebounds()).divide(BigDecimal.valueOf(validGames), 2, RoundingMode.HALF_UP));
        result.setAvgOffensiveRebounds(BigDecimal.valueOf(result.getTotalOffensiveRebounds()).divide(BigDecimal.valueOf(validGames), 2, RoundingMode.HALF_UP));
        result.setAvgDefensiveRebounds(BigDecimal.valueOf(result.getTotalDefensiveRebounds()).divide(BigDecimal.valueOf(validGames), 2, RoundingMode.HALF_UP));
        result.setAvgAssists(BigDecimal.valueOf(result.getTotalAssists()).divide(BigDecimal.valueOf(validGames), 2, RoundingMode.HALF_UP));
        result.setAvgSteals(BigDecimal.valueOf(result.getTotalSteals()).divide(BigDecimal.valueOf(validGames), 2, RoundingMode.HALF_UP));
        result.setAvgBlocks(BigDecimal.valueOf(result.getTotalBlocks()).divide(BigDecimal.valueOf(validGames), 2, RoundingMode.HALF_UP));
        result.setAvgTurnovers(BigDecimal.valueOf(result.getTotalTurnovers()).divide(BigDecimal.valueOf(validGames), 2, RoundingMode.HALF_UP));
        result.setAvgFouls(BigDecimal.valueOf(result.getTotalFouls()).divide(BigDecimal.valueOf(validGames), 2, RoundingMode.HALF_UP));
        result.setAvgMinutes(result.getTotalMinutes().divide(BigDecimal.valueOf(validGames), 2, RoundingMode.HALF_UP));
        result.setAvgEfficiency(result.getTotalEfficiency().divide(BigDecimal.valueOf(validGames), 2, RoundingMode.HALF_UP));
    }

    /**
     * 计算命中率 - 存储为小数格式（0-1），前端显示时再乘以100
     */
    private void calculateShootingPercentages(StatsAggregationResult result) {
        // 总投篮命中率
        if (result.getTotalFieldGoalsAttempted() > 0) {
            BigDecimal fgPercentage = BigDecimal.valueOf(result.getTotalFieldGoalsMade())
                    .divide(BigDecimal.valueOf(result.getTotalFieldGoalsAttempted()), 4, RoundingMode.HALF_UP);
            result.setFieldGoalPercentage(fgPercentage);
        } else {
            result.setFieldGoalPercentage(BigDecimal.ZERO);
        }

        // 三分命中率
        if (result.getTotalThreePointsAttempted() > 0) {
            BigDecimal threePointPercentage = BigDecimal.valueOf(result.getTotalThreePointsMade())
                    .divide(BigDecimal.valueOf(result.getTotalThreePointsAttempted()), 4, RoundingMode.HALF_UP);
            result.setThreePointPercentage(threePointPercentage);
        } else {
            result.setThreePointPercentage(BigDecimal.ZERO);
        }

        // 二分命中率
        if (result.getTotalTwoPointsAttempted() > 0) {
            BigDecimal twoPointPercentage = BigDecimal.valueOf(result.getTotalTwoPointsMade())
                    .divide(BigDecimal.valueOf(result.getTotalTwoPointsAttempted()), 4, RoundingMode.HALF_UP);
            result.setTwoPointPercentage(twoPointPercentage);
        } else {
            result.setTwoPointPercentage(BigDecimal.ZERO);
        }

        // 罚球命中率
        if (result.getTotalFreeThrowsAttempted() > 0) {
            BigDecimal ftPercentage = BigDecimal.valueOf(result.getTotalFreeThrowsMade())
                    .divide(BigDecimal.valueOf(result.getTotalFreeThrowsAttempted()), 4, RoundingMode.HALF_UP);
            result.setFreeThrowPercentage(ftPercentage);
        } else {
            result.setFreeThrowPercentage(BigDecimal.ZERO);
        }
    }

    /**
     * 计算高阶统计
     */
    private void calculateAdvancedStats(StatsAggregationResult result) {
        // 真实命中率 (TS%) = Points / (2 * (FGA + 0.44 * FTA))
        int totalFGA = result.getTotalFieldGoalsAttempted();
        int totalFTA = result.getTotalFreeThrowsAttempted();
        int totalPoints = result.getTotalPoints();
        
        if (totalFGA > 0 || totalFTA > 0) {
            double denominator = 2 * (totalFGA + 0.44 * totalFTA);
            if (denominator > 0) {
                BigDecimal tsPercentage = BigDecimal.valueOf(totalPoints)
                        .divide(BigDecimal.valueOf(denominator), 4, RoundingMode.HALF_UP);
                result.setTrueShootingPercentage(tsPercentage);
                log.debug("计算真实命中率 - 得分: {}, 总投篮: {}, 罚球: {}, TS%: {}", 
                        totalPoints, totalFGA, totalFTA, tsPercentage);
            } else {
                result.setTrueShootingPercentage(BigDecimal.ZERO);
                log.warn("真实命中率计算异常 - 分母为0");
            }
        } else {
            result.setTrueShootingPercentage(BigDecimal.ZERO);
        }

        // 助攻失误比
        if (result.getTotalTurnovers() > 0) {
            BigDecimal assistTurnoverRatio = BigDecimal.valueOf(result.getTotalAssists())
                    .divide(BigDecimal.valueOf(result.getTotalTurnovers()), 2, RoundingMode.HALF_UP);
            result.setAssistTurnoverRatio(assistTurnoverRatio);
        } else {
            result.setAssistTurnoverRatio(BigDecimal.ZERO);
        }
    }

    /**
     * 创建空的聚合结果
     */
    private StatsAggregationResult createEmptyAggregationResult() {
        StatsAggregationResult result = new StatsAggregationResult();
        result.setTotalGames(0);
        result.setValidGames(0);
        result.setTotalPoints(0);
        result.setTotalRebounds(0);
        result.setTotalOffensiveRebounds(0);
        result.setTotalDefensiveRebounds(0);
        result.setTotalAssists(0);
        result.setTotalSteals(0);
        result.setTotalBlocks(0);
        result.setTotalTurnovers(0);
        result.setTotalFouls(0);
        result.setTotalFieldGoalsMade(0);
        result.setTotalFieldGoalsAttempted(0);
        result.setTotalTwoPointsMade(0);
        result.setTotalTwoPointsAttempted(0);
        result.setTotalThreePointsMade(0);
        result.setTotalThreePointsAttempted(0);
        result.setTotalFreeThrowsMade(0);
        result.setTotalFreeThrowsAttempted(0);
        result.setTotalMinutes(BigDecimal.ZERO);
        result.setTotalEfficiency(BigDecimal.ZERO);
        
        // 设置默认的平均值和百分比
        result.setAvgPoints(BigDecimal.ZERO);
        result.setAvgRebounds(BigDecimal.ZERO);
        result.setAvgOffensiveRebounds(BigDecimal.ZERO);
        result.setAvgDefensiveRebounds(BigDecimal.ZERO);
        result.setAvgAssists(BigDecimal.ZERO);
        result.setAvgSteals(BigDecimal.ZERO);
        result.setAvgBlocks(BigDecimal.ZERO);
        result.setAvgTurnovers(BigDecimal.ZERO);
        result.setAvgFouls(BigDecimal.ZERO);
        result.setAvgMinutes(BigDecimal.ZERO);
        result.setAvgEfficiency(BigDecimal.ZERO);
        result.setFieldGoalPercentage(BigDecimal.ZERO);
        result.setThreePointPercentage(BigDecimal.ZERO);
        result.setTwoPointPercentage(BigDecimal.ZERO);
        result.setFreeThrowPercentage(BigDecimal.ZERO);
        result.setTrueShootingPercentage(BigDecimal.ZERO);
        result.setAssistTurnoverRatio(BigDecimal.ZERO);
        
        return result;
    }

    /**
     * 判断比赛结果（简化实现）
     */
    private boolean determineGameResult(PlayerGameRelatedDO gameResult) {
        // TODO: 实际实现需要根据比赛结果和球队得分判断胜负
        // 这里暂时返回随机结果
        return gameResult.getId() % 2 == 0;
    }
} 