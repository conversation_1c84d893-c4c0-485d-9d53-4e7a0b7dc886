package cn.iocoder.yudao.module.operation.service.registration;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.operation.controller.admin.activity.vo.ActivityRegistrationDetailRespVO;
import cn.iocoder.yudao.module.operation.controller.app.registration.vo.AppRegistrationCreateReqVO;
import cn.iocoder.yudao.module.operation.controller.app.registration.vo.AppRegistrationDetailRespVO;
import cn.iocoder.yudao.module.operation.controller.app.registration.vo.AppRegistrationPageItemRespVO;
import cn.iocoder.yudao.module.operation.controller.app.registration.vo.AppRegistrationPageReqVO;
import cn.iocoder.yudao.module.operation.controller.app.registration.vo.AppRegistrationRespVO;
import cn.iocoder.yudao.module.operation.controller.app.registration.vo.AppRegistrationSettlementReqVO;
import cn.iocoder.yudao.module.operation.controller.app.registration.vo.AppRegistrationSettlementRespVO;
import cn.iocoder.yudao.module.pay.api.notify.dto.PayOrderNotifyReqDTO;
import cn.iocoder.yudao.module.operation.controller.app.registration.vo.AppWaitlistItemRespVO;

import java.util.List;

/**
 * 活动报名 Service 接口
 *
 * <AUTHOR>
 */
public interface RegistrationService {

    /**
     * 计算报名结算信息
     * 
     * 包括活动信息和预计支付金额
     * 
     * @param userId 用户ID
     * @param settlementReqVO 结算请求
     * @return 结算信息
     */
    AppRegistrationSettlementRespVO settlementRegistration(Long userId, AppRegistrationSettlementReqVO settlementReqVO);

    /**
     * 创建活动报名（核心流程）
     * <p>
     * 1. 校验活动状态、报名资格等。
     * 2. 计算费用。
     * 3. 创建报名记录 (状态：待支付)。
     * 4. 调用支付模块创建支付订单。
     *
     * @param userId      用户编号
     * @param createReqVO 创建信息
     * @return 报名响应信息，包含报名 ID 和支付相关信息
     */
    AppRegistrationRespVO createRegistration(Long userId, AppRegistrationCreateReqVO createReqVO);

    /**
     * 处理支付成功的回调
     *
     * @param notifyReqDTO 回调数据
     */
    void handlePaySuccess(PayOrderNotifyReqDTO notifyReqDTO);

    /**
     * 取消报名
     * 
     * @param id 报名ID
     * @param userId 用户ID，用于权限校验
     * @param cancelReason 取消原因（可选）
     */
    void cancelRegistration(Long id, Long userId, String cancelReason);

    /**
     * 管理员取消报名
     * 
     * @param registrationId 报名ID
     * @param operatorId 操作员ID（管理员ID）
     * @param cancelReason 取消原因（可选）
     */
    void adminCancelRegistration(Long registrationId, Long operatorId, String cancelReason);
    

    /**
     * 获得报名详情
     *
     * @param id 报名记录ID
     * @param userId 用户ID，用于权限校验
     * @return 报名详情
     */
    AppRegistrationDetailRespVO getRegistrationDetail( Long id, Long userId);

    /**
     * 发起好友组队报名
     * <p>
     * 用户发起好友组队报名，完成自己的报名后，创建好友组并成为队长
     *
     * @param userId      用户ID
     * @param createReqVO 创建信息
     * @return 报名响应信息，包含报名ID、好友组ID和支付相关信息
     */
    AppRegistrationRespVO createGroupRegistration(Long userId, AppRegistrationCreateReqVO createReqVO);

    /**
     * 加入好友组队报名
     * <p>
     * 用户通过邀请码加入好友组队，并完成自己的报名
     *
     * @param userId         用户ID
     * @param createReqVO    创建信息
     * @param invitationCode 好友组邀请码
     * @return 报名响应信息，包含报名ID和支付相关信息
     */
    AppRegistrationRespVO joinGroupRegistration(Long userId, AppRegistrationCreateReqVO createReqVO, String invitationCode);

    /**
     * 检查用户是否已加入某活动的好友组
     * 
     * @param userId 用户ID
     * @param activityId 活动ID
     * @return 好友组ID，如果未加入则返回null
     */
    Long getUserFriendGroupId( Long userId, Long activityId);
    

    /**
     * 获取活动报名详情（管理后台用）
     * 
     * @param activityId 活动ID
     * @return 活动报名详情，包含统计信息和报名列表
     */
    ActivityRegistrationDetailRespVO getActivityRegistrationDetail(Long activityId);

    /**
     * 取消超时未支付的报名记录
     * 
     * 定时任务调用，用于处理支付订单已关闭但报名状态仍为待支付的记录：
     * 1. 查询支付订单已关闭但报名状态仍为待支付的记录
     * 2. 将这些报名记录状态更新为已取消
     * 3. 将支付状态更新为支付关闭
     * 
     * @return 处理的记录数量
     */
    int cancelTimeoutRegistrations();

} 