package cn.iocoder.yudao.module.operation.service.career;

import cn.iocoder.yudao.module.operation.service.career.event.BatchGameResultEvent;
import cn.iocoder.yudao.module.operation.service.career.event.GameResultEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 比赛结果事件监听器
 * 
 * 当比赛结束时，自动更新相关球员的生涯统计数据
 * 
 * <AUTHOR> Assistant
 * @since 2024
 */
@Component
@Slf4j
public class GameResultEventListener {

    @Resource
    private PlayerCareerStatsService playerCareerStatsService;
    
    @Resource
    private RadarChartCacheService radarChartCacheService;

    /**
     * 处理比赛结果事件
     * 异步更新球员生涯统计数据
     * 
     * @param event 比赛结果事件
     */
    @EventListener
    @Async("gameResultTaskExecutor")
    public void handleGameResult(GameResultEvent event) {
        try {
            log.info("🎯 收到比赛结果事件，开始更新球员生涯数据: gameId={}, playerId={}", 
                    event.getGameId(), event.getPlayerId());

            // 强制重新计算并更新球员的生涯统计数据
            playerCareerStatsService.forceRecalculateCareerStats(event.getPlayerId());
            
            // 清除该球员的雷达图缓存
            radarChartCacheService.evictPlayerRadarChartCache(event.getPlayerId());
            
            // 清除联盟最佳数据缓存（因为可能有新的最佳数据）
            radarChartCacheService.evictLeagueBestStatsCache();
            
            log.info("✅ 球员 {} 生涯数据更新完成，缓存已清除", event.getPlayerId());

        } catch (Exception e) {
            log.error("❌ 处理比赛结果事件失败: gameId={}, playerId={}", 
                    event.getGameId(), event.getPlayerId(), e);
        }
    }

    /**
     * 批量处理比赛结果事件
     * 当一场比赛有多个球员时使用
     * 
     * @param event 批量比赛结果事件
     */
    @EventListener
    @Async("gameResultTaskExecutor")
    public void handleBatchGameResult(BatchGameResultEvent event) {
        try {
            log.info("🎯 收到批量比赛结果事件，开始批量更新球员生涯数据: gameId={}, 球员数={}", 
                    event.getGameId(), event.getPlayerIds().size());

            for (Long playerId : event.getPlayerIds()) {
                try {
                    playerCareerStatsService.forceRecalculateCareerStats(playerId);
                    
                    // 清除该球员的雷达图缓存
                    radarChartCacheService.evictPlayerRadarChartCache(playerId);
                    
                    log.debug("✅ 球员 {} 生涯数据更新完成", playerId);
                    
                    // 添加延迟避免数据库压力过大
                    Thread.sleep(50);
                } catch (Exception e) {
                    log.error("❌ 更新球员 {} 生涯数据失败", playerId, e);
                }
            }
            
            // 批量更新完成后，清除联盟最佳数据缓存
            radarChartCacheService.evictLeagueBestStatsCache();
            
            log.info("✅ 批量更新球员生涯数据完成: gameId={}, 成功更新球员数={}", 
                    event.getGameId(), event.getPlayerIds().size());

        } catch (Exception e) {
            log.error("❌ 处理批量比赛结果事件失败: gameId={}", event.getGameId(), e);
        }
    }
}