package cn.iocoder.yudao.module.operation.controller.admin.player;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.operation.controller.admin.player.vo.PlayerCareerVO;
import cn.iocoder.yudao.module.operation.controller.admin.player.vo.AbilityTrendVO;
import cn.iocoder.yudao.module.operation.service.career.PlayerCareerService;
import cn.iocoder.yudao.module.operation.service.career.PlayerCareerStatsService;
import cn.iocoder.yudao.module.operation.service.career.RadarChartCacheService;
import cn.iocoder.yudao.module.operation.service.career.dto.InitializationResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 球员生涯数据 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 球员生涯数据")
@RestController
@RequestMapping("/operation/player-career")
@Validated
@Slf4j
public class PlayerCareerController {

    @Resource
    private PlayerCareerService playerCareerService;
    
    @Resource
    private PlayerCareerStatsService playerCareerStatsService;
    
    @Resource
    private RadarChartCacheService radarChartCacheService;

    @GetMapping("/get")
    @Operation(summary = "获取球员生涯数据")
    @Parameter(name = "playerId", description = "球员ID", required = true)
    @PreAuthorize("@ss.hasPermission('operation:player:query')")
    public CommonResult<PlayerCareerVO> getPlayerCareer(@RequestParam("playerId") @NotNull Long playerId) {
        log.info("获取球员生涯数据 - 球员ID: {}", playerId);
        PlayerCareerVO careerVO = playerCareerService.getPlayerCareer(playerId);
        return success(careerVO);
    }

    @PostMapping("/refresh")
    @Operation(summary = "刷新球员生涯统计数据")
    @Parameter(name = "playerId", description = "球员ID", required = true)
    @PreAuthorize("@ss.hasPermission('operation:player:update')")
    public CommonResult<Boolean> refreshPlayerCareerStats(@RequestParam("playerId") @NotNull Long playerId) {
        log.info("刷新球员生涯统计数据 - 球员ID: {}", playerId);
        boolean success = playerCareerService.refreshPlayerCareerStats(playerId);
        return success(success);
    }

    @PostMapping("/refresh-by-game-type")
    @Operation(summary = "按比赛类型刷新球员生涯统计数据")
    @Parameter(name = "playerId", description = "球员ID", required = true)
    @Parameter(name = "gameType", description = "比赛类型", required = true)
    @PreAuthorize("@ss.hasPermission('operation:player:update')")
    public CommonResult<Boolean> refreshPlayerCareerStatsByGameType(
            @RequestParam("playerId") @NotNull Long playerId,
            @RequestParam("gameType") @NotNull Integer gameType) {
        log.info("按比赛类型刷新球员生涯统计数据 - 球员ID: {}, 比赛类型: {}", playerId, gameType);
        boolean success = playerCareerService.refreshPlayerCareerStatsByGameType(playerId, gameType);
        return success(success);
    }

    @GetMapping("/ability-trend")
    @Operation(summary = "获取球员能力值趋势")
    @Parameter(name = "playerId", description = "球员ID", required = true)
    @PreAuthorize("@ss.hasPermission('operation:player:query')")
    public CommonResult<List<AbilityTrendVO>> getPlayerAbilityTrend(@RequestParam("playerId") @NotNull Long playerId) {
        log.info("获取球员能力值趋势 - 球员ID: {}", playerId);
        List<AbilityTrendVO> trendList = playerCareerService.getPlayerAbilityTrend(playerId);
        return success(trendList);
    }

    @PostMapping("/refresh-all")
    @Operation(summary = "批量刷新所有球员生涯数据")
    @PreAuthorize("@ss.hasPermission('operation:player:update')")
    public CommonResult<Boolean> refreshAllPlayersCareerStats() {
        log.info("批量刷新所有球员生涯数据");
        boolean success = playerCareerService.refreshAllPlayersCareerStats();
        return success(success);
    }

    @PostMapping("/batch-initialize")
    @Operation(summary = "批量初始化指定球员生涯数据")
    @PreAuthorize("@ss.hasPermission('operation:player:update')")
    public CommonResult<InitializationResult> batchInitializeCareerStats(
            @Valid @RequestBody @NotEmpty List<Long> playerIds) {
        
        log.info("🚀 管理员触发批量初始化生涯数据，球员数: {}", playerIds.size());
        
        InitializationResult result = playerCareerStatsService.batchInitializeCareerStats(playerIds);
        
        // 批量初始化后清除缓存，确保数据一致性
        radarChartCacheService.evictAllCache();
        log.info("✅ 批量初始化完成后已清除所有缓存");
        
        return success(result);
    }

    @PostMapping("/force-refresh")
    @Operation(summary = "强制重新计算球员生涯数据")
    @Parameter(name = "playerId", description = "球员ID", required = true)
    @PreAuthorize("@ss.hasPermission('operation:player:update')")
    public CommonResult<Boolean> forceRefreshPlayerCareerStats(@RequestParam("playerId") @NotNull Long playerId) {
        try {
            log.info("🔧 强制重新计算球员生涯数据 - 球员ID: {}", playerId);
            playerCareerStatsService.forceRecalculateCareerStats(playerId);
            return success(true);
        } catch (Exception e) {
            log.error("❌ 强制重新计算球员生涯数据失败 - 球员ID: {}", playerId, e);
            return success(false);
        }
    }

    @PostMapping("/sync-aggregated-data")
    @Operation(summary = "同步聚合统计数据")
    @PreAuthorize("@ss.hasPermission('operation:player:update')")
    public CommonResult<Boolean> syncAggregatedData() {
        try {
            log.info("🔄 开始同步聚合统计数据");
            // 这里可以添加批量同步逻辑
            // 例如：找出所有需要更新的球员，批量刷新聚合表数据
            return success(true);
        } catch (Exception e) {
            log.error("❌ 同步聚合统计数据失败", e);
            return success(false);
        }
    }

    @PostMapping("/cache/clear-player")
    @Operation(summary = "清除指定球员的雷达图缓存")
    @Parameter(name = "playerId", description = "球员ID", required = true)
    @PreAuthorize("@ss.hasPermission('operation:player:update')")
    public CommonResult<Boolean> clearPlayerRadarChartCache(@RequestParam("playerId") @NotNull Long playerId) {
        try {
            log.info("🗑️ 清除球员雷达图缓存 - 球员ID: {}", playerId);
            radarChartCacheService.evictPlayerRadarChartCache(playerId);
            return success(true);
        } catch (Exception e) {
            log.error("❌ 清除球员雷达图缓存失败 - 球员ID: {}", playerId, e);
            return success(false);
        }
    }

    @PostMapping("/cache/clear-league-best")
    @Operation(summary = "清除联盟最佳数据缓存")
    @PreAuthorize("@ss.hasPermission('operation:player:update')")
    public CommonResult<Boolean> clearLeagueBestStatsCache() {
        try {
            log.info("🗑️ 清除联盟最佳数据缓存");
            radarChartCacheService.evictLeagueBestStatsCache();
            return success(true);
        } catch (Exception e) {
            log.error("❌ 清除联盟最佳数据缓存失败", e);
            return success(false);
        }
    }

    @PostMapping("/cache/clear-all")
    @Operation(summary = "清除所有雷达图相关缓存")
    @PreAuthorize("@ss.hasPermission('operation:player:update')")
    public CommonResult<Boolean> clearAllRadarChartCache() {
        try {
            log.info("🗑️ 清除所有雷达图相关缓存");
            radarChartCacheService.evictAllCache();
            return success(true);
        } catch (Exception e) {
            log.error("❌ 清除所有雷达图相关缓存失败", e);
            return success(false);
        }
    }

    @GetMapping("/cache/stats")
    @Operation(summary = "获取缓存统计信息")
    @PreAuthorize("@ss.hasPermission('operation:player:query')")
    public CommonResult<String> getCacheStats() {
        try {
            String stats = radarChartCacheService.getCacheStats();
            log.info("📊 缓存统计信息: {}", stats);
            return success(stats);
        } catch (Exception e) {
            log.error("❌ 获取缓存统计信息失败", e);
            return success("获取缓存统计信息失败");
        }
    }

    // ==================== 内部方法 ====================

    /**
     * 获取球员生涯数据（内部调用，不校验权限）
     * 主要用于其他模块调用
     */
    @GetMapping("/internal/get")
    @Operation(summary = "获取球员生涯数据（内部调用）", description = "内部接口，不校验权限")
    public CommonResult<PlayerCareerVO> getPlayerCareerInternal(@RequestParam("playerId") @NotNull Long playerId) {
        PlayerCareerVO careerVO = playerCareerService.getPlayerCareer(playerId);
        return success(careerVO);
    }

    /**
     * 刷新球员生涯数据（内部调用，不校验权限）
     * 主要用于比赛结束后自动刷新
     */
    @PostMapping("/internal/refresh")
    @Operation(summary = "刷新球员生涯数据（内部调用）", description = "内部接口，不校验权限")
    public CommonResult<Boolean> refreshPlayerCareerStatsInternal(@RequestParam("playerId") @NotNull Long playerId) {
        boolean success = playerCareerService.refreshPlayerCareerStats(playerId);
        return success(success);
    }
} 