package cn.iocoder.yudao.module.operation.convert.player;

import cn.iocoder.yudao.module.operation.controller.admin.player.vo.PlayerCareerVO;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerCareerStatsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerCareerBestStatsDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 球员生涯数据转换器
 *
 * <AUTHOR>
 */
@Component
public class PlayerCareerConvert {

    /**
     * 转换为球员生涯VO
     */
    public PlayerCareerVO convertToCareerVO(PlayerDO player, 
                                          List<PlayerCareerStatsDO> careerStatsList, 
                                          List<PlayerCareerBestStatsDO> careerBestStatsList) {
        PlayerCareerVO careerVO = new PlayerCareerVO();
        
        // 设置球员基础信息
        careerVO.setPlayerInfo(buildPlayerInfo(player));
        
        // 设置能力信息
        careerVO.setAbilityInfo(buildAbilityInfo(player, careerStatsList));
        
        // 设置按比赛类型统计列表
        careerVO.setGameTypeStatsList(buildGameTypeStatsList(careerStatsList, careerBestStatsList));
        
        // 设置总体统计
        careerVO.setOverallStats(buildOverallStats(careerStatsList, careerBestStatsList));
        
        return careerVO;
    }

    /**
     * 构建球员基础信息
     */
    private PlayerCareerVO.PlayerInfo buildPlayerInfo(PlayerDO player) {
        PlayerCareerVO.PlayerInfo playerInfo = new PlayerCareerVO.PlayerInfo();
        playerInfo.setId(player.getId());
        playerInfo.setName(player.getName());
        playerInfo.setAvatar(player.getAvatar());
        playerInfo.setPosition(player.getPosition());
        playerInfo.setHeight(player.getHeight() != null ? player.getHeight().intValue() : null);
        playerInfo.setWeight(player.getWeight());
        playerInfo.setBirthYear(null); // PlayerDO中没有birthYear字段
        playerInfo.setNumber(player.getNumber());
        playerInfo.setSex(player.getSex());
        playerInfo.setPrivacyHidden(false); // PlayerDO中没有privacyHidden字段，设置默认值
        
        // 设置最后比赛时间
        if (player.getUpdateTime() != null) {
            playerInfo.setLastGameTime(player.getUpdateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));
        }
        
        return playerInfo;
    }

    /**
     * 构建能力信息
     */
    private PlayerCareerVO.AbilityInfo buildAbilityInfo(PlayerDO player, List<PlayerCareerStatsDO> careerStatsList) {
        PlayerCareerVO.AbilityInfo abilityInfo = new PlayerCareerVO.AbilityInfo();
        
        // 从球员基础信息获取能力值
        abilityInfo.setDisplayRatings(player.getRatings() != null ? BigDecimal.valueOf(player.getRatings()) : BigDecimal.ZERO);
        abilityInfo.setRealRatings(player.getRatings() != null ? BigDecimal.valueOf(player.getRatings()) : BigDecimal.ZERO);
        abilityInfo.setConsecutiveWins(0); // PlayerDO中没有连胜字段，设置默认值
        abilityInfo.setConsecutiveLosses(0); // PlayerDO中没有连败字段，设置默认值
        
        // 计算赛季比赛数和胜率
        PlayerCareerStatsDO overallStats = careerStatsList.stream()
                .filter(stats -> stats.getGameType() == 0) // 0表示全部比赛类型
                .findFirst()
                .orElse(null);
                
        if (overallStats != null) {
            abilityInfo.setSeasonGames(overallStats.getGamesPlayed());
            abilityInfo.setWinRate(overallStats.getWinRate());
        } else {
            abilityInfo.setSeasonGames(0);
            abilityInfo.setWinRate(BigDecimal.ZERO);
        }
        
        // 构建7维度评分 - 暂时使用默认值，后续需要从能力评分表获取
        PlayerCareerVO.SevenDimensionRating sevenDimensionRating = new PlayerCareerVO.SevenDimensionRating();
        sevenDimensionRating.setEfficiencyScore(BigDecimal.valueOf(75));
        sevenDimensionRating.setScoringScore(BigDecimal.valueOf(70));
        sevenDimensionRating.setReboundingScore(BigDecimal.valueOf(65));
        sevenDimensionRating.setAssistingScore(BigDecimal.valueOf(60));
        sevenDimensionRating.setDefenseScore(BigDecimal.valueOf(68));
        sevenDimensionRating.setTurnoverScore(BigDecimal.valueOf(72));
        sevenDimensionRating.setFoulScore(BigDecimal.valueOf(78));
        abilityInfo.setSevenDimensionRating(sevenDimensionRating);
        
        return abilityInfo;
    }

    /**
     * 构建按比赛类型统计列表
     */
    private List<PlayerCareerVO.GameTypeStats> buildGameTypeStatsList(List<PlayerCareerStatsDO> careerStatsList, 
                                                                     List<PlayerCareerBestStatsDO> careerBestStatsList) {
        List<PlayerCareerVO.GameTypeStats> gameTypeStatsList = new ArrayList<>();
        
        // 将最佳数据按比赛类型分组
        Map<Integer, PlayerCareerBestStatsDO> bestStatsMap = careerBestStatsList.stream()
                .collect(Collectors.toMap(PlayerCareerBestStatsDO::getGameType, stats -> stats));
        
        for (PlayerCareerStatsDO careerStats : careerStatsList) {
            PlayerCareerVO.GameTypeStats gameTypeStats = new PlayerCareerVO.GameTypeStats();
            
            // 基础数据
            gameTypeStats.setGameType(careerStats.getGameType());
            gameTypeStats.setTotalGames(careerStats.getGamesPlayed());
            gameTypeStats.setWins(careerStats.getTotalWins());
            
            // 效率值和命中率
            gameTypeStats.setEfficiencyRating(BigDecimal.valueOf(85)); // TODO: 从实际计算获取
            gameTypeStats.setTrueShootingPercentage(BigDecimal.valueOf(0.55)); // TODO: 计算真实命中率
            
            // 场均数据
            gameTypeStats.setAvgPoints(careerStats.getAvgPoints());
            gameTypeStats.setAvgRebounds(careerStats.getAvgRebounds());
            gameTypeStats.setAvgAssists(careerStats.getAvgAssists());
            gameTypeStats.setAvgSteals(careerStats.getAvgSteals());
            gameTypeStats.setAvgBlocks(careerStats.getAvgBlocks());
            gameTypeStats.setAvgTurnovers(careerStats.getAvgTurnovers());
            gameTypeStats.setAvgFouls(careerStats.getAvgFouls());
            
            // 命中率
            gameTypeStats.setFgPercentage(careerStats.getFieldGoalPercentage());
            gameTypeStats.setThreePercentage(careerStats.getThreePointPercentage());
            gameTypeStats.setFtPercentage(careerStats.getFreeThrowPercentage());
            
            // 最佳数据
            PlayerCareerBestStatsDO bestStats = bestStatsMap.get(careerStats.getGameType());
            if (bestStats != null) {
                PlayerCareerVO.BestStats bestStatsVO = new PlayerCareerVO.BestStats();
                bestStatsVO.setBestPoints(bestStats.getBestPoints());
                bestStatsVO.setBestRebounds(bestStats.getBestRebounds());
                bestStatsVO.setBestAssists(bestStats.getBestAssists());
                bestStatsVO.setBestSteals(bestStats.getBestSteals());
                bestStatsVO.setBestBlocks(bestStats.getBestBlocks());
                gameTypeStats.setBestStats(bestStatsVO);
            }
            
            gameTypeStatsList.add(gameTypeStats);
        }
        
        return gameTypeStatsList;
    }

    /**
     * 构建总体统计
     */
    private PlayerCareerVO.OverallStats buildOverallStats(List<PlayerCareerStatsDO> careerStatsList, 
                                                          List<PlayerCareerBestStatsDO> careerBestStatsList) {
        PlayerCareerVO.OverallStats overallStats = new PlayerCareerVO.OverallStats();
        
        // 获取全部比赛类型的统计（gameType = 0）
        PlayerCareerStatsDO allGameStats = careerStatsList.stream()
                .filter(stats -> stats.getGameType() == 0)
                .findFirst()
                .orElse(null);

        if (allGameStats != null) {
            // 添加调试日志
            log.info("🔧 ADMIN API构建总体统计数据 - 球员ID: {}, 场均得分: {}, 总得分: {}, 总场次: {}",
                    allGameStats.getPlayerId(), allGameStats.getAvgPoints(),
                    allGameStats.getTotalPoints(), allGameStats.getGamesPlayed());

            // 基础统计
            overallStats.setTotalGames(allGameStats.getGamesPlayed());
            overallStats.setTotalWins(allGameStats.getTotalWins());
            overallStats.setTotalLosses(allGameStats.getTotalLosses());
            overallStats.setOverallWinRate(allGameStats.getWinRate());

            // 场均数据
            overallStats.setAvgPoints(allGameStats.getAvgPoints());
            overallStats.setAvgRebounds(allGameStats.getAvgRebounds());
            overallStats.setAvgAssists(allGameStats.getAvgAssists());
            overallStats.setAvgSteals(allGameStats.getAvgSteals());
            overallStats.setAvgBlocks(allGameStats.getAvgBlocks());
            overallStats.setAvgTurnovers(allGameStats.getAvgTurnovers());
            overallStats.setAvgFouls(allGameStats.getAvgFouls());
            overallStats.setAvgMinutesPlayed(allGameStats.getAvgMinutesPlayed());
            overallStats.setAvgEfficiency(allGameStats.getAvgEfficiency());
            
            // 命中率统计
            overallStats.setFgPercentage(allGameStats.getFieldGoalPercentage());
            overallStats.setThreePercentage(allGameStats.getThreePointPercentage());
            overallStats.setTwoPointPercentage(allGameStats.getTwoPointPercentage());
            overallStats.setFtPercentage(allGameStats.getFreeThrowPercentage());
            overallStats.setTrueShootingPercentage(allGameStats.getTrueShootingPercentage());
            overallStats.setEffectiveFieldGoalPercentage(allGameStats.getEffectiveFieldGoalPercentage());
            
            // 高阶数据
            overallStats.setAssistTurnoverRatio(allGameStats.getAssistTurnoverRatio());
            overallStats.setPlusMinus(allGameStats.getPlusMinus());
            overallStats.setTotalPoints(allGameStats.getTotalPoints());
            overallStats.setTotalRebounds(allGameStats.getTotalRebounds());
            overallStats.setTotalAssists(allGameStats.getTotalAssists());
        } else {
            // 如果没有总体统计，设置默认值
            overallStats.setTotalGames(0);
            overallStats.setTotalWins(0);
            overallStats.setTotalLosses(0);
            overallStats.setOverallWinRate(BigDecimal.ZERO);
            overallStats.setAvgPoints(BigDecimal.ZERO);
            overallStats.setAvgRebounds(BigDecimal.ZERO);
            overallStats.setAvgAssists(BigDecimal.ZERO);
            overallStats.setAvgSteals(BigDecimal.ZERO);
            overallStats.setAvgBlocks(BigDecimal.ZERO);
            overallStats.setAvgTurnovers(BigDecimal.ZERO);
            overallStats.setAvgFouls(BigDecimal.ZERO);
            overallStats.setAvgMinutesPlayed(BigDecimal.ZERO);
            overallStats.setAvgEfficiency(BigDecimal.ZERO);
            overallStats.setFgPercentage(BigDecimal.ZERO);
            overallStats.setThreePercentage(BigDecimal.ZERO);
            overallStats.setTwoPointPercentage(BigDecimal.ZERO);
            overallStats.setFtPercentage(BigDecimal.ZERO);
            overallStats.setTrueShootingPercentage(BigDecimal.ZERO);
            overallStats.setEffectiveFieldGoalPercentage(BigDecimal.ZERO);
            overallStats.setAssistTurnoverRatio(BigDecimal.ZERO);
            overallStats.setPlusMinus(BigDecimal.ZERO);
            overallStats.setTotalPoints(0);
            overallStats.setTotalRebounds(0);
            overallStats.setTotalAssists(0);
        }
        
        // 设置最佳数据
        PlayerCareerBestStatsDO bestStats = careerBestStatsList.stream()
                .filter(stats -> stats.getGameType() == 0)
                .findFirst()
                .orElse(null);
                
        if (bestStats != null) {
            PlayerCareerVO.BestStats bestStatsVO = new PlayerCareerVO.BestStats();
            bestStatsVO.setBestPoints(bestStats.getBestPoints());
            bestStatsVO.setBestRebounds(bestStats.getBestRebounds());
            bestStatsVO.setBestAssists(bestStats.getBestAssists());
            bestStatsVO.setBestSteals(bestStats.getBestSteals());
            bestStatsVO.setBestBlocks(bestStats.getBestBlocks());
            overallStats.setBestStats(bestStatsVO);
        }
        
        return overallStats;
    }
} 