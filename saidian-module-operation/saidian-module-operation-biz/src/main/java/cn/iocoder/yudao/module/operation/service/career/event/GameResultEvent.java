package cn.iocoder.yudao.module.operation.service.career.event;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 比赛结果事件
 * 
 * 当比赛结束时发布此事件，触发相关球员生涯数据的更新
 * 
 * <AUTHOR> Assistant
 * @since 2024
 */
@Getter
public class GameResultEvent extends ApplicationEvent {

    private final Long gameId;
    private final Long playerId;
    private final String gameResult; // "win" 或 "lose"
    private final LocalDateTime gameTime;

    public GameResultEvent(Object source, Long gameId, Long playerId, String gameResult, LocalDateTime gameTime) {
        super(source);
        this.gameId = gameId;
        this.playerId = playerId;
        this.gameResult = gameResult;
        this.gameTime = gameTime;
    }

}