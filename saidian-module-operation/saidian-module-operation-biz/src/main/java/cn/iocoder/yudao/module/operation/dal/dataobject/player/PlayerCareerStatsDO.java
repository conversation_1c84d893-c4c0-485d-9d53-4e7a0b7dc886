package cn.iocoder.yudao.module.operation.dal.dataobject.player;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 球员生涯聚合统计 DO
 *
 * <AUTHOR>
 */
@TableName("sd_player_career_stats")
@KeySequence("sd_player_career_stats_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlayerCareerStatsDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 球员ID
     */
    private Long playerId;

    /**
     * 比赛类型:0-全部,1-排位赛,2-友谊赛,3-联赛
     */
    private Integer gameType;

    // ========== 生涯特有字段 ==========
    /**
     * 参赛总赛季数
     */
    private Integer totalSeasons;

    /**
     * 生涯首场比赛日期
     */
    private LocalDate firstGameDate;

    /**
     * 生涯最近一场比赛日期
     */
    private LocalDate latestGameDate;

    // ========== 基础统计 - 总计数据 ==========
    /**
     * 参赛场次
     */
    private Integer gamesPlayed;

    /**
     * 有效统计场次（逻辑字段，数据库表中不存在）
     */
    @TableField(exist = false)
    private Integer validStatsGames;

    /**
     * 总得分
     */
    private Integer totalPoints;

    /**
     * 总篮板
     */
    private Integer totalRebounds;

    /**
     * 总进攻篮板
     */
    @TableField("total_offensive_rebounds")
    private Integer totalOffensiveRebounds;

    /**
     * 总防守篮板
     */
    @TableField("total_defensive_rebounds")
    private Integer totalDefensiveRebounds;

    /**
     * 总助攻
     */
    private Integer totalAssists;

    /**
     * 总抢断
     */
    private Integer totalSteals;

    /**
     * 总盖帽
     */
    private Integer totalBlocks;

    /**
     * 总失误
     */
    private Integer totalTurnovers;

    /**
     * 总犯规
     */
    private Integer totalFouls;

    /**
     * 总投篮命中数
     */
    @TableField("total_field_goals_made")
    private Integer totalFieldGoalsMade;

    /**
     * 总投篮出手数
     */
    @TableField("total_field_goals_attempted")
    private Integer totalFieldGoalsAttempted;

    /**
     * 总三分命中数
     */
    @TableField("total_three_points_made")
    private Integer totalThreePointsMade;

    /**
     * 总三分出手数
     */
    @TableField("total_three_points_attempted")
    private Integer totalThreePointsAttempted;

    /**
     * 总二分命中数
     */
    @TableField("total_two_points_made")
    private Integer totalTwoPointsMade;

    /**
     * 总二分出手数
     */
    @TableField("total_two_points_attempted")
    private Integer totalTwoPointsAttempted;

    /**
     * 总罚球命中数
     */
    @TableField("total_free_throws_made")
    private Integer totalFreeThrowsMade;

    /**
     * 总罚球出手数
     */
    @TableField("total_free_throws_attempted")
    private Integer totalFreeThrowsAttempted;

    /**
     * 总出场时间(秒)
     */
    @TableField("total_playing_time")
    private Integer totalMinutesPlayed;

    // ========== 平均统计 - 场均数据 ==========
    /**
     * 场均得分
     */
    private BigDecimal avgPoints;

    /**
     * 场均篮板
     */
    private BigDecimal avgRebounds;

    /**
     * 场均进攻篮板
     */
    private BigDecimal avgOffensiveRebounds;

    /**
     * 场均防守篮板
     */
    private BigDecimal avgDefensiveRebounds;

    /**
     * 场均助攻
     */
    private BigDecimal avgAssists;

    /**
     * 场均抢断
     */
    private BigDecimal avgSteals;

    /**
     * 场均盖帽
     */
    private BigDecimal avgBlocks;

    /**
     * 场均失误
     */
    private BigDecimal avgTurnovers;

    /**
     * 场均犯规
     */
    private BigDecimal avgFouls;

    /**
     * 场均出场时间(分钟)
     */
    @TableField("avg_playing_time")
    private BigDecimal avgMinutesPlayed;

    /**
     * 效率值（场均效率值）
     */
    @TableField("efficiency_rating")
    private BigDecimal avgEfficiency;

    // ========== 命中率统计 ==========
    /**
     * 投篮命中率
     */
    private BigDecimal fieldGoalPercentage;

    /**
     * 三分命中率
     */
    private BigDecimal threePointPercentage;

    /**
     * 二分命中率
     */
    private BigDecimal twoPointPercentage;

    /**
     * 罚球命中率
     */
    private BigDecimal freeThrowPercentage;

    // ========== 高阶数据统计 ==========
    /**
     * 真实命中率
     */
    private BigDecimal trueShootingPercentage;

    /**
     * 有效投篮命中率（数据库表中不存在）
     */
    @TableField(exist = false)
    private BigDecimal effectiveFieldGoalPercentage;

    /**
     * 进攻篮板率（数据库表中不存在）
     */
    @TableField(exist = false)
    private BigDecimal offensiveReboundRate;

    /**
     * 防守篮板率（数据库表中不存在）
     */
    @TableField(exist = false)
    private BigDecimal defensiveReboundRate;

    /**
     * 助攻失误比（数据库表中不存在）
     */
    @TableField(exist = false)
    private BigDecimal assistTurnoverRatio;

    /**
     * 球员效率指数(PER)
     */
    private BigDecimal playerEfficiencyRating;

    /**
     * 使用率
     */
    private BigDecimal usageRate;

    /**
     * 净胜分（数据库表中不存在）
     */
    @TableField(exist = false)
    private BigDecimal plusMinus;

    // ========== 连胜统计 ==========
    /**
     * 当前连胜数
     */
    private Integer currentStreak;

    /**
     * 最大连胜数
     */
    private Integer maxWinStreak;

    /**
     * 最大连败数
     */
    @TableField("max_loss_streak")
    private Integer maxLoseStreak;

    /**
     * 当前连胜开始日期（数据库表中不存在）
     */
    @TableField(exist = false)
    private LocalDate streakStartDate;

    /**
     * 总胜场
     */
    @TableField("wins")
    private Integer totalWins;

    /**
     * 总负场
     */
    @TableField("losses")
    private Integer totalLosses;

    /**
     * 胜率
     */
    private BigDecimal winRate;
}