package cn.iocoder.yudao.module.operation.service.career;

import cn.iocoder.yudao.module.operation.service.career.event.BatchGameResultEvent;
import cn.iocoder.yudao.module.operation.service.career.event.GameResultEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 比赛结果事件发布器
 * 
 * 用于在比赛结束时发布事件，触发球员生涯数据的异步更新
 * 
 * <AUTHOR> Assistant
 * @since 2024
 */
@Component
@Slf4j
public class GameResultEventPublisher {

    @Resource
    private ApplicationEventPublisher eventPublisher;

    /**
     * 发布单个球员比赛结果事件
     * 
     * @param gameId 比赛ID
     * @param playerId 球员ID
     * @param gameResult 比赛结果 ("win" 或 "lose")
     */
    public void publishGameResult(Long gameId, Long playerId, String gameResult) {
        try {
            GameResultEvent event = new GameResultEvent(this, gameId, playerId, gameResult, LocalDateTime.now());
            eventPublisher.publishEvent(event);
            
            log.debug("📢 发布比赛结果事件: gameId={}, playerId={}, result={}", gameId, playerId, gameResult);
        } catch (Exception e) {
            log.error("❌ 发布比赛结果事件失败: gameId={}, playerId={}", gameId, playerId, e);
        }
    }

    /**
     * 批量发布比赛结果事件
     * 用于一场比赛多个球员的情况
     * 
     * @param gameId 比赛ID
     * @param playerIds 球员ID列表
     */
    public void publishBatchGameResult(Long gameId, List<Long> playerIds) {
        try {
            BatchGameResultEvent event = new BatchGameResultEvent(
                this, gameId, playerIds, LocalDateTime.now());
            eventPublisher.publishEvent(event);
            
            log.debug("📢 发布批量比赛结果事件: gameId={}, 球员数={}", gameId, playerIds.size());
        } catch (Exception e) {
            log.error("❌ 发布批量比赛结果事件失败: gameId={}, 球员数={}", gameId, playerIds.size(), e);
        }
    }

    /**
     * 发布比赛结束事件（所有参与球员）
     * 在比赛Service中调用，比赛结束时触发
     * 
     * @param gameId 比赛ID
     * @param playerIds 参与比赛的所有球员ID
     */
    public void publishGameFinished(Long gameId, List<Long> playerIds) {
        if (playerIds == null || playerIds.isEmpty()) {
            log.warn("⚠️ 比赛 {} 没有参与球员，跳过事件发布", gameId);
            return;
        }

        log.info("🏁 比赛 {} 结束，发布事件更新 {} 名球员的生涯数据", gameId, playerIds.size());
        publishBatchGameResult(gameId, playerIds);
    }
}