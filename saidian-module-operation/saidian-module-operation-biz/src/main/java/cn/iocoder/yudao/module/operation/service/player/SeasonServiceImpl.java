package cn.iocoder.yudao.module.operation.service.player;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.operation.controller.admin.season.vo.SeasonPageReqVO;
import cn.iocoder.yudao.module.operation.controller.admin.season.vo.SeasonSaveReqVO;
import cn.iocoder.yudao.module.operation.controller.admin.season.vo.SeasonGamePageReqVO;
import cn.iocoder.yudao.module.operation.controller.admin.game.vo.GamePageReqVO;
import cn.iocoder.yudao.module.operation.controller.admin.game.vo.GameRespVO;
import cn.iocoder.yudao.module.operation.controller.admin.game.vo.PlayerGameRelatedRespVO;
import cn.iocoder.yudao.module.operation.controller.app.player.vo.AppPlayerCareerOverviewRespVO;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.SeasonDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.GameDO;
import cn.iocoder.yudao.module.operation.dal.mysql.player.SeasonMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.game.GameMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.game.PlayerGameRelatedMapper;
import cn.iocoder.yudao.module.operation.service.game.GameService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;



/**
 * 赛季管理服务实现
 *
 * <AUTHOR> Assistant
 */
@Service
@Slf4j
public class SeasonServiceImpl implements SeasonService {

    @Resource
    private SeasonMapper seasonMapper;
    
    @Resource
    private GameMapper gameMapper;
    
    @Resource
    private PlayerGameRelatedMapper playerGameRelatedMapper;
    

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createSeason(SeasonSaveReqVO createReqVO) {
        // 插入
        SeasonDO season = BeanUtils.toBean(createReqVO, SeasonDO.class);
        seasonMapper.insert(season);
        
        log.info("创建赛季成功: {} (ID: {})", season.getSeasonName(), season.getId());
        
        // 返回
        return season.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSeason(SeasonSaveReqVO updateReqVO) {
        // 校验存在
        validateSeasonExists(updateReqVO.getId());
        
        // 更新
        SeasonDO updateObj = BeanUtils.toBean(updateReqVO, SeasonDO.class);
        seasonMapper.updateById(updateObj);
        
        log.info("更新赛季成功: {} (ID: {})", updateReqVO.getSeasonName(), updateReqVO.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSeason(Long id) {
        // 校验存在
        validateSeasonExists(id);
        
        // 删除
        seasonMapper.deleteById(id);
        
        log.info("删除赛季成功: ID = {}", id);
    }

    private void validateSeasonExists(Long id) {
        if (seasonMapper.selectById(id) == null) {
            throw new IllegalArgumentException("赛季不存在，ID: " + id);
        }
    }

    @Override
    public SeasonDO getSeason(Long id) {
        return seasonMapper.selectById(id);
    }

    @Override
    public PageResult<SeasonDO> getSeasonPage(SeasonPageReqVO pageReqVO) {
        LambdaQueryWrapper<SeasonDO> queryWrapper = new LambdaQueryWrapper<SeasonDO>()
                .like(pageReqVO.getSeasonName() != null, SeasonDO::getSeasonName, pageReqVO.getSeasonName())
                .eq(pageReqVO.getSeasonType() != null, SeasonDO::getSeasonType, pageReqVO.getSeasonType())
                .eq(pageReqVO.getStatus() != null, SeasonDO::getStatus, pageReqVO.getStatus())
                .eq(pageReqVO.getIsCurrent() != null, SeasonDO::getIsCurrent, pageReqVO.getIsCurrent())
                .eq(SeasonDO::getDeleted, false)
                .orderByDesc(SeasonDO::getCreateTime);
        
        if (pageReqVO.getCreateTime() != null && pageReqVO.getCreateTime().length == 2) {
            queryWrapper.between(SeasonDO::getCreateTime, pageReqVO.getCreateTime()[0], pageReqVO.getCreateTime()[1]);
        }
        
        return seasonMapper.selectPage(pageReqVO, queryWrapper);
    }

    @Override
    public SeasonDO getCurrentSeason() {
        SeasonDO currentSeason = seasonMapper.selectOne(
                new LambdaQueryWrapper<SeasonDO>()
                        .eq(SeasonDO::getIsCurrent, 1)
                        .eq(SeasonDO::getDeleted, false)
        );
        
        if (currentSeason == null) {
            // 如果没有当前赛季，创建默认赛季
            log.info("未找到当前赛季，创建默认赛季");
            currentSeason = initializeDefaultSeason();
        }
        
        return currentSeason;
    }

    @Override
    public SeasonDO getSeasonByDate(LocalDate date) {
        SeasonDO season = seasonMapper.selectOne(
                new LambdaQueryWrapper<SeasonDO>()
                        .le(SeasonDO::getStartDate, date)
                        .ge(SeasonDO::getEndDate, date)
                        .eq(SeasonDO::getDeleted, false)
                        .orderByDesc(SeasonDO::getCreateTime)
                        .last("LIMIT 1")
        );
        
        if (season == null) {
            // 如果没有匹配的赛季，返回当前赛季
            log.info("日期 {} 没有匹配的赛季，使用当前赛季", date);
            season = getCurrentSeason();
        }
        
        return season;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SeasonDO createSeason(String seasonName, LocalDate startDate, LocalDate endDate, Integer seasonType) {
        SeasonDO season = SeasonDO.builder()
                .seasonName(seasonName)
                .startDate(startDate)
                .endDate(endDate)
                .seasonType(seasonType)
                .status(1) // 未开始
                .isCurrent(0) // 非当前赛季
                .totalGames(0)
                .totalPlayers(0)
                .build();
        
        seasonMapper.insert(season);
        log.info("创建新赛季: {} (ID: {})", seasonName, season.getId());
        
        return season;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SeasonDO initializeDefaultSeason() {
        // 检查是否已存在当前赛季
        SeasonDO existingSeason = seasonMapper.selectOne(
                new LambdaQueryWrapper<SeasonDO>()
                        .eq(SeasonDO::getIsCurrent, 1)
                        .eq(SeasonDO::getDeleted, false)
        );
        
        if (existingSeason != null) {
            return existingSeason;
        }
        
        // 创建2024当前赛季
        LocalDate now = LocalDate.now();
        int currentYear = now.getYear();
        
        SeasonDO defaultSeason = SeasonDO.builder()
                .seasonName(currentYear + "当前赛季")
                .startDate(LocalDate.of(currentYear, 1, 1))
                .endDate(LocalDate.of(currentYear, 12, 31))
                .seasonType(1) // 春季
                .status(2) // 进行中
                .isCurrent(1) // 当前赛季
                .description("系统自动创建的默认赛季")
                .totalGames(0)
                .totalPlayers(0)
                .build();
        
        seasonMapper.insert(defaultSeason);
        log.info("初始化默认赛季: {} (ID: {})", defaultSeason.getSeasonName(), defaultSeason.getId());
        
        return defaultSeason;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long assignSeasonToGame(Long gameId, LocalDate gameDate) {
        // 根据比赛日期查找合适的赛季
        SeasonDO season = getSeasonByDate(gameDate);
        
        // 更新比赛的赛季ID
        int updateCount = gameMapper.updateSeasonId(gameId, season.getId());
        if (updateCount > 0) {
            log.info("✅ 为比赛 {} 分配赛季 {} ({})", gameId, season.getId(), season.getSeasonName());
            // 更新赛季统计信息
            updateSeasonStats(season.getId());
        } else {
            log.warn("⚠️ 为比赛 {} 分配赛季失败，比赛可能不存在", gameId);
        }
        
        return season.getId();
    }

    @Override
    public List<SeasonDO> getAllSeasons() {
        return seasonMapper.selectList(
                new LambdaQueryWrapper<SeasonDO>()
                        .eq(SeasonDO::getDeleted, false)
                        .orderByDesc(SeasonDO::getStartDate)
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSeasonStats(Long seasonId) {
        // 统计该赛季的比赛数量
        long totalGames = gameMapper.countBySeasonId(seasonId);
        
        // 统计该赛季的参与球员数量
        long totalPlayers = gameMapper.countPlayersBySeasonId(seasonId);
        
        // 更新赛季统计信息
        seasonMapper.update(null,
                new LambdaUpdateWrapper<SeasonDO>()
                        .eq(SeasonDO::getId, seasonId)
                        .set(SeasonDO::getTotalGames, totalGames)
                        .set(SeasonDO::getTotalPlayers, totalPlayers)
        );
        
        log.info("✅ 更新赛季 {} 统计信息: 比赛数量={}, 球员数量={}", seasonId, totalGames, totalPlayers);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean autoSwitchSeason() {
        try {
            // 1. 获取当前日期
            LocalDate currentDate = LocalDate.now();
            
            // 2. 获取当前赛季
            SeasonDO currentSeason = getCurrentSeason();
            
            // 3. 检查当前日期是否在当前赛季范围内
            if (currentDate.isBefore(currentSeason.getStartDate()) || currentDate.isAfter(currentSeason.getEndDate())) {
                log.info("当前日期 {} 不在当前赛季 {} 范围内，需要切换赛季", currentDate, currentSeason.getSeasonName());
                
                
            } else {
                log.info("当前日期 {} 在当前赛季 {} 范围内，无需切换", currentDate, currentSeason.getSeasonName());
            }

            return true;
            
        } catch (Exception e) {
            log.error("❌ 自动赛季切换失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean switchToSeason(Long seasonId) {
        try {
            // 1. 验证目标赛季存在
            SeasonDO targetSeason = seasonMapper.selectById(seasonId);
            if (targetSeason == null) {
                log.warn("目标赛季不存在: {}", seasonId);
                return false;
            }
            
            // 2. 将所有赛季设为非当前
            seasonMapper.update(null,
                    new LambdaUpdateWrapper<SeasonDO>()
                            .set(SeasonDO::getIsCurrent, false)
            );
            
            // 3. 设置目标赛季为当前赛季
            seasonMapper.update(null,
                    new LambdaUpdateWrapper<SeasonDO>()
                            .eq(SeasonDO::getId, seasonId)
                            .set(SeasonDO::getIsCurrent, true)
                            .set(SeasonDO::getStatus, 2) // 进行中
            );
            
            log.info("✅ 手动切换到赛季: {} ({})", targetSeason.getSeasonName(), seasonId);
            return true;
            
        } catch (Exception e) {
            log.error("❌ 手动赛季切换失败", e);
            return false;
        }
    }

    @Override
    public PageResult<GameRespVO> getSeasonGamePage(SeasonGamePageReqVO pageReqVO) {
        // 构造GamePageReqVO来调用GameMapper的分页查询
        GamePageReqVO gamePageReqVO = new GamePageReqVO();
        gamePageReqVO.setPageNo(pageReqVO.getPageNo());
        gamePageReqVO.setPageSize(pageReqVO.getPageSize());
        gamePageReqVO.setSeasonId(pageReqVO.getSeasonId());
        gamePageReqVO.setStatus(pageReqVO.getStatus());
        gamePageReqVO.setStartTime(pageReqVO.getStartTime());
        gamePageReqVO.setType(pageReqVO.getType());
        
        log.info("查询赛季 {} 的比赛列表", pageReqVO.getSeasonId());
        
        // 通过GameMapper直接查询支持seasonId过滤的比赛分页
        PageResult<GameDO> gamePageResult = gameMapper.selectPageBySeasonId(gamePageReqVO);
        
        // 转换为GameRespVO并填充球队信息
        PageResult<GameRespVO> result = BeanUtils.toBean(gamePageResult, GameRespVO.class);
        
        // 填充球队名称和logo信息
        for (GameRespVO gameRespVO : result.getList()) {
            fillTeamInfo(gameRespVO);
        }
        
        log.info("查询赛季 {} 的比赛列表完成，共 {} 条记录", pageReqVO.getSeasonId(), result.getTotal());
        
        return result;
    }
    
    /**
     * 填充球队信息（名称和logo）
     */
    private void fillTeamInfo(GameRespVO gameRespVO) {
        // 根据teamId生成球队名称（临时方案，后续可以从球队表查询）
        if (gameRespVO.getHomeTeamId() != null) {
            gameRespVO.setHomeTeamName("主队" + gameRespVO.getHomeTeamId());
            // 可以设置默认logo或从配置中获取
            // gameRespVO.setHomeTeamLogo("/static/images/default-team-logo.png");
        }
        
        if (gameRespVO.getGuestTeamId() != null) {
            gameRespVO.setGuestTeamName("客队" + gameRespVO.getGuestTeamId());
            // 可以设置默认logo或从配置中获取  
            // gameRespVO.setGuestTeamLogo("/static/images/default-team-logo.png");
        }
    }

    @Override
    public List<PlayerGameRelatedRespVO> getGamePlayers(Long gameId) {
        return gameMapper.selectPlayersRelatedByGameId(gameId);
    }

    @Override
    @Transactional
    public int initGameSeasons() {
        // 获取所有没有关联赛季的比赛
        List<GameDO> gamesWithoutSeason = gameMapper.selectList(
            new LambdaQueryWrapper<GameDO>()
                .isNull(GameDO::getSeasonId)
                .orderByAsc(GameDO::getStartTime)
        );
        
        if (gamesWithoutSeason.isEmpty()) {
            return 0;
        }
        
        // 获取所有赛季，按开始时间排序
        List<SeasonDO> seasons = seasonMapper.selectList(
            new LambdaQueryWrapper<SeasonDO>()
                .orderByAsc(SeasonDO::getStartDate)
        );
        
        if (seasons.isEmpty()) {
            return 0;
        }
        
        int updatedCount = 0;
        
        for (GameDO game : gamesWithoutSeason) {
            if (game.getStartTime() == null) {
                continue;
            }
            
            // 根据比赛开始时间查找合适的赛季
            SeasonDO matchedSeason = findMatchedSeason(game.getStartTime(), seasons);
            
            if (matchedSeason != null) {
                // 更新比赛的赛季关联
                gameMapper.updateSeasonId(game.getId(), matchedSeason.getId());
                
                // 更新赛季统计
                updateSeasonStats(matchedSeason.getId());
                
                updatedCount++;
                log.info("比赛 {} 已关联到赛季 {}", game.getId(), matchedSeason.getId());
            }
        }
        
        return updatedCount;
    }
    
    /**
     * 根据比赛时间查找匹配的赛季
     */
    private SeasonDO findMatchedSeason(LocalDateTime gameTime, List<SeasonDO> seasons) {
        for (SeasonDO season : seasons) {
            LocalDateTime seasonStart = season.getStartDate().atStartOfDay();
            LocalDateTime seasonEnd = season.getEndDate().atTime(23, 59, 59);
            
            if (!gameTime.isBefore(seasonStart) && !gameTime.isAfter(seasonEnd)) {
                return season;
            }
        }
        
        // 如果没有找到完全匹配的赛季，返回当前赛季
        return seasons.stream()
            .filter(season -> season.getIsCurrent() == 1)
            .findFirst()
            .orElse(null);
    }

    // ========== 新增：前端API相关方法实现 ==========

    @Override
    public List<AppPlayerCareerOverviewRespVO.SeasonOption> getAvailableSeasonOptions() {
        try {
            // 获取所有赛季，按创建时间倒序排列
            List<SeasonDO> seasons = seasonMapper.selectList(
                new LambdaQueryWrapper<SeasonDO>()
                    .orderByDesc(SeasonDO::getCreateTime)
            );

            if (seasons.isEmpty()) {
                // 如果没有赛季数据，创建默认赛季
                log.warn("没有找到赛季数据，创建默认赛季");
                SeasonDO defaultSeason = initializeDefaultSeason();
                seasons = new ArrayList<>();
                seasons.add(defaultSeason);
            }

            // 转换为前端需要的格式
            return seasons.stream()
                .map(this::convertToSeasonOption)
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取可用赛季列表失败", e);
            // 返回默认的赛季列表
            return createDefaultSeasonOptions();
        }
    }

    @Override
    public String getCurrentSeasonString() {
        try {
            SeasonDO currentSeason = getCurrentSeason();
            if (currentSeason != null) {
                return currentSeason.getSeasonName();
            }
        } catch (Exception e) {
            log.error("获取当前赛季字符串失败", e);
        }

        // 返回默认的当前年份
        return String.valueOf(LocalDate.now().getYear());
    }

    @Override
    public boolean isCurrentSeasonString(String season) {
        if (season == null) {
            return false;
        }

        String currentSeason = getCurrentSeasonString();
        return season.equals(currentSeason);
    }

    @Override
    public String getSeasonStatusString(String season) {
        if (season == null) {
            return "未知";
        }

        try {
            // 根据赛季名称查找赛季信息
            SeasonDO seasonDO = seasonMapper.selectOne(
                new LambdaQueryWrapper<SeasonDO>()
                    .eq(SeasonDO::getSeasonName, season)
            );

            if (seasonDO != null) {
                LocalDate now = LocalDate.now();
                if (seasonDO.getStartDate() != null && seasonDO.getEndDate() != null) {
                    if (now.isBefore(seasonDO.getStartDate())) {
                        return "未开始";
                    } else if (now.isAfter(seasonDO.getEndDate())) {
                        return "已结束";
                    } else {
                        return "进行中";
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取赛季状态失败: season={}", season, e);
        }

        // 默认判断：如果是当前年份则为进行中，否则为已结束
        int currentYear = LocalDate.now().getYear();
        try {
            int seasonYear = Integer.parseInt(season);
            return seasonYear == currentYear ? "进行中" : "已结束";
        } catch (NumberFormatException e) {
            return "未知";
        }
    }

    @Override
    public boolean isValidSeasonString(String season) {
        if (season == null || season.trim().isEmpty()) {
            return false;
        }

        try {
            // 检查是否存在该赛季
            SeasonDO seasonDO = seasonMapper.selectOne(
                new LambdaQueryWrapper<SeasonDO>()
                    .eq(SeasonDO::getSeasonName, season)
            );

            return seasonDO != null;
        } catch (Exception e) {
            log.error("验证赛季有效性失败: season={}", season, e);
            return false;
        }
    }

    // ========== 私有辅助方法 ==========

    /**
     * 将 SeasonDO 转换为 SeasonOption
     */
    private AppPlayerCareerOverviewRespVO.SeasonOption convertToSeasonOption(SeasonDO seasonDO) {
        AppPlayerCareerOverviewRespVO.SeasonOption option = new AppPlayerCareerOverviewRespVO.SeasonOption();
        option.setName(seasonDO.getSeasonName());
        option.setValue(seasonDO.getSeasonName());
        option.setCurrent(seasonDO.getIsCurrent() == 1);
        option.setStatus(getSeasonStatusString(seasonDO.getSeasonName()));
        option.setDescription(seasonDO.getDescription());
        return option;
    }

    /**
     * 创建默认的赛季选项列表
     */
    private List<AppPlayerCareerOverviewRespVO.SeasonOption> createDefaultSeasonOptions() {
        List<AppPlayerCareerOverviewRespVO.SeasonOption> options = new ArrayList<>();

        int currentYear = LocalDate.now().getYear();
        for (int i = 0; i < 3; i++) {
            int year = currentYear - i;
            AppPlayerCareerOverviewRespVO.SeasonOption option = new AppPlayerCareerOverviewRespVO.SeasonOption();
            option.setName(year + "赛季");
            option.setValue(String.valueOf(year));
            option.setCurrent(i == 0);
            option.setStatus(i == 0 ? "进行中" : "已结束");
            option.setDescription(year + "年度赛季");
            options.add(option);
        }

        return options;
    }
}