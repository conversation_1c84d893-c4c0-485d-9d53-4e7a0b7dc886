package cn.iocoder.yudao.module.operation.controller.admin.player;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.operation.controller.admin.player.PlayerCareerDataInitializer;
import cn.iocoder.yudao.module.operation.controller.admin.player.PlayerCareerDataInitializer.InitializationResult;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.SeasonDO;
import cn.iocoder.yudao.module.operation.enums.GameStatusEnum;
import cn.iocoder.yudao.module.operation.service.player.SeasonService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 球员生涯数据初始化控制器
 *
 * 提供管理员接口来执行生涯模块数据初始化
 *
 * <AUTHOR> Assistant
 * @since 2024
 */
@Tag(name = "管理后台 - 球员生涯数据初始化")
@RestController
@RequestMapping("/operation/career/init")
@Slf4j
public class PlayerCareerInitController {

    @Resource
    private PlayerCareerDataInitializer careerDataInitializer;
    
    @Resource
    private cn.iocoder.yudao.module.operation.service.career.PlayerCareerDataInitializerV2 careerDataInitializerV2;
    
    @Resource
    private DataCompletionService dataCompletionService;
    
    @Resource
    private SeasonService seasonService;
    
    @Resource
    private cn.iocoder.yudao.module.operation.dal.mysql.game.GameMapper gameMapper;
    
    @Resource
    private cn.iocoder.yudao.module.operation.dal.mysql.game.PlayerStatisticsMapper playerStatisticsMapper;
    
    @Resource
    private cn.iocoder.yudao.module.operation.dal.mysql.player.PlayerSeasonStatsMapper playerSeasonStatsMapper;
    
    @Resource
    private cn.iocoder.yudao.module.operation.dal.mysql.player.PlayerCareerStatsMapper playerCareerStatsMapper;
    
    @Resource
    private cn.iocoder.yudao.module.operation.dal.mysql.player.PlayerCareerBestStatsMapper playerCareerBestStatsMapper;


    /**
     * 执行生涯数据初始化 (V2 - 新表结构)
     */
    @PostMapping("/full-v2")
    @Operation(summary = "执行生涯数据初始化 V2", description = "使用新的分离存储表结构初始化生涯数据，自动跳过没有数据的比赛")
    @PermitAll
    public CommonResult<cn.iocoder.yudao.module.operation.service.career.PlayerCareerDataInitializerV2.InitializationResult> initializeCareerDataV2() {
        log.info("📞 接收到生涯数据初始化请求 (V2 - 新表结构)");

        try {
            cn.iocoder.yudao.module.operation.service.career.PlayerCareerDataInitializerV2.InitializationResult result = 
                    careerDataInitializerV2.initializeCareerData();

            if (result.success) {
                log.info("🎉 生涯数据初始化 V2 成功完成");
                return CommonResult.success(result);
            } else {
                log.error("❌ 生涯数据初始化 V2 失败: {}", result.errorMessage);
                return CommonResult.error(500, "初始化失败: " + result.errorMessage);
            }

        } catch (Exception e) {
            log.error("❌ 生涯数据初始化 V2 异常", e);
            return CommonResult.error(500, "初始化异常: " + e.getMessage());
        }
    }

    /**
     * 检查生涯数据状态
     */
    @GetMapping("/status")
    @Operation(summary = "检查生涯数据状态", description = "查看当前生涯模块的数据完整性状态")
    @PermitAll
    public CommonResult<StatusReport> checkCareerDataStatus() {
        try {
            // 获取当前赛季
            SeasonDO currentSeason = seasonService.getCurrentSeason();
            
            // 查询数据状态
            StatusReport statusReport = generateStatusReport(currentSeason);
            
            return CommonResult.success(statusReport);
            
        } catch (Exception e) {
            log.error("❌ 检查生涯数据状态异常", e);
            StatusReport errorReport = new StatusReport();
            errorReport.errorMessage = "检查状态异常: " + e.getMessage();
            errorReport.generateSummary();
            return CommonResult.error(500, "检查状态异常: " + e.getMessage());
        }
    }

    /**
     * 生成状态报告 - 获取真实数据状态
     */
    private StatusReport generateStatusReport(SeasonDO currentSeason) {
        StatusReport report = new StatusReport();
        
        try {
            // 获取当前真实数据状态
            PlayerCareerDataInitializer.ValidationResult validation = careerDataInitializer.getCurrentDataStatus(currentSeason);
            
            report.totalPlayers = validation.totalPlayers;
            report.playersWithData = validation.playersWithData;           // 参赛球员数
            report.playersWithSeasonData = validation.playersWithSeasonData; // 本赛季参赛球员数
            report.seasonStatsCount = validation.seasonStatsCount;
            report.careerStatsCount = validation.careerStatsCount;
            report.bestStatsCount = validation.bestStatsCount;
            
            // 获取有效比赛总数（有数据统计的比赛）
            report.totalGamesWithStats = careerDataInitializer.getTotalGamesWithStats();
            
            // 获取比赛状态统计
            report.gameStatusInfo = generateGameStatusInfo();
            
            // 获取已结束比赛数据完整性分析
            report.finishedGameDataAnalysis = generateFinishedGameDataAnalysis();
            
            // 执行数据质量验证
            performDataQualityValidation(report, currentSeason);
            
            // 计算数据完整率（基于实际参赛球员数量）
            if (report.playersWithData > 0 || report.playersWithSeasonData > 0) {
                // 每个参赛球员应该有：
                // - 赛季统计：4条（每种比赛类型1条，gameType 0-3）- 仅针对本赛季参赛球员
                // - 生涯统计：4条（每种比赛类型1条，gameType 0-3）- 针对所有参赛球员
                // - 最佳记录：1条 - 仅针对本赛季参赛球员
                
                int expectedSeasonStats = report.playersWithSeasonData * 4;  // 本赛季参赛球员 × 4种比赛类型
                int expectedCareerStats = report.playersWithData * 4;        // 所有参赛球员 × 4种比赛类型
                int expectedBestStats = report.playersWithSeasonData * 1;    // 本赛季参赛球员 × 1条最佳记录
                
                int totalExpectedRecords = expectedSeasonStats + expectedCareerStats + expectedBestStats;
                int actualRecords = report.seasonStatsCount + report.careerStatsCount + report.bestStatsCount;
                
                report.completionRate = totalExpectedRecords > 0 ? 
                        Math.min(100.0, (double)actualRecords / totalExpectedRecords * 100) : 100.0;
                
                // 添加详细的完整性分析
                report.expectedSeasonStats = expectedSeasonStats;
                report.expectedCareerStats = expectedCareerStats;
                report.expectedBestStats = expectedBestStats;
                
                report.seasonStatsCompletionRate = expectedSeasonStats > 0 ? 
                        (double)report.seasonStatsCount / expectedSeasonStats * 100 : 100.0;
                report.careerStatsCompletionRate = expectedCareerStats > 0 ? 
                        (double)report.careerStatsCount / expectedCareerStats * 100 : 100.0;
                report.bestStatsCompletionRate = expectedBestStats > 0 ? 
                        (double)report.bestStatsCount / expectedBestStats * 100 : 100.0;
            } else {
                // 没有参赛球员时，完整率为100%（因为没有期望的数据）
                report.completionRate = 100.0;
                report.expectedSeasonStats = 0;
                report.expectedCareerStats = 0;
                report.expectedBestStats = 0;
                report.seasonStatsCompletionRate = 100.0;
                report.careerStatsCompletionRate = 100.0;
                report.bestStatsCompletionRate = 100.0;
            }
            
            // 检查第0节数据缺失状态
            @SuppressWarnings("unchecked")
            java.util.Map<String, Object> missingDataStatus = (java.util.Map<String, Object>) dataCompletionService.checkMissingDataStatus();
            report.playerGamesMissingSection0 = (Integer) missingDataStatus.getOrDefault("playerGamesMissingSection0", 0);
            report.teamGamesMissingSection0 = (Integer) missingDataStatus.getOrDefault("teamGamesMissingSection0", 0);
            report.needsSection0Completion = report.playerGamesMissingSection0 > 0 || report.teamGamesMissingSection0 > 0;
            
            report.currentSeasonName = currentSeason.getSeasonName();
            report.lastUpdateTime = new java.util.Date();
            
            // 生成摘要信息
            report.generateSummary();
            
        } catch (Exception e) {
            log.error("生成状态报告失败", e);
            report.errorMessage = "数据查询失败: " + e.getMessage();
            report.generateSummary();
        }
        
        return report;
    }

    /**
     * 生成比赛状态统计信息
     */
    private StatusReport.GameStatusInfo generateGameStatusInfo() {
        StatusReport.GameStatusInfo gameStatusInfo = new StatusReport.GameStatusInfo();
        
        try {
            // 查询所有比赛的状态分布
            List<cn.iocoder.yudao.module.operation.dal.dataobject.game.GameDO> allGames = 
                    gameMapper.selectList(new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<>());
            
            gameStatusInfo.totalGames = allGames.size();
            gameStatusInfo.statusDetails = new java.util.HashMap<>();
            
            // 按状态分组统计
            java.util.Map<Integer, Long> statusCountMap = allGames.stream()
                    .collect(java.util.stream.Collectors.groupingBy(
                            game -> game.getStatus() != null ? game.getStatus() : 0,
                            java.util.stream.Collectors.counting()
                    ));
            
            // 生成状态详情
            for (java.util.Map.Entry<Integer, Long> entry : statusCountMap.entrySet()) {
                Integer status = entry.getKey();
                Long count = entry.getValue();
                
                StatusReport.GameStatusInfo.StatusDetail detail = new StatusReport.GameStatusInfo.StatusDetail();
                detail.count = count.intValue();
                
                // 使用 GameStatusEnum 获取状态名称
                try {
                    if (status == 0) {
                        detail.statusName = "草稿"; // 0 状态特殊处理
                    } else {
                        detail.statusName = GameStatusEnum.getDescription(status);
                    }
                } catch (IllegalArgumentException e) {
                    detail.statusName = "未知状态(" + status + ")";
                }
                
                detail.percentage = gameStatusInfo.totalGames > 0 ? 
                        (double) count / gameStatusInfo.totalGames * 100 : 0.0;
                
                gameStatusInfo.statusDetails.put(status, detail);
            }
            
        } catch (Exception e) {
            log.error("生成比赛状态统计失败", e);
            gameStatusInfo.totalGames = 0;
            gameStatusInfo.statusDetails = new java.util.HashMap<>();
        }
        
        return gameStatusInfo;
    }

    /**
     * 生成已结束比赛数据完整性分析
     */
    private StatusReport.FinishedGameDataAnalysis generateFinishedGameDataAnalysis() {
        StatusReport.FinishedGameDataAnalysis analysis = new StatusReport.FinishedGameDataAnalysis();
        
        try {
            // 查询所有已结束的比赛（status=4）
            List<cn.iocoder.yudao.module.operation.dal.dataobject.game.GameDO> finishedGames = 
                    gameMapper.selectList(new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<cn.iocoder.yudao.module.operation.dal.dataobject.game.GameDO>()
                            .eq(cn.iocoder.yudao.module.operation.dal.dataobject.game.GameDO::getStatus, 4));
            
            analysis.finishedGamesTotal = finishedGames.size();
            
            if (analysis.finishedGamesTotal == 0) {
                // 没有已结束的比赛
                analysis.finishedGamesWithStats = 0;
                analysis.finishedGamesWithScoreOnly = 0;
                analysis.finishedGamesWithoutScore = 0;
                analysis.dataCompletenessRate = 0.0;
                analysis.scoreCompletenessRate = 0.0;
                return analysis;
            }
            
            // 统计有统计数据的比赛（查询player_statistics表）
            int gamesWithStats = 0;
            int gamesWithScore = 0;
            
            for (cn.iocoder.yudao.module.operation.dal.dataobject.game.GameDO game : finishedGames) {
                // 检查是否有比分
                boolean hasScore = (game.getHomeTeamPoints() != null && game.getGuestTeamPoints() != null);
                if (hasScore) {
                    gamesWithScore++;
                }
                
                // 检查是否有统计数据
                Long statsCount = playerStatisticsMapper.selectCount(
                        new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerStatisticsDO>()
                                .eq(cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerStatisticsDO::getGameId, game.getId()));
                
                if (statsCount != null && statsCount > 0) {
                    gamesWithStats++;
                }
            }
            
            analysis.finishedGamesWithStats = gamesWithStats;
            analysis.finishedGamesWithScoreOnly = gamesWithScore - gamesWithStats;
            analysis.finishedGamesWithoutScore = analysis.finishedGamesTotal - gamesWithScore;
            
            // 计算完整率
            analysis.dataCompletenessRate = (double) gamesWithStats / analysis.finishedGamesTotal * 100;
            analysis.scoreCompletenessRate = (double) gamesWithScore / analysis.finishedGamesTotal * 100;
            
        } catch (Exception e) {
            log.error("生成已结束比赛数据分析失败", e);
            analysis.finishedGamesTotal = 0;
            analysis.finishedGamesWithStats = 0;
            analysis.finishedGamesWithScoreOnly = 0;
            analysis.finishedGamesWithoutScore = 0;
            analysis.dataCompletenessRate = 0.0;
            analysis.scoreCompletenessRate = 0.0;
        }
        
        return analysis;
    }

    /**
     * 执行数据质量验证
     */
    private void performDataQualityValidation(StatusReport report, SeasonDO currentSeason) {
        // 由于需要引入PlayerSeasonStatsMapper等依赖，而且这个验证比较复杂
        // 暂时简化处理，后续可以扩展
        report.dataQualityIssues = "";
    }

    /**
     * 状态报告数据结构
     */
    public static class StatusReport {
        // 基础数据统计
        public int totalPlayers;                // 总球员数
        public int playersWithData;             // 参赛球员数（有生涯数据）
        public int playersWithSeasonData;       // 本赛季参赛球员数
        public int totalGamesWithStats;         // 有数据统计的比赛总数
        public int seasonStatsCount;            // 赛季统计记录数
        public int careerStatsCount;            // 生涯统计记录数
        public int bestStatsCount;              // 最佳记录数
        public double completionRate;
        
        // 期望数量和完整率详情
        public int expectedSeasonStats;
        public int expectedCareerStats;
        public int expectedBestStats;
        public double seasonStatsCompletionRate;
        public double careerStatsCompletionRate;
        public double bestStatsCompletionRate;
        
        public String currentSeasonName;
        public java.util.Date lastUpdateTime;
        public String errorMessage;
        public String dataQualityIssues;  // 数据质量问题描述
        
        // 第0节数据缺失状态
        public int playerGamesMissingSection0;
        public int teamGamesMissingSection0;
        public boolean needsSection0Completion;
        
        // 比赛状态统计
        public GameStatusInfo gameStatusInfo;
        
        // 已结束比赛数据完整性分析
        public FinishedGameDataAnalysis finishedGameDataAnalysis;
        
        // 格式化显示的字符串（为了兼容）
        public String summary;
        
        // 结构化显示数据（便于前端渲染）
        public DisplayData displayData;
        
        /**
         * 比赛状态统计信息
         */
        public static class GameStatusInfo {
            public int totalGames;
            public java.util.Map<Integer, StatusDetail> statusDetails;
            
            public static class StatusDetail {
                public int count;
                public String statusName;
                public double percentage;
            }
        }

        /**
         * 已结束比赛数据完整性分析
         */
        public static class FinishedGameDataAnalysis {
            public int finishedGamesTotal;           // 已结束比赛总数
            public int finishedGamesWithStats;       // 有统计数据的已结束比赛数
            public int finishedGamesWithScoreOnly;   // 只有比分的已结束比赛数
            public int finishedGamesWithoutScore;    // 连比分都没有的已结束比赛数
            public double dataCompletenessRate;     // 数据完整率（有统计数据的比例）
            public double scoreCompletenessRate;    // 比分完整率（有比分的比例）
        }

        /**
         * 前端显示数据结构
         */
        public static class DisplayData {
            public BasicInfo basicInfo;
            public StatsInfo statsInfo;
            public Section0Info section0Info;
            public GameStatusInfo gameStatusInfo;
            public FinishedGameDataAnalysis finishedGameDataAnalysis;
            public SystemInfo systemInfo;
            
            public static class BasicInfo {
                public String title = "📊 生涯模块数据状态报告";
                public int totalPlayers;
                public int playersWithData;         // 参赛球员数
                public int playersWithSeasonData;   // 本赛季参赛球员数
                public int totalGamesWithStats;
                public String playersLabel = "🏀 球员总数";
                public String playersWithDataLabel = "🏆 参赛球员";
                public String playersWithSeasonDataLabel = "📅 本赛季参赛";
                public String gamesLabel = "🎮 有效比赛";
            }
            
            public static class StatsInfo {
                public int seasonStatsCount;
                public int careerStatsCount;
                public int bestStatsCount;
                public double completionRate;
                
                // 期望数量和详细完整率
                public int expectedSeasonStats;
                public int expectedCareerStats;
                public int expectedBestStats;
                public double seasonStatsCompletionRate;
                public double careerStatsCompletionRate;
                public double bestStatsCompletionRate;
                
                public String seasonStatsLabel = "📈 赛季统计";
                public String careerStatsLabel = "🏆 生涯统计";
                public String bestStatsLabel = "⭐ 最佳记录";
                public String completionRateLabel = "📊 数据完整率";
            }
            
            public static class Section0Info {
                public String title = "📋 第0节数据状态";
                public int playerGamesMissingSection0;
                public int teamGamesMissingSection0;
                public boolean needsSection0Completion;
                public String playerMissingLabel = "🏀 球员缺失";
                public String teamMissingLabel = "🏆 球队缺失";
                public String needsCompletionLabel = "🔧 需要补全";
            }
            
            public static class SystemInfo {
                public String currentSeasonName;
                public java.util.Date lastUpdateTime;
                public String seasonLabel = "⏰ 当前赛季";
                public String timeLabel = "🕐 检查时间";
            }
        }

        /**
         * 生成摘要信息和结构化数据
         */
        public void generateSummary() {
            // 生成结构化数据
            generateDisplayData();
            
            if (errorMessage != null) {
                summary = "❌ 状态检查失败: " + errorMessage;
                return;
            }
            
            StringBuilder sb = new StringBuilder();
            sb.append("📊 生涯模块数据状态报告\n");
            sb.append("========================\n");
            sb.append(String.format("🏀 球员总数: %d个\n", totalPlayers));
            sb.append(String.format("🏆 参赛球员: %d个 (有生涯数据)\n", playersWithData));
            sb.append(String.format("📅 本赛季参赛: %d个\n", playersWithSeasonData));
            sb.append(String.format("🎮 有效比赛: %d场\n", totalGamesWithStats));
            sb.append("------------------------\n");
            sb.append("📊 数据统计详情:\n");
            sb.append(String.format("📈 赛季统计: %d/%d条 (%.1f%%)\n", 
                    seasonStatsCount, expectedSeasonStats, seasonStatsCompletionRate));
            sb.append(String.format("🏆 生涯统计: %d/%d条 (%.1f%%)\n", 
                    careerStatsCount, expectedCareerStats, careerStatsCompletionRate));
            sb.append(String.format("⭐ 最佳记录: %d/%d条 (%.1f%%)\n", 
                    bestStatsCount, expectedBestStats, bestStatsCompletionRate));
            sb.append(String.format("📊 总体完整率: %.1f%%\n", completionRate));
            sb.append("------------------------\n");
            sb.append("📋 第0节数据状态:\n");
            sb.append(String.format("🏀 球员缺失: %d场比赛\n", playerGamesMissingSection0));
            sb.append(String.format("🏆 球队缺失: %d场比赛\n", teamGamesMissingSection0));
            sb.append(String.format("🔧 需要补全: %s\n", needsSection0Completion ? "是" : "否"));
            sb.append("------------------------\n");
            sb.append("🎮 比赛状态分布:\n");
            if (gameStatusInfo != null && gameStatusInfo.statusDetails != null) {
                sb.append(String.format("📊 总比赛数: %d场\n", gameStatusInfo.totalGames));
                for (java.util.Map.Entry<Integer, StatusReport.GameStatusInfo.StatusDetail> entry : gameStatusInfo.statusDetails.entrySet()) {
                    StatusReport.GameStatusInfo.StatusDetail detail = entry.getValue();
                    sb.append(String.format("   %s: %d场 (%.1f%%)\n", 
                            detail.statusName, detail.count, detail.percentage));
                }
            } else {
                sb.append("   数据获取失败\n");
            }
            sb.append("------------------------\n");
            sb.append("🔍 已结束比赛数据分析:\n");
            if (finishedGameDataAnalysis != null) {
                sb.append(String.format("📊 已结束比赛总数: %d场\n", finishedGameDataAnalysis.finishedGamesTotal));
                if (finishedGameDataAnalysis.finishedGamesTotal > 0) {
                    sb.append(String.format("✅ 有统计数据: %d场 (%.1f%%)\n", 
                            finishedGameDataAnalysis.finishedGamesWithStats, finishedGameDataAnalysis.dataCompletenessRate));
                    sb.append(String.format("📊 仅有比分: %d场\n", finishedGameDataAnalysis.finishedGamesWithScoreOnly));
                    sb.append(String.format("❌ 无比分数据: %d场\n", finishedGameDataAnalysis.finishedGamesWithoutScore));
                    sb.append(String.format("📈 比分完整率: %.1f%%\n", finishedGameDataAnalysis.scoreCompletenessRate));
                }
            } else {
                sb.append("   数据获取失败\n");
            }
            sb.append("========================\n");
            sb.append(String.format("⏰ 当前赛季: %s\n", currentSeasonName));
            sb.append(String.format("🕐 检查时间: %s\n", 
                    new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(lastUpdateTime)));
            sb.append("========================");
            
            summary = sb.toString();
        }

        /**
         * 生成结构化显示数据
         */
        private void generateDisplayData() {
            displayData = new DisplayData();
            
            // 基础信息
            displayData.basicInfo = new DisplayData.BasicInfo();
            displayData.basicInfo.totalPlayers = this.totalPlayers;
            displayData.basicInfo.playersWithData = this.playersWithData;
            displayData.basicInfo.playersWithSeasonData = this.playersWithSeasonData;
            displayData.basicInfo.totalGamesWithStats = this.totalGamesWithStats;
            
            // 统计信息
            displayData.statsInfo = new DisplayData.StatsInfo();
            displayData.statsInfo.seasonStatsCount = this.seasonStatsCount;
            displayData.statsInfo.careerStatsCount = this.careerStatsCount;
            displayData.statsInfo.bestStatsCount = this.bestStatsCount;
            displayData.statsInfo.completionRate = this.completionRate;
            
            // 详细完整率信息
            displayData.statsInfo.expectedSeasonStats = this.expectedSeasonStats;
            displayData.statsInfo.expectedCareerStats = this.expectedCareerStats;
            displayData.statsInfo.expectedBestStats = this.expectedBestStats;
            displayData.statsInfo.seasonStatsCompletionRate = this.seasonStatsCompletionRate;
            displayData.statsInfo.careerStatsCompletionRate = this.careerStatsCompletionRate;
            displayData.statsInfo.bestStatsCompletionRate = this.bestStatsCompletionRate;
            
            // 第0节数据信息
            displayData.section0Info = new DisplayData.Section0Info();
            displayData.section0Info.playerGamesMissingSection0 = this.playerGamesMissingSection0;
            displayData.section0Info.teamGamesMissingSection0 = this.teamGamesMissingSection0;
            displayData.section0Info.needsSection0Completion = this.needsSection0Completion;
            
            // 比赛状态信息
            displayData.gameStatusInfo = this.gameStatusInfo;
            
            // 已结束比赛数据分析
            displayData.finishedGameDataAnalysis = this.finishedGameDataAnalysis;
            
            // 系统信息
            displayData.systemInfo = new DisplayData.SystemInfo();
            displayData.systemInfo.currentSeasonName = this.currentSeasonName;
            displayData.systemInfo.lastUpdateTime = this.lastUpdateTime;
        }

        @Override
        public String toString() {
            if (summary == null) {
                generateSummary();
            }
            return summary;
        }
    }

    /**
     * 重置生涯数据 V2（危险操作）
     */
    @DeleteMapping("/reset-v2")
    @Operation(summary = "重置生涯数据 V2", description = "清除新表结构的所有生涯模块数据，仅用于开发测试")
    @PermitAll
    public CommonResult<String> resetCareerDataV2() {
        log.warn("⚠️ 收到重置生涯数据请求 V2 - 这是危险操作！");

        try {
            // 获取当前赛季
            SeasonDO currentSeason = seasonService.getCurrentSeason();
            log.info("开始重置生涯数据 V2，当前赛季: {}", currentSeason.getSeasonName());
            
            // 执行重置操作 - 使用MyBatis-Plus Lambda语法
            int seasonStatsDeleted = Math.toIntExact(playerSeasonStatsMapper.delete(
                    new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerSeasonStatsDO>()
                            .eq(cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerSeasonStatsDO::getSeasonId, currentSeason.getId())
            ));
            
            int careerStatsDeleted = Math.toIntExact(playerCareerStatsMapper.delete(
                    new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerCareerStatsDO>()
            ));
            
            int bestStatsDeleted = Math.toIntExact(playerCareerBestStatsMapper.delete(
                    new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerCareerBestStatsDO>()
            ));
            
            String message = String.format("🎉 生涯数据重置成功 (V2): 删除赛季统计%d条, 生涯统计%d条, 最佳记录%d条", 
                    seasonStatsDeleted, careerStatsDeleted, bestStatsDeleted);
            
            log.info(message);
            return CommonResult.success(message);
            
        } catch (Exception e) {
            log.error("❌ 生涯数据重置失败 (V2)", e);
            return CommonResult.error(500, "重置失败: " + e.getMessage());
        }
    }

    /**
     * 重置生涯数据（危险操作）
     */
    @DeleteMapping("/reset")
    @Operation(summary = "重置生涯数据", description = "清除所有生涯模块数据，仅用于开发测试")
    @PermitAll
    public CommonResult<String> resetCareerData() {
        log.warn("⚠️ 收到重置生涯数据请求 - 这是危险操作！");

        try {
            ResetResult resetResult = performDataReset();
            
            if (resetResult.success) {
                log.info("🎉 生涯数据重置成功完成");
                return CommonResult.success(resetResult.toString());
            } else {
                log.error("❌ 生涯数据重置失败: {}", resetResult.errorMessage);
                return CommonResult.error(500, "重置失败: " + resetResult.errorMessage);
            }

        } catch (Exception e) {
            log.error("❌ 生涯数据重置异常", e);
            return CommonResult.error(500, "重置异常: " + e.getMessage());
        }
    }

    /**
     * 执行数据重置
     */
    private ResetResult performDataReset() {
        ResetResult result = new ResetResult();
        
        try {
            SeasonDO currentSeason = seasonService.getCurrentSeason();
            log.info("开始重置生涯数据，当前赛季: {}", currentSeason.getSeasonName());
            
            // 通过初始化器执行数据重置
            ResetResult resetResult = careerDataInitializer.resetCareerData(currentSeason);
            
            result.seasonStatsDeleted = resetResult.seasonStatsDeleted;
            result.careerStatsDeleted = resetResult.careerStatsDeleted;
            result.bestStatsDeleted = resetResult.bestStatsDeleted;
            result.success = resetResult.success;
            result.message = resetResult.message;
            result.errorMessage = resetResult.errorMessage;
            
            if (result.success) {
                log.info("生涯数据重置完成: 删除了{}条赛季统计，{}条生涯统计，{}条最佳记录", 
                        result.seasonStatsDeleted, result.careerStatsDeleted, result.bestStatsDeleted);
            } else {
                log.error("生涯数据重置失败: {}", result.errorMessage);
            }
            
        } catch (Exception e) {
            result.success = false;
            result.errorMessage = e.getMessage();
            log.error("数据重置失败", e);
        }
        
        return result;
    }

    /**
     * 重置结果数据结构
     */
    public static class ResetResult {
        public boolean success;
        public String message;
        public int seasonStatsDeleted;
        public int careerStatsDeleted;
        public int bestStatsDeleted;
        public String errorMessage;

        @Override
        public String toString() {
            if (!success) {
                return "❌ 重置失败: " + errorMessage;
            }
            
            return String.format(
                    "🗑️ 生涯数据重置完成\n" +
                    "========================\n" +
                    "📈 赛季统计: %d条\n" +
                    "🏆 生涯统计: %d条\n" +
                    "⭐ 最佳记录: %d条\n" +
                    "========================\n" +
                    "⚠️ 请执行 /init/full 重新生成数据",
                    seasonStatsDeleted, careerStatsDeleted, bestStatsDeleted
            );
        }
    }

    /**
     * 补全缺失的第0节数据
     */
    @PostMapping("/complete-missing-section-data")
    @Operation(summary = "补全缺失的第0节数据", description = "补全历史比赛中缺失的第0节（全场）汇总数据")
    @PermitAll
    public CommonResult<DataCompletionService.CompletionResult> completeMissingSectionData() {
        log.info("📞 接收到数据补全请求");

        try {
            DataCompletionService.CompletionResult result = dataCompletionService.completeAllMissingData();

            if (result.success) {
                log.info("🎉 数据补全成功完成");
                return CommonResult.success(result);
            } else {
                log.error("❌ 数据补全失败: {}", result.errorMessage);
                return CommonResult.error(500, "数据补全失败: " + result.errorMessage);
            }

        } catch (Exception e) {
            log.error("❌ 数据补全异常", e);
            return CommonResult.error(500, "数据补全异常: " + e.getMessage());
        }
    }

    /**
     * 检查缺失数据状态
     */
    @GetMapping("/missing-data-status")
    @Operation(summary = "检查缺失数据状态", description = "检查历史比赛中缺失的第0节数据情况")
    @PermitAll
    public CommonResult<Object> checkMissingDataStatus() {
        try {
            Object status = dataCompletionService.checkMissingDataStatus();
            return CommonResult.success(status);
        } catch (Exception e) {
            log.error("❌ 检查缺失数据状态异常", e);
            return CommonResult.error(500, "检查状态异常: " + e.getMessage());
        }
    }

    /**
     * 测试接口：补全缺失的第0节数据（无权限验证）
     * 仅用于开发测试，生产环境应该移除
     */
    @PostMapping("/test/complete-missing-section-data")
    @Operation(summary = "【测试】补全缺失的第0节数据", description = "测试接口：补全历史比赛中缺失的第0节数据，无权限验证")
    @PermitAll
    public CommonResult<DataCompletionService.CompletionResult> testCompleteMissingSectionData() {
        log.info("📞 接收到测试数据补全请求（无权限验证）");

        try {
            DataCompletionService.CompletionResult result = dataCompletionService.completeAllMissingData();

            if (result.success) {
                log.info("🎉 测试数据补全成功完成");
                return CommonResult.success(result);
            } else {
                log.error("❌ 测试数据补全失败: {}", result.errorMessage);
                return CommonResult.error(500, "数据补全失败: " + result.errorMessage);
            }

        } catch (Exception e) {
            log.error("❌ 测试数据补全异常", e);
            return CommonResult.error(500, "数据补全异常: " + e.getMessage());
        }
    }

    /**
     * 测试接口：检查缺失数据状态（无权限验证）
     * 仅用于开发测试，生产环境应该移除
     */
    @GetMapping("/test/missing-data-status")
    @Operation(summary = "【测试】检查缺失数据状态", description = "测试接口：检查缺失数据状态，无权限验证")
    @PermitAll
    public CommonResult<Object> testCheckMissingDataStatus() {
        try {
            Object status = dataCompletionService.checkMissingDataStatus();
            return CommonResult.success(status);
        } catch (Exception e) {
            log.error("❌ 测试检查缺失数据状态异常", e);
            return CommonResult.error(500, "检查状态异常: " + e.getMessage());
        }
    }

    // ============ 赛季管理接口 ============

    /**
     * 执行自动赛季切换检查
     */
    @PostMapping("/season/auto-switch")
    @Operation(summary = "自动赛季切换检查", description = "检查当前日期并自动切换到对应赛季")
    @PermitAll
    public CommonResult<String> autoSwitchSeason() {
        log.info("📞 接收到自动赛季切换请求");

        try {
            boolean success = seasonService.autoSwitchSeason();
            
            if (success) {
                String currentSeasonName = seasonService.getCurrentSeason().getSeasonName();
                String result = String.format("✅ 自动赛季切换检查完成\n当前赛季: %s", currentSeasonName);
                log.info("🎉 自动赛季切换成功");
                return CommonResult.success(result);
            } else {
                log.error("❌ 自动赛季切换失败");
                return CommonResult.error(500, "自动赛季切换失败");
            }

        } catch (Exception e) {
            log.error("❌ 自动赛季切换异常", e);
            return CommonResult.error(500, "自动赛季切换异常: " + e.getMessage());
        }
    }

    /**
     * 手动切换到指定赛季
     */
    @PostMapping("/season/switch/{seasonId}")
    @Operation(summary = "手动切换赛季", description = "手动切换到指定的赛季")
    @PermitAll
    public CommonResult<String> switchToSeason(@PathVariable Long seasonId) {
        log.info("📞 接收到手动切换赛季请求: seasonId={}", seasonId);

        try {
            boolean success = seasonService.switchToSeason(seasonId);
            
            if (success) {
                String currentSeasonName = seasonService.getCurrentSeason().getSeasonName();
                String result = String.format("✅ 手动赛季切换成功\n当前赛季: %s", currentSeasonName);
                log.info("🎉 手动赛季切换成功: {}", currentSeasonName);
                return CommonResult.success(result);
            } else {
                log.error("❌ 手动赛季切换失败: seasonId={}", seasonId);
                return CommonResult.error(500, "手动赛季切换失败，可能是赛季ID不存在");
            }

        } catch (Exception e) {
            log.error("❌ 手动赛季切换异常", e);
            return CommonResult.error(500, "手动赛季切换异常: " + e.getMessage());
        }
    }

    /**
     * 获取所有赛季信息
     */
    @GetMapping("/season/list")
    @Operation(summary = "获取所有赛季信息", description = "查看系统中的所有赛季及其状态")
    @PermitAll
    public CommonResult<Object> listAllSeasons() {
        try {
            List<SeasonDO> seasons = seasonService.getAllSeasons();
            SeasonDO currentSeason = seasonService.getCurrentSeason();
            
            StringBuilder result = new StringBuilder();
            result.append("📅 系统赛季信息\n");
            result.append("================\n");
            
            for (SeasonDO season : seasons) {
                String status = "";
                switch (season.getStatus()) {
                    case 1: status = "未开始"; break;
                    case 2: status = "进行中"; break;
                    case 3: status = "已结束"; break;
                    default: status = "未知"; break;
                }
                
                String isCurrent = (season.getIsCurrent() != null && season.getIsCurrent() == 1) ? " ⭐[当前]" : "";
                result.append(String.format("🏀 %s (%s - %s) [%s]%s\n",
                        season.getSeasonName(),
                        season.getStartDate(),
                        season.getEndDate(),
                        status,
                        isCurrent
                ));
            }
            
            result.append("================\n");
            result.append(String.format("当前激活赛季: %s", currentSeason.getSeasonName()));
            
            return CommonResult.success(result.toString());
            
        } catch (Exception e) {
            log.error("❌ 获取赛季列表异常", e);
            return CommonResult.error(500, "获取赛季列表异常: " + e.getMessage());
        }
    }
}