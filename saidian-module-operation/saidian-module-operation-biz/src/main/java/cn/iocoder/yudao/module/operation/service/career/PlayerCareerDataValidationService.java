package cn.iocoder.yudao.module.operation.service.career;

import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerCareerStatsDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerStatisticsDO;
import cn.iocoder.yudao.module.operation.dal.mysql.player.PlayerCareerStatsMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.game.PlayerStatisticsMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 球员生涯数据验证和同步服务
 * 用于诊断和修复数据不一致问题
 * 
 * <AUTHOR> Assistant
 */
@Service
@Slf4j
public class PlayerCareerDataValidationService {

    @Resource
    private PlayerCareerStatsMapper playerCareerStatsMapper;
    
    @Resource
    private PlayerStatisticsMapper playerStatisticsMapper;
    
    @Resource
    private PlayerCareerStatsService playerCareerStatsService;
    
    @Resource
    private RadarChartCacheService radarChartCacheService;

    /**
     * 验证球员数据一致性
     */
    public DataValidationResult validatePlayerData(Long playerId, Integer gameType) {
        log.info("🔍 开始验证球员 {} 的数据一致性，比赛类型: {}", playerId, gameType);
        
        DataValidationResult result = new DataValidationResult();
        result.setPlayerId(playerId);
        result.setGameType(gameType);
        
        try {
            // 1. 从聚合表获取数据
            PlayerCareerStatsDO aggregatedData = getAggregatedData(playerId, gameType);
            if (aggregatedData != null) {
                result.setAggregatedAvgPoints(aggregatedData.getAvgPoints());
                result.setAggregatedTotalPoints(aggregatedData.getTotalPoints());
                result.setAggregatedGamesPlayed(aggregatedData.getGamesPlayed());
            }
            
            // 2. 从原始数据计算
            RawDataCalculation rawCalculation = calculateFromRawData(playerId, gameType);
            result.setRawAvgPoints(rawCalculation.getAvgPoints());
            result.setRawTotalPoints(rawCalculation.getTotalPoints());
            result.setRawGamesPlayed(rawCalculation.getGamesPlayed());
            result.setValidGamesCount(rawCalculation.getValidGamesCount());
            
            // 3. 比较结果
            result.setDataConsistent(isDataConsistent(result));
            
            log.info("📊 数据验证完成 - 球员: {}, 聚合表场均得分: {}, 原始数据场均得分: {}, 一致性: {}", 
                    playerId, result.getAggregatedAvgPoints(), result.getRawAvgPoints(), result.isDataConsistent());
            
        } catch (Exception e) {
            log.error("❌ 数据验证失败 - 球员ID: {}", playerId, e);
            result.setErrorMessage(e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 修复数据不一致问题
     */
    public boolean fixDataInconsistency(Long playerId) {
        log.info("🔧 开始修复球员 {} 的数据不一致问题", playerId);
        
        try {
            // 1. 清除所有相关缓存
            radarChartCacheService.evictAllCache();
            log.info("✅ 已清除所有缓存");
            
            // 2. 强制重新计算所有比赛类型的生涯数据
            playerCareerStatsService.forceRecalculateCareerStats(playerId);
            log.info("✅ 已强制重新计算生涯数据");
            
            // 3. 验证修复结果
            DataValidationResult validationResult = validatePlayerData(playerId, 0);
            if (validationResult.isDataConsistent()) {
                log.info("✅ 数据不一致问题修复成功 - 球员ID: {}", playerId);
                return true;
            } else {
                log.warn("⚠️ 数据修复后仍存在不一致 - 球员ID: {}", playerId);
                return false;
            }
            
        } catch (Exception e) {
            log.error("❌ 修复数据不一致问题失败 - 球员ID: {}", playerId, e);
            return false;
        }
    }
    
    /**
     * 从聚合表获取数据
     */
    private PlayerCareerStatsDO getAggregatedData(Long playerId, Integer gameType) {
        LambdaQueryWrapper<PlayerCareerStatsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PlayerCareerStatsDO::getPlayerId, playerId)
               .eq(PlayerCareerStatsDO::getGameType, gameType)
               .eq(PlayerCareerStatsDO::getDeleted, false);
        
        return playerCareerStatsMapper.selectOne(wrapper);
    }
    
    /**
     * 从原始数据计算
     */
    private RawDataCalculation calculateFromRawData(Long playerId, Integer gameType) {
        // 获取原始比赛数据
        LambdaQueryWrapper<PlayerStatisticsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PlayerStatisticsDO::getPlayerId, playerId)
               .eq(PlayerStatisticsDO::getSection, 0) // 全场数据
               .eq(PlayerStatisticsDO::getDeleted, false);
        
        List<PlayerStatisticsDO> rawStats = playerStatisticsMapper.selectList(wrapper);
        
        RawDataCalculation calculation = new RawDataCalculation();
        
        if (rawStats.isEmpty()) {
            calculation.setGamesPlayed(0);
            calculation.setValidGamesCount(0);
            calculation.setTotalPoints(0);
            calculation.setAvgPoints(BigDecimal.ZERO);
            return calculation;
        }
        
        int totalPoints = 0;
        int validGames = 0;
        
        for (PlayerStatisticsDO stat : rawStats) {
            if (stat.getPoints() != null) {
                totalPoints += stat.getPoints();
                validGames++;
            }
        }
        
        calculation.setGamesPlayed(rawStats.size());
        calculation.setValidGamesCount(validGames);
        calculation.setTotalPoints(totalPoints);
        
        if (validGames > 0) {
            calculation.setAvgPoints(BigDecimal.valueOf(totalPoints)
                    .divide(BigDecimal.valueOf(validGames), 2, RoundingMode.HALF_UP));
        } else {
            calculation.setAvgPoints(BigDecimal.ZERO);
        }
        
        return calculation;
    }
    
    /**
     * 检查数据是否一致
     */
    private boolean isDataConsistent(DataValidationResult result) {
        if (result.getAggregatedAvgPoints() == null || result.getRawAvgPoints() == null) {
            return false;
        }
        
        return result.getAggregatedAvgPoints().compareTo(result.getRawAvgPoints()) == 0;
    }
    
    /**
     * 数据验证结果
     */
    public static class DataValidationResult {
        private Long playerId;
        private Integer gameType;
        private BigDecimal aggregatedAvgPoints;
        private Integer aggregatedTotalPoints;
        private Integer aggregatedGamesPlayed;
        private BigDecimal rawAvgPoints;
        private Integer rawTotalPoints;
        private Integer rawGamesPlayed;
        private Integer validGamesCount;
        private boolean dataConsistent;
        private String errorMessage;
        
        // Getters and Setters
        public Long getPlayerId() { return playerId; }
        public void setPlayerId(Long playerId) { this.playerId = playerId; }
        
        public Integer getGameType() { return gameType; }
        public void setGameType(Integer gameType) { this.gameType = gameType; }
        
        public BigDecimal getAggregatedAvgPoints() { return aggregatedAvgPoints; }
        public void setAggregatedAvgPoints(BigDecimal aggregatedAvgPoints) { this.aggregatedAvgPoints = aggregatedAvgPoints; }
        
        public Integer getAggregatedTotalPoints() { return aggregatedTotalPoints; }
        public void setAggregatedTotalPoints(Integer aggregatedTotalPoints) { this.aggregatedTotalPoints = aggregatedTotalPoints; }
        
        public Integer getAggregatedGamesPlayed() { return aggregatedGamesPlayed; }
        public void setAggregatedGamesPlayed(Integer aggregatedGamesPlayed) { this.aggregatedGamesPlayed = aggregatedGamesPlayed; }
        
        public BigDecimal getRawAvgPoints() { return rawAvgPoints; }
        public void setRawAvgPoints(BigDecimal rawAvgPoints) { this.rawAvgPoints = rawAvgPoints; }
        
        public Integer getRawTotalPoints() { return rawTotalPoints; }
        public void setRawTotalPoints(Integer rawTotalPoints) { this.rawTotalPoints = rawTotalPoints; }
        
        public Integer getRawGamesPlayed() { return rawGamesPlayed; }
        public void setRawGamesPlayed(Integer rawGamesPlayed) { this.rawGamesPlayed = rawGamesPlayed; }
        
        public Integer getValidGamesCount() { return validGamesCount; }
        public void setValidGamesCount(Integer validGamesCount) { this.validGamesCount = validGamesCount; }
        
        public boolean isDataConsistent() { return dataConsistent; }
        public void setDataConsistent(boolean dataConsistent) { this.dataConsistent = dataConsistent; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }
    
    /**
     * 原始数据计算结果
     */
    private static class RawDataCalculation {
        private Integer gamesPlayed;
        private Integer validGamesCount;
        private Integer totalPoints;
        private BigDecimal avgPoints;
        
        // Getters and Setters
        public Integer getGamesPlayed() { return gamesPlayed; }
        public void setGamesPlayed(Integer gamesPlayed) { this.gamesPlayed = gamesPlayed; }
        
        public Integer getValidGamesCount() { return validGamesCount; }
        public void setValidGamesCount(Integer validGamesCount) { this.validGamesCount = validGamesCount; }
        
        public Integer getTotalPoints() { return totalPoints; }
        public void setTotalPoints(Integer totalPoints) { this.totalPoints = totalPoints; }
        
        public BigDecimal getAvgPoints() { return avgPoints; }
        public void setAvgPoints(BigDecimal avgPoints) { this.avgPoints = avgPoints; }
    }
}
