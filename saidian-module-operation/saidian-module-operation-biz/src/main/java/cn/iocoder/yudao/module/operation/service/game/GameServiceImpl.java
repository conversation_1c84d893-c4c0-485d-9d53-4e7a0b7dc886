package cn.iocoder.yudao.module.operation.service.game;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.operation.controller.admin.game.vo.*;
import cn.iocoder.yudao.module.operation.controller.app.daily.vo.AppDailyActivityRegisterPlayerRespVO;
import cn.iocoder.yudao.module.operation.controller.app.game.vo.AppGameScheduleCardResponseVO;
import cn.iocoder.yudao.module.operation.controller.app.game.vo.AppGameSchedulePageReqVO;
import cn.iocoder.yudao.module.operation.controller.app.game.vo.AppGameScheduleScrollReqVO;
import cn.iocoder.yudao.module.operation.controller.app.player.vo.AppPlayerRankRespVO;
import cn.iocoder.yudao.module.operation.controller.app.team.TeamGameHistoryVO;
import cn.iocoder.yudao.module.operation.dal.dataobject.activity.ActivityDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.daily.DailyActivityDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.GameDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerGameRelatedDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.league.LeagueDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.league.LeagueRegistrationDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.player.PlayerDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.registration.RegistrationDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.team.TeamDO;
import cn.iocoder.yudao.module.operation.dal.mysql.game.AppPlayerRankRespBO;
import cn.iocoder.yudao.module.operation.dal.mysql.game.GameMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.game.PlayerGameRelatedMapper;
import cn.iocoder.yudao.module.operation.dal.mysql.registration.RegistrationMapper;
import java.time.LocalDateTime;
import cn.iocoder.yudao.module.operation.dal.redis.RedisKeyConstants;
import cn.iocoder.yudao.module.operation.enums.DailyAvitivityStatusEnum;
import cn.iocoder.yudao.module.operation.enums.GameStatusEnum;
import cn.iocoder.yudao.module.operation.enums.GameTypeEnum;
import cn.iocoder.yudao.module.operation.enums.PlayerAttendEnum;

import cn.iocoder.yudao.module.operation.service.daily.PlayerRegistrationService;
import cn.iocoder.yudao.module.operation.service.daily.bo.PlayerAssignBO;
import cn.iocoder.yudao.module.operation.service.game.bo.GameScheduleBO;
import cn.iocoder.yudao.module.operation.service.game.bo.PlayerGameResultBO;
import cn.iocoder.yudao.module.operation.service.game.bo.PlayerGameStatisticsBO;
import cn.iocoder.yudao.module.operation.service.league.bo.LeagueRegistrationTeamBO;
import cn.iocoder.yudao.module.operation.service.player.PlayerService;
import cn.iocoder.yudao.module.operation.service.team.TeamService;
import cn.iocoder.yudao.module.operation.model.team.PlayerRegistrationInfo;
import cn.iocoder.yudao.module.operation.service.career.PlayerStatsUpdateService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.operation.enums.ErrorCodeConstants.*;

/**
 * 比赛 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class GameServiceImpl implements GameService {

    @Resource
    private GameMapper gameMapper;

    @Resource
    private PlayerGameRelatedMapper playerGameRelatedMapper;

    @Resource
    private TeamService teamService;
    @Resource
    private PlayerService playerService;

    @Resource
    private PlayerStatsUpdateService playerStatsUpdateService;

    @Override
    public Long createGame(GameSaveReqVO createReqVO) {
        // 插入
        GameDO game = BeanUtils.toBean(createReqVO, GameDO.class);
        gameMapper.insert(game);
        // 返回
        return game.getId();
    }

    @Override
    @Transactional
    public void updateGame(GameSaveReqVO updateReqVO) {
        // 校验存在
        // 更新
        GameDO updateObj = BeanUtils.toBean(updateReqVO, GameDO.class);
        gameMapper.updateById(updateObj);
        
        // 🏀 TDD Sprint 2: 比赛信息更新后触发轻量级胜负数据更新
        if (updateReqVO.getId() != null) {
            try {
                log.info("🏀 比赛信息更新完成，开始触发生涯胜负数据更新，比赛ID: {}", updateReqVO.getId());
                playerStatsUpdateService.updatePlayerWinLossStats(updateReqVO.getId());
                log.info("✅ 生涯胜负数据更新完成，比赛ID: {}", updateReqVO.getId());
            } catch (Exception e) {
                log.error("❌ 生涯胜负数据更新失败，比赛ID: {}, 错误: {}", updateReqVO.getId(), e.getMessage(), e);
                // 生涯数据更新失败不影响主流程，记录错误日志即可
            }
        }
    }

    @Override
    public void deleteGame(Long id) {
        // 校验存在
        validateGameExists(id);
        // 删除
        gameMapper.deleteById(id);
    }

    private void validateGameExists(Long id) {
        if (gameMapper.selectById(id) == null) {
            throw exception(GAME_NOT_EXISTS);
        }
    }

    @Override
    public GameDO getGame(Long id) {
        return gameMapper.selectById(id);
    }

    @Override
    public PageResult<GameRespVO> getGamePage(GamePageReqVO pageReqVO) {
        PageResult<GameDO> gameDOPageResult = gameMapper.selectPage(pageReqVO);
        List<Long> teamIds = new ArrayList<>();
        for (GameDO gameDO : gameDOPageResult.getList()) {
            teamIds.add(gameDO.getHomeTeamId());
            teamIds.add(gameDO.getGuestTeamId());
        }
        Map<Long, TeamDO> teamMap = getIdToTeamDOMap(teamIds);

        PageResult<GameRespVO> results = new PageResult<>();
        List<GameRespVO> resultList = BeanUtils.toBean(gameDOPageResult.getList(), GameRespVO.class);
        for (GameRespVO gameRespVO : resultList) {
            gameRespVO.setHomeTeamLogo(teamMap.get(gameRespVO.getHomeTeamId()).getLogo());
            gameRespVO.setHomeTeamName(teamMap.get(gameRespVO.getHomeTeamId()).getName());
            gameRespVO.setGuestTeamLogo(teamMap.get(gameRespVO.getGuestTeamId()).getLogo());
            gameRespVO.setGuestTeamName(teamMap.get(gameRespVO.getGuestTeamId()).getName());
        }

        results.setList(resultList);
        results.setTotal(gameDOPageResult.getTotal());
        return results;
    }

    @Override
    public GameDO getGameByActivityId(DailyActivityDO dailyActivityDO) {
        if (dailyActivityDO == null) {
            throw exception(DAILY_ACTIVITY_NOT_EXISTS);
        }
        return gameMapper.selectById(dailyActivityDO.getGameId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateGameStatusById(Long gameId, Integer status) {
        GameDO gameDO = new GameDO();
        gameDO.setId(gameId);
        gameDO.setStatus(status);
        gameMapper.updateById(gameDO);
        
        // 🏀 TDD Sprint 2: 比赛状态更新后触发轻量级胜负数据更新
        if (gameId != null) {
            try {
                log.info("🏀 比赛状态更新完成，开始触发生涯胜负数据更新，比赛ID: {}, 新状态: {}", gameId, status);
                playerStatsUpdateService.updatePlayerWinLossStats(gameId);
                log.info("✅ 生涯胜负数据更新完成，比赛ID: {}", gameId);
            } catch (Exception e) {
                log.error("❌ 生涯胜负数据更新失败，比赛ID: {}, 错误: {}", gameId, e.getMessage(), e);
                // 生涯数据更新失败不影响主流程，记录错误日志即可
            }
        }
    }

    @Override
    public void updateGame(GameDO gameDo) {
        gameMapper.updateById(gameDo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateGameAttend(GameDetailSaveReqVO updateReqVO) {
        List<GameDetailSaveReqVO.PlayerAttendedReqVO> attendedVOS = new ArrayList<>();
        attendedVOS.addAll(updateReqVO.getHomePlayers());
        attendedVOS.addAll(updateReqVO.getGuestPlayers());

        List<PlayerGameRelatedDO> updateReqVOs = attendedVOS.stream().map(playerAttendedReqVO -> {
            PlayerGameRelatedDO playerGameRelatedDO = new PlayerGameRelatedDO();
            playerGameRelatedDO.setId(playerAttendedReqVO.getPlayerGameRelatedId());
            playerGameRelatedDO.setNumber(playerAttendedReqVO.getNumber());
            playerGameRelatedDO.setAttend(playerAttendedReqVO.getAttend());
            return playerGameRelatedDO;
        }).collect(Collectors.toList());

        playerGameRelatedMapper.updateBatch(updateReqVOs);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeTeam(Long gameId, Long playerId, Long newTeamId) {
        PlayerGameRelatedDO playerGameRelatedDO = playerGameRelatedMapper.selectOne(new LambdaQueryWrapperX<PlayerGameRelatedDO>()
                .eq(PlayerGameRelatedDO::getGameId, gameId)
                .eq(PlayerGameRelatedDO::getPlayerId, playerId));

        if (playerGameRelatedDO == null) {
            throw exception(GAME_RELATED_NOT_EXISTS);
        }

        GameDO gameDO = gameMapper.selectById(gameId);
        if (gameDO == null) {
            throw exception(GAME_NOT_EXISTS);
        }

        if (!gameDO.getHomeTeamId().equals(newTeamId) && !gameDO.getGuestTeamId().equals(newTeamId)) {
            throw exception(GAME_TEAM_NOT_EXISTS);
        }

        playerGameRelatedDO.setTeamId(newTeamId);

        playerGameRelatedMapper.updateById(new PlayerGameRelatedDO().setTeamId(newTeamId).setId(playerGameRelatedDO.getId()));
    }

    @Override
    public List<PlayerGameResultBO> getAllGameOfPlayer(Long playerId) {
        return gameMapper.selectGameByPlayerId(playerId);
    }

    @Override
    public PlayerGameStatisticsBO getGameStatisticsOfPlayer(Long playerId) {
        List<PlayerGameResultBO> gamePlayerBOList = getAllGameOfPlayer(playerId);
        return getPlayerGameStatisticsBO(playerId, gamePlayerBOList);
    }

    private static PlayerGameStatisticsBO getPlayerGameStatisticsBO(Long playerId, List<PlayerGameResultBO> playerGameResultBOS) {
        Integer totalGameCount = playerGameResultBOS.size();
        Integer totalWinGameCount = 0;
        Integer totalLoseGameCount = 0;
        Integer totalAbsentGameCount = 0;
        Integer totalPresentGameCount = 0;

        for (PlayerGameResultBO gamePlayerBO : playerGameResultBOS) {
            if (PlayerAttendEnum.ABSENT.getType().equals(gamePlayerBO.getAttend())) {
                totalAbsentGameCount++;
                //缺席也统计胜场和败场
            } else {
                totalPresentGameCount++;
            }

            if (gamePlayerBO.isWin()) {
                totalWinGameCount++;
            } else {
                totalLoseGameCount++;
            }
        }
        return new PlayerGameStatisticsBO()
                .setPlayerId(playerId)
                .setTotalGames(totalGameCount)
                .setWinGames(totalWinGameCount)
                .setAbsentGames(totalAbsentGameCount)
                .setLoseGames(totalLoseGameCount)
                .setPresentGames(totalPresentGameCount)
                .setPlayerGameResultBOList(playerGameResultBOS);
    }

    @Override
    @Cacheable(cacheNames = RedisKeyConstants.SAIDIAN_SCHEDULE + "#300", key = "#reqVO.pageNo+'_'+#reqVO.pageSize+'_'+#reqVO.userId+'_'+#reqVO.playerId+'_'+#reqVO.status")
    public PageResult<AppGameScheduleCardResponseVO> schedulePage(AppGameSchedulePageReqVO reqVO) {
        // 必须使用 MyBatis Plus 的分页对象
        IPage<GameScheduleBO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        if (reqVO.getPlayerId() != null) {
            gameMapper.selectGameSchedulePageOfPlayer(page, reqVO);
        } else {
            gameMapper.selectGameSchedulePage(page);
        }

        return getAppGameScheduleCardResponseVOPageResult(page);
    }

    private Map<Long, TeamDO> getIdToTeamDOMap(List<Long> teamIds) {
        return teamService.getTeamByIds(teamIds).stream().collect(Collectors.toMap(TeamDO::getId, teamDO -> teamDO));
    }

    private static List<Long> getTeamIds(IPage<GameScheduleBO> page) {
        return page.getRecords().stream().flatMap(gameDO -> CollectionUtil.newArrayList(gameDO.getHomeTeamId(), gameDO.getGuestTeamId()).stream())
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addPlayerToGame(Long gameId, Long assignedTeamId, PlayerDO playerDO) {
        PlayerGameRelatedDO entity = new PlayerGameRelatedDO();
        entity.setPlayerId(playerDO.getId());
        entity.setGameId(gameId);
        entity.setTeamId(assignedTeamId);
        entity.setNumber(playerDO.getNumber());
        entity.setUserId(playerDO.getMemberUserId());
        entity.setRatings(playerDO.getRatings());
        entity.setPosition(playerDO.getPosition());
        entity.setAttend(PlayerAttendEnum.ABSENT.getType());
        entity.setCreator(String.valueOf(playerDO.getMemberUserId()));
        playerGameRelatedMapper.insert(entity);

        return entity.getId();
    }

    @Override
    public void removePlayerFromGameByUserId(Long gameId, Long userId) {
        playerGameRelatedMapper.delete(new LambdaQueryWrapperX<PlayerGameRelatedDO>()
                .eq(PlayerGameRelatedDO::getGameId, gameId)
                .eq(PlayerGameRelatedDO::getUserId, userId));
    }

    @Override
    public List<AppDailyActivityRegisterPlayerRespVO> getPlayersByGameIdAndTeamId(Long gameId, Long teamId) {
        return gameMapper.getPlayersByGameIdAndTeamId(gameId, teamId);
    }

    @Override
    public List<PlayerAssignBO> getPlayerAssignBOByGameId(Long gameId) {
        List<PlayerGameRelatedDO> playerGameRelatedDOS = playerGameRelatedMapper.selectList(new LambdaQueryWrapper<PlayerGameRelatedDO>()
                .eq(PlayerGameRelatedDO::getGameId, gameId));
        return playerGameRelatedDOS.stream().map(playerGameRelatedDO -> {
            PlayerAssignBO playerAssignBO = new PlayerAssignBO();
            playerAssignBO.setPlayerId(playerGameRelatedDO.getPlayerId());
            playerAssignBO.setTeamId(playerGameRelatedDO.getTeamId());
            playerAssignBO.setRatings(playerGameRelatedDO.getRatings());
            playerAssignBO.setPosition(playerGameRelatedDO.getPosition());
            return playerAssignBO;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removePlayerFromGameByPlayerGameRelatedId(Long playerGameRelatedId) {
        playerGameRelatedMapper.deleteById(playerGameRelatedId);
    }

    @Override
    public PageResult<AppPlayerRankRespVO> getWinRateRankPage(PageParam pageVO) {
        //查出所有球员
        IPage<AppPlayerRankRespBO> page = new Page<>(pageVO.getPageNo(), pageVO.getPageSize());
        gameMapper.getWinRateRankPage(page);

        List<AppPlayerRankRespVO> records = page.getRecords().stream().map(appPlayerRankRespBO -> {
            AppPlayerRankRespVO record = new AppPlayerRankRespVO();
            BeanUtil.copyProperties(appPlayerRankRespBO, record);
            record.setStatistics(appPlayerRankRespBO.getWinRate() + "%");
            return record;
        }).collect(Collectors.toList());

        return new PageResult<>(records, page.getTotal());
    }

    @Override
    public PageResult<AppPlayerRankRespVO> getWinCountRankPage(PageParam pageVO) {
        //查出所有球员
        IPage<AppPlayerRankRespBO> page = new Page<>(pageVO.getPageNo(), pageVO.getPageSize());
        gameMapper.getWinCountRankPage(page);

        List<AppPlayerRankRespVO> records = page.getRecords().stream().map(appPlayerRankRespBO -> {
            AppPlayerRankRespVO record = new AppPlayerRankRespVO();
            BeanUtil.copyProperties(appPlayerRankRespBO, record);
            record.setStatistics(appPlayerRankRespBO.getWins() + "胜");
            return record;
        }).collect(Collectors.toList());

        return new PageResult<>(records, page.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addPlayerToGame(GamePlayerReqVO gamePlayerReqVO) {
        // 校验参数
        if (gamePlayerReqVO.getId() == null) {
            throw exception(GAME_NOT_EXISTS);
        }
        PlayerDO player = playerService.getPlayer(gamePlayerReqVO.getPlayerId());
        if (player == null) {
            throw exception(PLAYER_NOT_EXISTS);
        }
        GameDO game = getGame(gamePlayerReqVO.getId());

        PlayerGameRelatedDO playerGameRelatedDO = playerGameRelatedMapper.selectOne(new LambdaQueryWrapperX<PlayerGameRelatedDO>()
                .eq(PlayerGameRelatedDO::getGameId, game.getId())
                .eq(PlayerGameRelatedDO::getPlayerId, player.getId()));
        if (playerGameRelatedDO != null) {
            throw exception(GAME_PLAYER_ALREADY_EXISTS);
        }


        if (gamePlayerReqVO.getTeam() == null) {
            gamePlayerReqVO.setTeam("home");
        }
        Long teamId = "home".equals(gamePlayerReqVO.getTeam()) ? game.getHomeTeamId() : game.getGuestTeamId();

        playerGameRelatedMapper.insert(new PlayerGameRelatedDO()
                .setPlayerId(player.getId())
                .setGameId(game.getId())
                .setNumber(player.getNumber())
                .setTeamId(teamId)
                .setUserId(player.getMemberUserId())
                .setRatings(player.getRatings())
                .setPosition(player.getPosition())
                .setAttend(PlayerAttendEnum.ABSENT.getType())
        );

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePlayerFromGame(Long playerId, Long id) {
        playerGameRelatedMapper.delete(new LambdaQueryWrapperX<PlayerGameRelatedDO>()
                .eq(PlayerGameRelatedDO::getPlayerId, playerId)
                .eq(PlayerGameRelatedDO::getGameId, id));
    }

    @Override
    public Map<Long, PlayerGameStatisticsBO> getGameStatisticsOfAllPlayer() {
        List<PlayerGameResultBO> playerGameResultBOS = gameMapper.selectGameOfAllPlayer();

        Map<Long, PlayerGameStatisticsBO> results = new HashMap<>();
        // 统计每个球员的比赛数据
        Map<Long, List<PlayerGameResultBO>> playerIdToGameResult = playerGameResultBOS.stream()
                .collect(Collectors.groupingBy(PlayerGameResultBO::getPlayerId));
        for (Long playerId : playerIdToGameResult.keySet()) {
            List<PlayerGameResultBO> gameResultBOS = playerIdToGameResult.getOrDefault(playerId, new ArrayList<>());
            results.put(playerId, getPlayerGameStatisticsBO(playerId, gameResultBOS));
        }
        return results;
    }

    @Override
    @Cacheable(cacheNames = RedisKeyConstants.SAIDIAN_TEAM_SCHEDULE + "#300", key = "#reqVO.pageNo+'_'+#reqVO.pageSize+'_'+#reqVO.teamId+'_'+#reqVO.status")
    public PageResult<AppGameScheduleCardResponseVO> getTeamSchedulePage(@Valid AppGameSchedulePageReqVO reqVO) {
        // 必须使用 MyBatis Plus 的分页对象
        IPage<GameScheduleBO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        gameMapper.selectGameSchedulePageOfTeam(page, reqVO);

        return getAppGameScheduleCardResponseVOPageResult(page);
    }

    @Override
    @Cacheable(cacheNames = RedisKeyConstants.TEAM_GAME_HISTORY)
    public Map<String, TeamGameHistoryVO> getGameHistoryOfAllTeams(List<Long> teamIds) {
        if (CollectionUtil.isEmpty(teamIds)) {
            return new HashMap<>();
        }

        Map<String, TeamGameHistoryVO> teamGameHistoryMap = new HashMap<>();
        for (Long teamId : teamIds) {
            TeamGameHistoryVO teamGameHistoryVO = new TeamGameHistoryVO();
            teamGameHistoryVO.setTeamId(teamId);
            teamGameHistoryVO.setWin(0);
            teamGameHistoryVO.setTotal(0);
            teamGameHistoryMap.put(String.valueOf(teamId), teamGameHistoryVO);
        }

        List<GameDO> gameDOS = gameMapper.selectList(
                new LambdaQueryWrapperX<GameDO>()
                        .eq(GameDO::getStatus, DailyAvitivityStatusEnum.END.getStatus())
                        .and(wrapper -> wrapper
                                .in(GameDO::getHomeTeamId, teamIds)
                                .or()
                                .in(GameDO::getGuestTeamId, teamIds)
                        )
        );
        //根据gameDOS 计算每个team的胜负数据
        for (GameDO gameDO : gameDOS) {
            TeamGameHistoryVO homeTeamHistoryVO = teamGameHistoryMap.get(gameDO.getHomeTeamId().toString());
            TeamGameHistoryVO guestTeamHistoryVO = teamGameHistoryMap.get(gameDO.getGuestTeamId().toString());

            // 检查主队历史对象是否为null
            if (homeTeamHistoryVO != null) {
                Long winnerTeamId = gameDO.getHomeTeamPoints() > gameDO.getGuestTeamPoints() ? gameDO.getHomeTeamId() : gameDO.getGuestTeamId();
                if (winnerTeamId.equals(gameDO.getHomeTeamId())) {
                    homeTeamHistoryVO.setWin(homeTeamHistoryVO.getWin() + 1);
                }
                // 无论输赢，给主队加一个比赛场次
                homeTeamHistoryVO.setTotal(homeTeamHistoryVO.getTotal() + 1);
            }

            // 检查客队历史对象是否为null
            if (guestTeamHistoryVO != null) {
                Long winnerTeamId = gameDO.getHomeTeamPoints() > gameDO.getGuestTeamPoints() ? gameDO.getHomeTeamId() : gameDO.getGuestTeamId();
                if (!winnerTeamId.equals(gameDO.getHomeTeamId())) {
                    guestTeamHistoryVO.setWin(guestTeamHistoryVO.getWin() + 1);
                }
                // 无论输赢，给客队加一个比赛场次
                guestTeamHistoryVO.setTotal(guestTeamHistoryVO.getTotal() + 1);
            }

        }

        return teamGameHistoryMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public GameDO createGame(List<LeagueRegistrationTeamBO> successTeams, LeagueDO league) {
        GameDO gameDO = new GameDO()
                .setStatus(DailyAvitivityStatusEnum.WAIT_START.getStatus())
                .setStartTime(league.getStartTime())
                .setHomeTeamId(successTeams.get(0).getTeamId())
                .setGuestTeamId(successTeams.get(1).getTeamId())
                .setType(GameTypeEnum.FRIEND.getType());
        gameMapper.insert(gameDO);

        for (LeagueRegistrationTeamBO successTeam : successTeams) {
            //查出球员
            List<PlayerDO> playerDOS = playerService.getPlayersByIds(successTeam
                    .getPlayerLeagueRegistrations().stream()
                    .map(LeagueRegistrationDO::getPlayerId)
                    .collect(Collectors.toList()));
            //球员比赛关联在一起
            playerGameRelatedMapper.insertBatch(playerDOS.stream()
                    .map(playerDO -> new PlayerGameRelatedDO()
                            .setPlayerId(playerDO.getId())
                            .setGameId(gameDO.getId())
                            .setNumber(playerDO.getNumber())
                            .setTeamId(successTeam.getTeamId())
                            .setUserId(playerDO.getMemberUserId())
                            .setRatings(playerDO.getRatings())
                            .setPosition(playerDO.getPosition())
                            .setAttend(PlayerAttendEnum.ABSENT.getType())
                    ).collect(Collectors.toList()));
        }

        return gameDO;
    }

    @Override
    @Cacheable(cacheNames = RedisKeyConstants.SCHEDULE_SCROLL + "#300", key = "#reqVO.pageNo+'_'+#reqVO.pageSize+'_'+#reqVO.playerId+'_'+#reqVO.direction+'_'+#reqVO.cursorTime")
    public PageResult<AppGameScheduleCardResponseVO> scheduleScroll(AppGameScheduleScrollReqVO reqVO) {
        // 1. 构建查询条件
        LambdaQueryWrapper<GameDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.ne(GameDO::getStatus, DailyAvitivityStatusEnum.CANCELED.getStatus());
        // 2. 根据方向设置时间范围条件
        LocalDate baseDateTime = reqVO.getCursorTime();
        switch (reqVO.getDirection()) {
            case "current":
                // 当前：获取基准日期当天及以后的比赛
                wrapper.ge(GameDO::getStartTime, baseDateTime);
                // 按时间升序排序
                wrapper.orderByAsc(GameDO::getStartTime);
                break;

            case "previous":
                // 历史：获取基准日期之前的比赛
                wrapper.lt(GameDO::getStartTime, baseDateTime);
                // 按时间降序排序，这样可以获取离基准日期最近的历史比赛
                wrapper.orderByDesc(GameDO::getStartTime);
                break;

            case "next":
                // 未来：获取基准日期之后的比赛
                wrapper.gt(GameDO::getStartTime, baseDateTime);
                // 按时间升序排序
                wrapper.orderByAsc(GameDO::getStartTime);
                break;

            default:
                throw exception(GAME_SCHEDULE_QUERY_DIRECTION_NOT_FOUND);
        }


        // 3. 执行分页查询
        Page<GameDO> page = new Page<>(1, reqVO.getPageSize());
        Page<GameDO> resultPage = gameMapper.selectPage(page, wrapper);

        // 4. 对于历史方向的查询结果需要再次倒序，确保返回给前端的数据按时间升序排列

        List<Long> teamIds = page.getRecords().stream().flatMap(gameDO -> CollectionUtil.newArrayList(gameDO.getHomeTeamId(), gameDO.getGuestTeamId()).stream())
                .collect(Collectors.toList());
        Map<Long, TeamDO> teamMap = getIdToTeamDOMap(teamIds);

        List<AppGameScheduleCardResponseVO> scheduleList = page.getRecords().stream().map(gameDO -> {
            AppGameScheduleCardResponseVO cardResponse = new AppGameScheduleCardResponseVO();
            BeanUtil.copyProperties(gameDO, cardResponse);
            cardResponse.setGameId(gameDO.getId());

            TeamDO homeTeam = teamMap.get(gameDO.getHomeTeamId());
            Optional.ofNullable(homeTeam).ifPresent(team -> {
                cardResponse.setHomeTeamLogo(homeTeam.getLogo());
                cardResponse.setHomeTeamName(homeTeam.getName());
            });

            TeamDO guestTeam = teamMap.get(gameDO.getGuestTeamId());
            Optional.ofNullable(guestTeam).ifPresent(team -> {
                cardResponse.setGuestTeamLogo(guestTeam.getLogo());
                cardResponse.setGuestTeamName(guestTeam.getName());
            });

            return cardResponse;
        }).collect(Collectors.toList());

        if ("previous".equals(reqVO.getDirection())) {
            Collections.reverse(scheduleList);
        }

        // 5. 构建返回结果
        return new PageResult<>(scheduleList, resultPage.getTotal());
    }

    @Override
    @Cacheable(cacheNames = RedisKeyConstants.SCHEDULE_SCROLL + "#300", key = "#reqVO.pageNo+'_'+#reqVO.pageSize+'_'+#reqVO.playerId+'_'+#reqVO.direction+'_'+#reqVO.cursorTime")
    public PageResult<AppGameScheduleCardResponseVO> scheduleScrollByPlayerId(AppGameScheduleScrollReqVO reqVO) {
        IPage<GameScheduleBO> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());

        gameMapper.scrollGameSchedulePageOfPlayer(page, reqVO);
        return getAppGameScheduleCardResponseVOPageResult(page);

    }

    private PageResult<AppGameScheduleCardResponseVO> getAppGameScheduleCardResponseVOPageResult(IPage<GameScheduleBO> page) {
        if (CollectionUtil.isEmpty(page.getRecords())) {
            return PageResult.empty();
        }

        List<Long> teamIds = getTeamIds(page);
        Map<Long, TeamDO> teamMap = getIdToTeamDOMap(teamIds);

        List<AppGameScheduleCardResponseVO> records = page.getRecords().stream().map(scheduleBO -> {
            AppGameScheduleCardResponseVO cardResponse = new AppGameScheduleCardResponseVO();
            BeanUtil.copyProperties(scheduleBO, cardResponse);
            cardResponse.setHomeTeamLogo(teamMap.get(scheduleBO.getHomeTeamId()).getLogo());
            cardResponse.setHomeTeamName(teamMap.get(scheduleBO.getHomeTeamId()).getName());
            cardResponse.setHomeTeamColor(teamMap.get(scheduleBO.getHomeTeamId()).getHomeColor());

            cardResponse.setGuestTeamLogo(teamMap.get(scheduleBO.getGuestTeamId()).getLogo());
            cardResponse.setGuestTeamName(teamMap.get(scheduleBO.getGuestTeamId()).getName());
            cardResponse.setGuestTeamColor(teamMap.get(scheduleBO.getGuestTeamId()).getGuestColor());
            return cardResponse;
        }).collect(Collectors.toList());

        return new PageResult<>(records, page.getTotal());
    }

    @Override
    public AppGameScheduleCardResponseVO getNextGameSchedule(Long userId) {
        // 1. 查询用户最近的比赛，比当前系统时间大,只返回一场
        GameDO gameDO = gameMapper.getNextGameSchedule(userId, LocalDateTime.now());
        //如果有比赛，就返回一场
        if (gameDO != null) {
            AppGameScheduleCardResponseVO cardResponse = new AppGameScheduleCardResponseVO();
            cardResponse.setGameId(gameDO.getId());
            cardResponse.setStartTime(gameDO.getStartTime());
            return cardResponse;
        }
        //如果没有比赛，就返回最近准备开始的一场比赛
        List<GameDO> waitStartGameDOs = gameMapper.selectList(new LambdaQueryWrapper<GameDO>()
                .eq(GameDO::getStatus, DailyAvitivityStatusEnum.WAIT_START.getStatus())
                .ge(GameDO::getStartTime, LocalDateTime.now())
                .orderByAsc(GameDO::getStartTime));
        if (CollectionUtil.isNotEmpty(waitStartGameDOs)) {
            AppGameScheduleCardResponseVO cardResponse = new AppGameScheduleCardResponseVO();
            cardResponse.setGameId(waitStartGameDOs.get(0).getId());
            cardResponse.setStartTime(waitStartGameDOs.get(0).getStartTime());
            return cardResponse;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createGameFromActivity(@Valid ActivityDO activity) {
        if (activity == null) {
            throw exception(ACTIVITY_NOT_EXISTS);
        }
        
        // 获取主客队信息（排位赛在活动创建时设置，友谊赛在预处理阶段设置）
        Long homeTeamId = activity.getHomeTeamId();
        Long guestTeamId = activity.getGuestTeamId();
        
        log.info("[createGameFromActivity] 开始创建比赛，活动 id={}, 活动类型={}, 主队={}, 客队={}", 
                activity.getId(), activity.getType(), homeTeamId, guestTeamId);
        
        // 创建比赛记录
        GameDO game = GameDO.builder()
                .startTime(activity.getStartTime())
                .location(activity.getLocation())
                .type(activity.getGameType() != null ? activity.getGameType() : GameTypeEnum.RANK.getType())
                .status(GameStatusEnum.WAIT_START.getStatus())
                .picUrl(activity.getPicUrl())
                .homeTeamId(homeTeamId)
                .guestTeamId(guestTeamId)
                .build();
        
        gameMapper.insert(game);
        
        log.info("[createGameFromActivity] 比赛记录创建成功，活动 id={}, 比赛 id={}, 主队={}, 客队={}", 
                activity.getId(), game.getId(), homeTeamId, guestTeamId);
        
        return game.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateGameTeams(Long gameId, Long homeTeamId, Long guestTeamId) {
        if (gameId == null) {
            throw new IllegalArgumentException("比赛ID不能为空");
        }
        
        GameDO game = gameMapper.selectById(gameId);
        if (game == null) {
            throw exception(GAME_NOT_EXISTS);
        }
        
        game.setHomeTeamId(homeTeamId);
        game.setGuestTeamId(guestTeamId);
        game.setUpdateTime(LocalDateTime.now());
        
        int updateCount = gameMapper.updateById(game);
        if (updateCount == 0) {
            throw new RuntimeException("更新比赛主客队信息失败，比赛ID: " + gameId);
        }
        
        log.info("[updateGameTeams] 更新比赛主客队信息成功，比赛 id={}, 主队={}, 客队={}", 
                gameId, homeTeamId, guestTeamId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addPlayersToFriendlyGame(Long gameId, List<RegistrationDO> registrations) {
        if (gameId == null || registrations == null || registrations.isEmpty()) {
            log.warn("[addPlayersToFriendlyGame] 参数无效，gameId={}, registrations size={}", 
                    gameId, registrations != null ? registrations.size() : 0);
            return 0;
        }
        
        // 检查比赛是否存在
        GameDO game = gameMapper.selectById(gameId);
        if (game == null) {
            throw exception(GAME_NOT_EXISTS);
        }
        
        int addedCount = 0;
        for (RegistrationDO registration : registrations) {
            try {
                // 获取球员信息
                PlayerDO player = playerService.getPlayer(registration.getPlayerId());
                if (player == null) {
                    log.warn("[addPlayersToFriendlyGame] 球员不存在，跳过添加，playerId={}", registration.getPlayerId());
                    continue;
                }
                
                // 检查球员是否已在比赛中
                if (isPlayerInGame(gameId, registration.getPlayerId())) {
                    log.warn("[addPlayersToFriendlyGame] 球员已在比赛中，跳过添加，playerId={}", registration.getPlayerId());
                    continue;
                }
                
                // 获取分配的队伍ID
                Long assignedTeamId = registration.getTeamAssigned();
                if (assignedTeamId == null) {
                    log.warn("[addPlayersToFriendlyGame] 球员未分配队伍，跳过添加，playerId={}", registration.getPlayerId());
                    continue;
                }
                
                // 添加球员到比赛
                addPlayerToGame(gameId, assignedTeamId, player);
                addedCount++;
                
                log.debug("[addPlayersToFriendlyGame] 添加球员到比赛成功，playerId={}, teamId={}", 
                        registration.getPlayerId(), assignedTeamId);
                
            } catch (Exception e) {
                log.error("[addPlayersToFriendlyGame] 添加球员到比赛失败，playerId={}", 
                        registration.getPlayerId(), e);
                // 继续处理其他球员，不中断整个流程
            }
        }
        
        log.info("[addPlayersToFriendlyGame] 友谊赛球员添加完成，比赛 id={}, 成功添加{}名球员", 
                gameId, addedCount);
        
        return addedCount;
    }
    
    

    // =============== 批量操作方法实现 ===============

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addPlayersToGameBatch(Long gameId, List<PlayerRegistrationInfo> playerInfos) {
        if (CollectionUtil.isEmpty(playerInfos)) {
            return 0;
        }

        // 验证比赛存在
        GameDO game = getGame(gameId);
        if (game == null) {
            throw exception(GAME_NOT_EXISTS);
        }

        // 构建PlayerGameRelatedDO列表
        List<PlayerGameRelatedDO> playerGameRelatedList = playerInfos.stream()
                .map(playerInfo -> {
                    PlayerDO player = playerInfo.getPlayer();
                    RegistrationDO registration = playerInfo.getRegistration();
                    
                    PlayerGameRelatedDO entity = new PlayerGameRelatedDO();
                    entity.setPlayerId(player.getId());
                    entity.setGameId(gameId);
                    entity.setTeamId(registration.getTeamAssigned()); // 使用报名记录中的队伍分配
                    entity.setNumber(player.getNumber());
                    entity.setUserId(player.getMemberUserId());
                    entity.setRatings(player.getRatings());
                    entity.setPosition(player.getPosition());
                    entity.setAttend(PlayerAttendEnum.ABSENT.getType());
                    // 设置公共字段
                    entity.setCreator(String.valueOf(player.getMemberUserId()));
                    entity.setCreateTime(LocalDateTime.now());
                    entity.setUpdater(String.valueOf(player.getMemberUserId()));
                    entity.setUpdateTime(LocalDateTime.now());
                    entity.setDeleted(false);
                    return entity;
                })
                .collect(Collectors.toList());

        // 批量插入
        return playerGameRelatedMapper.insertBatch(playerGameRelatedList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updatePlayerTeamsBatch(Long gameId, Map<Long, Long> teamAssignmentMap) {
        if (teamAssignmentMap == null || teamAssignmentMap.isEmpty()) {
            return 0;
        }

        // 验证比赛存在
        GameDO game = getGame(gameId);
        if (game == null) {
            throw exception(GAME_NOT_EXISTS);
        }

        // 验证队伍ID是否属于该比赛
        Set<Long> validTeamIds = new HashSet<>();
        validTeamIds.add(game.getHomeTeamId());
        validTeamIds.add(game.getGuestTeamId());

        for (Long teamId : teamAssignmentMap.values()) {
            if (!validTeamIds.contains(teamId)) {
                throw exception(GAME_TEAM_NOT_EXISTS);
            }
        }

        // 批量更新
        return playerGameRelatedMapper.updatePlayerTeamsBatch(gameId, teamAssignmentMap);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int removePlayersFromGameBatch(Long gameId, List<Long> playerIds) {
        if (CollectionUtil.isEmpty(playerIds)) {
            return 0;
        }

        // 验证比赛存在
        GameDO game = getGame(gameId);
        if (game == null) {
            throw exception(GAME_NOT_EXISTS);
        }

        // 批量删除（逻辑删除）
        return playerGameRelatedMapper.deleteByGameIdAndPlayerIds(gameId, playerIds);
    }

    @Override
    public boolean isPlayerInGame(Long gameId, Long playerId) {
        if (gameId == null || playerId == null) {
            return false;
        }
        return playerGameRelatedMapper.existsByGameIdAndPlayerId(gameId, playerId);
    }

    @Override
    public Set<Long> getPlayerIdsInGame(Long gameId) {
        if (gameId == null) {
            return new HashSet<>();
        }
        List<Long> playerIds = playerGameRelatedMapper.selectPlayerIdsByGameId(gameId);
        return new HashSet<>(playerIds);
    }
}