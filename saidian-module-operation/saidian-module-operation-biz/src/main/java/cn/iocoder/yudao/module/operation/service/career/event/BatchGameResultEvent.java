package cn.iocoder.yudao.module.operation.service.career.event;

import lombok.Data;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 批量比赛结果事件
 * 用于一场比赛多个球员的情况
 */
@Getter
public class BatchGameResultEvent extends ApplicationEvent {

    private final Long gameId;
    private final List<Long> playerIds;
    private final LocalDateTime gameTime;

    public BatchGameResultEvent(Object source, Long gameId, List<Long> playerIds, LocalDateTime gameTime) {
        super(source);
        this.gameId = gameId;
        this.playerIds = playerIds;
        this.gameTime = gameTime;
    }
}
