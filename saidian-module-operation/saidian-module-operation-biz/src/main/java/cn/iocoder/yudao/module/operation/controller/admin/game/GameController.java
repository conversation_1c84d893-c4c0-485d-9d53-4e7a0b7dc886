package cn.iocoder.yudao.module.operation.controller.admin.game;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.operation.controller.admin.game.vo.*;
import cn.iocoder.yudao.module.operation.controller.admin.team.vo.TeamRespVO;
import cn.iocoder.yudao.module.operation.dal.dataobject.activity.ActivityDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.GameDO;
import cn.iocoder.yudao.module.operation.dal.dataobject.game.PlayerGameRelatedDO;
import cn.iocoder.yudao.module.operation.enums.activity.ActivityStatusEnum;
import cn.iocoder.yudao.module.operation.enums.activity.ActivityTypeEnum;
import cn.iocoder.yudao.module.operation.service.activity.ActivityService;
import cn.iocoder.yudao.module.operation.service.daily.PlayerRegistrationService;
import cn.iocoder.yudao.module.operation.service.game.GamePlayerSyncService;
import cn.iocoder.yudao.module.operation.service.game.GameService;
import cn.iocoder.yudao.module.operation.service.game.PlayerGameRelatedService;
import cn.iocoder.yudao.module.operation.service.player.PlayerService;
import cn.iocoder.yudao.module.operation.service.team.TeamService;
import cn.iocoder.yudao.module.operation.service.teamassignment.TeamAssignmentService;
import cn.iocoder.yudao.module.operation.service.teamassignment.bo.TeamAssignmentResult;
import cn.iocoder.yudao.module.operation.service.career.event.GameEditEvent;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.operation.enums.ErrorCodeConstants.*;

@Tag(name = "管理后台 - 比赛")
@RestController
@RequestMapping("/operation/game")
@Validated
@Slf4j
public class GameController {

    @Resource
    private GameService gameService;

    @Resource
    private TeamService teamService;

    @Resource
    private PlayerService playerService;

    @Resource
    private PlayerRegistrationService playerRegistrationService;

    @Resource
    private PlayerGameRelatedService playerGameRelatedService;

    @Resource
    private GamePlayerSyncService gamePlayerSyncService;

    @Resource
    private TeamAssignmentService teamAssignmentService;

    @Resource
    private ActivityService activityService;
    
    @Resource
    private ApplicationEventPublisher eventPublisher;

    @PostMapping("/create")
    @Operation(summary = "创建比赛")
    @PreAuthorize("@ss.hasPermission('operation:game:create')")
    public CommonResult<Long> createGame(@Valid @RequestBody GameSaveReqVO createReqVO) {
        return success(gameService.createGame(createReqVO));
    }

    @PostMapping("/player/add")
    @Operation(summary = "添加球员到比赛")
    @PreAuthorize("@ss.hasPermission('operation:game:create')")
    public CommonResult<Boolean> addPlayerToGame(@Valid @RequestBody GamePlayerReqVO gamePlayerReqVO) {
        gameService.addPlayerToGame(gamePlayerReqVO);
        
        // 🏀 添加球员后需要更新生涯统计
        try {
            List<Long> playerIds = java.util.Collections.singletonList(gamePlayerReqVO.getPlayerId());
            GameDO game = gameService.getGame(gamePlayerReqVO.getId());
            if (game != null) {
                GameEditEvent event = GameEditEvent.createResultChangeEvent(
                    this, gamePlayerReqVO.getId(), playerIds, game.getStartTime());
                eventPublisher.publishEvent(event);
                log.info("🏀 添加球员事件已发布: gameId={}, playerId={}", gamePlayerReqVO.getId(), gamePlayerReqVO.getPlayerId());
            }
        } catch (Exception e) {
            log.error("❌ 发布添加球员事件失败: gameId={}, playerId={}", gamePlayerReqVO.getId(), gamePlayerReqVO.getPlayerId(), e);
        }
        
        return success(true);
    }

    @DeleteMapping("/player/delete")
    @Operation(summary = "删除某场比赛的球员")
    @PreAuthorize("@ss.hasPermission('operation:game:delete')")
    public CommonResult<Boolean> deletePlayerFromGame(Long playerId, Long id) {
        gameService.deletePlayerFromGame(playerId, id);
        
        // 🏀 删除球员后需要更新生涯统计
        try {
            List<Long> playerIds = java.util.Collections.singletonList(playerId);
            GameDO game = gameService.getGame(id);
            if (game != null) {
                GameEditEvent event = GameEditEvent.createResultChangeEvent(
                    this, id, playerIds, game.getStartTime());
                eventPublisher.publishEvent(event);
                log.info("🏀 删除球员事件已发布: gameId={}, playerId={}", id, playerId);
            }
        } catch (Exception e) {
            log.error("❌ 发布删除球员事件失败: gameId={}, playerId={}", id, playerId, e);
        }
        
        return success(true);
    }

    @PutMapping("/update")
    @Operation(summary = "更新比赛")
    @PreAuthorize("@ss.hasPermission('operation:game:update')")
    public CommonResult<Boolean> updateGame(@Valid @RequestBody GameSaveReqVO updateReqVO) {
        // 更新比赛基础信息
        gameService.updateGame(updateReqVO);
        
        // 🏀 发布比赛编辑事件，触发生涯数据轻量级更新
        if (updateReqVO.getId() != null) {
            try {
                // 获取参与比赛的所有球员ID
                List<Long> playerIds = playerGameRelatedService.getPlayerIdsByGameId(updateReqVO.getId());
                if (!playerIds.isEmpty()) {
                    // 发布比赛结果变更事件
                    GameEditEvent event = GameEditEvent.createResultChangeEvent(
                        this, updateReqVO.getId(), playerIds, updateReqVO.getStartTime());
                    eventPublisher.publishEvent(event);
                    log.info("🏀 比赛编辑事件已发布: gameId={}, 球员数={}", updateReqVO.getId(), playerIds.size());
                }
            } catch (Exception e) {
                log.error("❌ 发布比赛编辑事件失败: gameId={}", updateReqVO.getId(), e);
                // 不抛出异常，避免影响正常的比赛更新流程
            }
        }
        
        return success(true);
    }

    @PutMapping("/change-team")
    @Operation(summary = "换队")
    @PreAuthorize("@ss.hasPermission('operation:game:update')")
    public CommonResult<Boolean> changeTeam(@Valid @RequestBody ChangeTeamReqVO changeTeamReqVO) {
        gameService.changeTeam(changeTeamReqVO.getGameId(), changeTeamReqVO.getPlayerId(), changeTeamReqVO.getNewTeamId());
        
        // 🏀 换队也可能影响比赛结果，发布事件更新胜负数据
        try {
            List<Long> playerIds = playerGameRelatedService.getPlayerIdsByGameId(changeTeamReqVO.getGameId());
            if (!playerIds.isEmpty()) {
                GameDO game = gameService.getGame(changeTeamReqVO.getGameId());
                GameEditEvent event = GameEditEvent.createResultChangeEvent(
                    this, changeTeamReqVO.getGameId(), playerIds, 
                    game != null ? game.getStartTime() : null);
                eventPublisher.publishEvent(event);
                log.info("🏀 换队编辑事件已发布: gameId={}, 球员数={}", changeTeamReqVO.getGameId(), playerIds.size());
            }
        } catch (Exception e) {
            log.error("❌ 发布换队编辑事件失败: gameId={}", changeTeamReqVO.getGameId(), e);
        }
        
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除比赛")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('operation:game:delete')")
    public CommonResult<Boolean> deleteGame(@RequestParam("id") Long id) {
        gameService.deleteGame(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得比赛")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('operation:game:query')")
    public CommonResult<GameRespVO> getGame(@RequestParam("id") Long id) {
        GameDO game = gameService.getGame(id);
        return success(BeanUtils.toBean(game, GameRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得比赛分页")
    @PreAuthorize("@ss.hasPermission('operation:game:query')")
    public CommonResult<PageResult<GameRespVO>> getGamePage(@Valid GamePageReqVO pageReqVO) {
        PageResult<GameRespVO> pageResult = gameService.getGamePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, GameRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出比赛 Excel")
    @PreAuthorize("@ss.hasPermission('operation:game:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportGameExcel(@Valid GamePageReqVO pageReqVO,
                                HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<GameRespVO> list = gameService.getGamePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "比赛.xls", "数据", GameRespVO.class,
                BeanUtils.toBean(list, GameRespVO.class));
    }

    @PutMapping("/update-attend")
    @Operation(summary = "更新出席结果")
    @PreAuthorize("@ss.hasPermission('operation:game:update')")
    public CommonResult<Boolean> updateGameAttend(@Valid @RequestBody GameDetailSaveReqVO updateReqVO) {
        gameService.updateGameAttend(updateReqVO);
        return success(true);
    }


    @GetMapping("/detail/get")
    @Operation(summary = "获得比赛结果")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('operation:game:query')")
    public CommonResult<GameDetailSaveRespVO> getGameDetail(@RequestParam("id") Long id) {
        GameDO game = gameService.getGame(id);

        List<PlayerGameRelatedRespVO> players = playerGameRelatedService.getPlayersOfGame(id);

        GameDetailSaveRespVO result = new GameDetailSaveRespVO();
        result.setId(game.getId());
        result.setGuestTeamId(game.getGuestTeamId());
        result.setHomeTeamId(game.getHomeTeamId());
        result.setHomeScore(game.getHomeTeamPoints());
        result.setGuestScore(game.getGuestTeamPoints());
        result.setType(game.getType());

        result.setHomeTeam(BeanUtils.toBean(teamService.getTeam(game.getHomeTeamId()), TeamRespVO.class));
        result.setGuestTeam(BeanUtils.toBean(teamService.getTeam(game.getGuestTeamId()), TeamRespVO.class));

        result.setHomePlayers(players.stream().filter(playerGameRelatedRespVO -> playerGameRelatedRespVO.getTeamId().equals(game.getHomeTeamId()))
                .collect(Collectors.toList()));

        result.setGuestPlayers(players.stream().filter(playerGameRelatedRespVO -> playerGameRelatedRespVO.getTeamId().equals(game.getGuestTeamId()))
                .collect(Collectors.toList()));

        return success(result);
    }
}