package cn.iocoder.yudao.module.operation.controller.admin.daily.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 日常活动分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DailyActivityPageReqVO extends PageParam {

    @Schema(description = "活动标题")
    private String title;

    @Schema(description = "比赛id", example = "24639")
    private Long gameId;

    @Schema(description = "比赛id", requiredMode = Schema.RequiredMode.REQUIRED, example = "24639")
    private Long templateId;

    @Schema(description = "开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] startTime;

    @Schema(description = "报名截止时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] registrationEndTime;

    @Schema(description = "最小报名人数")
    private Integer minPlayers;

    @Schema(description = "最大报名人数")
    private Integer maxPlayers;

    @Schema(description = "活动价格:人均费用（总），单位：分", example = "21333")
    private Integer price;

    @Schema(description = "比赛模式：1、计时制|100分钟")
    private Integer mode;

    @Schema(description = "比赛类型：1、全场5人制篮球赛 | 2、半场4人制篮球赛")
    private Integer type;

    @Schema(description = "活动封面")
    private String picUrl;

    @Schema(description = "场地单价，单位：分", requiredMode = Schema.RequiredMode.REQUIRED, example = "11705")
    private Integer courtPrice;

    @Schema(description = "裁判人数", example = "24179")
    private Integer refereeCount;

    @Schema(description = "裁判单价，单位：分", example = "11705")
    private Integer refereePrice;

    @Schema(description = "摄像师人数", example = "15142")
    private Integer photographerCount;

    @Schema(description = "摄像师单价，单位：分", example = "29441")
    private Integer photographerPrice;

    @Schema(description = "录像师人数", example = "4856")
    private Integer videographerCount;

    @Schema(description = "录像师单价，单位：分", example = "24634")
    private Integer videographerPrice;

    @Schema(description = "录像师人数", example = "7516")
    private Integer recorderCount;

    @Schema(description = "技术台单价，单位：分", example = "12709")
    private Integer recorderPrice;

    @Schema(description = "比赛介绍", example = "你说的对")
    private String description;

    @Schema(description = "比赛地点")
    private String location;

    @Schema(description = "活动状态：如报名中", example = "1")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}