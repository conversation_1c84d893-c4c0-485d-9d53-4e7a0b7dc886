package cn.iocoder.yudao.module.operation.controller.admin.daily.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 日常活动新增 Request VO")
@Data
public class DailyActivityCreateReqVO extends DailyActivityPriceVO {

    @Schema(description = "活动标题")
    @NotNull(message = "活动标题不能为空")
    private String title;

    @Schema(description = "比赛id", requiredMode = Schema.RequiredMode.REQUIRED, example = "24639")
    private Long templateId;

    @Schema(description = "开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime startTime;

    @Schema(description = "报名截止时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "报名截止时间不能为空")
    private LocalDateTime registrationEndTime;

    @Schema(description = "最小报名人数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "最小报名人数不能为空")
    private Integer minPlayers;

    @Schema(description = "最大报名人数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "最大报名人数不能为空")
    private Integer maxPlayers;

    @Schema(description = "比赛模式：1、计时制|100分钟")
    private Integer mode;

    @Schema(description = "比赛类型：1、全场5人制篮球赛 | 2、半场4人制篮球赛")
    private Integer type;

    @Schema(description = "活动封面")
    private String picUrl;

    @Schema(description = "赠送积分", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @NotNull(message = "赠送积分不能为空")
    private Integer giveIntegral;

    @Schema(description = "比赛介绍", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    @NotEmpty(message = "比赛介绍不能为空")
    private String description;

    @Schema(description = "比赛地点", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "比赛地点不能为空")
    private String location;

    @Schema(description = "活动状态：如报名中", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "活动状态：如报名中不能为空")
    private Integer status;

}