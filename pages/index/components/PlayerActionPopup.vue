<template>
  <uni-popup 
    ref="popup" 
    type="bottom" 
    :is-mask-click="true"
    background-color="#ffffff"
    border-radius="24rpx 24rpx 0 0"
    @maskClick="handleClose"
  >
    <view class="action-popup">
      <!-- 头部拖拽条 -->
      <view class="popup-header">
        <view class="drag-indicator"></view>
        <view class="popup-title">更多操作</view>
        <text class="close-btn" @click="handleClose">✕</text>
      </view>
      
      <!-- 球员信息预览 -->
      <view v-if="playerInfo" class="player-preview">
        <image 
          :src="playerInfo.avatar || '/static/default-avatar.png'" 
          class="player-avatar"
          mode="aspectFill"
        />
        <view class="player-info">
          <text class="player-name">{{ playerInfo.name || '未知球员' }}</text>
          <text class="player-desc">{{ getPlayerDesc() }}</text>
        </view>
      </view>
      
      <!-- 操作选项列表 -->
      <view class="action-list">
        <!-- 球员自己看到的操作 -->
        <template v-if="isOwner">
          <view class="action-item" @click="handleEdit">
            <view class="action-icon edit-icon">
              <text class="icon">✏️</text>
            </view>
            <view class="action-content">
              <text class="action-title">编辑资料</text>
              <text class="action-desc">修改个人信息和生涯数据</text>
            </view>
            <text class="action-arrow">›</text>
          </view>
          
          <view class="action-item" @click="handleDownload">
            <view class="action-icon download-icon">
              <text class="icon">⬇️</text>
            </view>
            <view class="action-content">
              <text class="action-title">下载球员卡</text>
              <text class="action-desc">生成个人球员卡片并保存</text>
            </view>
            <text class="action-arrow">›</text>
          </view>
          
          <view class="action-item" @click="handleShare">
            <view class="action-icon share-icon">
              <text class="icon">📤</text>
            </view>
            <view class="action-content">
              <text class="action-title">分享生涯</text>
              <text class="action-desc">分享我的篮球生涯给好友</text>
            </view>
            <text class="action-arrow">›</text>
          </view>
          
          <view class="action-item" @click="handlePrivacySettings">
            <view class="action-icon privacy-icon">
              <text class="icon">🔒</text>
            </view>
            <view class="action-content">
              <text class="action-title">隐私设置</text>
              <text class="action-desc">设置生涯数据的可见性</text>
            </view>
            <text class="action-arrow">›</text>
          </view>
        </template>
        
        <!-- 其他用户看到的操作 -->
        <template v-else>
          <view class="action-item" @click="handleContact">
            <view class="action-icon contact-icon">
              <text class="icon">💬</text>
            </view>
            <view class="action-content">
              <text class="action-title">私信交流</text>
              <text class="action-desc">与这位球员私信交流</text>
            </view>
            <text class="action-arrow">›</text>
          </view>
          
          <view class="action-item" @click="handleFollow">
            <view class="action-icon follow-icon">
              <text class="icon">⭐</text>
            </view>
            <view class="action-content">
              <text class="action-title">{{ isFollowing ? '取消关注' : '关注球员' }}</text>
              <text class="action-desc">{{ isFollowing ? '不再关注这位球员' : '关注这位球员的动态' }}</text>
            </view>
            <text class="action-arrow">›</text>
          </view>
          
          <view class="action-item" @click="handleShare">
            <view class="action-icon share-icon">
              <text class="icon">📤</text>
            </view>
            <view class="action-content">
              <text class="action-title">分享球员</text>
              <text class="action-desc">把这位球员分享给好友</text>
            </view>
            <text class="action-arrow">›</text>
          </view>
          
          <view class="action-item" @click="handleCompare">
            <view class="action-icon compare-icon">
              <text class="icon">📊</text>
            </view>
            <view class="action-content">
              <text class="action-title">数据对比</text>
              <text class="action-desc">与我的数据进行对比</text>
            </view>
            <text class="action-arrow">›</text>
          </view>
          
          <!-- 举报选项 -->
          <view class="action-item danger" @click="handleReport">
            <view class="action-icon report-icon">
              <text class="icon">⚠️</text>
            </view>
            <view class="action-content">
              <text class="action-title">举报</text>
              <text class="action-desc">举报不当内容或虚假信息</text>
            </view>
            <text class="action-arrow">›</text>
          </view>
        </template>
      </view>
      
      <!-- 取消按钮 -->
      <view class="cancel-section">
        <view class="cancel-btn" @click="handleClose">
          <text>取消</text>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script setup>
import { ref, computed, watch, defineProps, defineEmits } from 'vue';

// 组件属性
const props = defineProps({
  // 控制弹窗显示/隐藏
  modelValue: {
    type: Boolean,
    default: false
  },
  // 是否为球员本人
  isOwner: {
    type: Boolean,
    default: false
  },
  // 球员信息
  playerInfo: {
    type: Object,
    default: null
  }
});

// 组件事件
const emit = defineEmits([
  'update:modelValue',
  'edit',
  'download', 
  'share',
  'contact',
  'report',
  'follow',
  'compare',
  'privacy-settings'
]);

// 组件引用
const popup = ref(null);

// 响应式状态
const isFollowing = ref(false); // 是否已关注，实际应该从API获取

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 获取球员描述信息
function getPlayerDesc() {
  if (!props.playerInfo) return '';
  
  const parts = [];
  if (props.playerInfo.position) {
    const positions = ['后卫', '前锋', '中锋'];
    parts.push(positions[props.playerInfo.position - 1] || '球员');
  }
  if (props.playerInfo.height) {
    parts.push(`${props.playerInfo.height}cm`);
  }
  if (props.playerInfo.experience) {
    parts.push(`${props.playerInfo.experience}年经验`);
  }
  
  return parts.length ? parts.join(' · ') : '篮球球员';
}

// 监听显示状态变化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    popup.value?.open();
  } else {
    popup.value?.close();
  }
}, { immediate: true });

// 事件处理方法
function handleClose() {
  visible.value = false;
}

function handleEdit() {
  emit('edit');
  handleClose();
}

function handleDownload() {
  emit('download');
  handleClose();
}

function handleShare() {
  emit('share');
  handleClose();
}

function handleContact() {
  emit('contact');
  handleClose();
}

function handleReport() {
  emit('report');
  handleClose();
}

function handleFollow() {
  // 切换关注状态
  isFollowing.value = !isFollowing.value;
  emit('follow', isFollowing.value);
  
  // 显示反馈
  uni.showToast({
    title: isFollowing.value ? '已关注' : '已取消关注',
    icon: 'success',
    duration: 1500
  });
  
  handleClose();
}

function handleCompare() {
  if (!props.playerInfo?.playerId) {
    uni.showToast({
      title: '暂无法对比',
      icon: 'none'
    });
    return;
  }
  
  // 跳转到数据对比页面
  uni.navigateTo({
    url: `/pages/player/data-compare?targetPlayerId=${props.playerInfo.playerId}`
  });
  
  handleClose();
}

function handlePrivacySettings() {
  emit('privacy-settings');
  handleClose();
}
</script>

<style scoped lang="scss">
.action-popup {
  background-color: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
}

.popup-header {
  position: relative;
  padding: 24rpx 32rpx 16rpx;
  text-align: center;
  border-bottom: 1rpx solid #f0f0f0;
  
  .drag-indicator {
    width: 60rpx;
    height: 6rpx;
    background-color: #e0e0e0;
    border-radius: 3rpx;
    margin: 0 auto 16rpx;
  }
  
  .popup-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
  }
  
  .close-btn {
    position: absolute;
    top: 24rpx;
    right: 32rpx;
    width: 48rpx;
    height: 48rpx;
    line-height: 48rpx;
    text-align: center;
    font-size: 28rpx;
    color: #999999;
    background-color: #f5f5f5;
    border-radius: 50%;
  }
}

.player-preview {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  
  .player-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 40rpx;
    margin-right: 24rpx;
    border: 2rpx solid #ffffff;
    box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
  }
  
  .player-info {
    flex: 1;
    
    .player-name {
      display: block;
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
      margin-bottom: 8rpx;
    }
    
    .player-desc {
      display: block;
      font-size: 24rpx;
      color: #666666;
      line-height: 1.4;
    }
  }
}

.action-list {
  padding: 16rpx 0;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  transition: background-color 0.2s;
  
  &:active {
    background-color: #f8f9fa;
  }
  
  &.danger {
    .action-title {
      color: #ff4757;
    }
    
    .action-icon {
      background-color: #ffebee;
      
      .icon {
        filter: hue-rotate(-10deg);
      }
    }
  }
  
  .action-icon {
    width: 72rpx;
    height: 72rpx;
    border-radius: 36rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 24rpx;
    
    .icon {
      font-size: 32rpx;
    }
    
    &.edit-icon {
      background-color: #e3f2fd;
    }
    
    &.download-icon {
      background-color: #f3e5f5;
    }
    
    &.share-icon {
      background-color: #e8f5e8;
    }
    
    &.contact-icon {
      background-color: #fff3e0;
    }
    
    &.follow-icon {
      background-color: #fce4ec;
    }
    
    &.compare-icon {
      background-color: #e0f2f1;
    }
    
    &.privacy-icon {
      background-color: #f1f8e9;
    }
    
    &.report-icon {
      background-color: #ffebee;
    }
  }
  
  .action-content {
    flex: 1;
    
    .action-title {
      display: block;
      font-size: 30rpx;
      font-weight: 500;
      color: #333333;
      margin-bottom: 8rpx;
      line-height: 1.2;
    }
    
    .action-desc {
      display: block;
      font-size: 24rpx;
      color: #666666;
      line-height: 1.3;
    }
  }
  
  .action-arrow {
    font-size: 32rpx;
    color: #cccccc;
    margin-left: 16rpx;
    transform: rotate(0deg);
    transition: transform 0.2s;
  }
  
  &:active .action-arrow {
    transform: rotate(90deg);
  }
}

.cancel-section {
  padding: 16rpx 32rpx 40rpx;
  
  .cancel-btn {
    width: 100%;
    height: 88rpx;
    background-color: #f8f9fa;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30rpx;
    font-weight: 500;
    color: #666666;
    transition: all 0.2s;
    
    &:active {
      background-color: #e9ecef;
      transform: scale(0.98);
    }
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .action-popup {
    background-color: #1a1a1a;
  }
  
  .popup-header {
    border-bottom-color: #333333;
    
    .popup-title {
      color: #ffffff;
    }
    
    .close-btn {
      background-color: #333333;
      color: #cccccc;
    }
  }
  
  .player-preview {
    background: linear-gradient(135deg, #2d2d30 0%, #1e1e20 100%);
    
    .player-name {
      color: #ffffff;
    }
    
    .player-desc {
      color: #cccccc;
    }
  }
  
  .action-item {
    &:active {
      background-color: #2d2d30;
    }
    
    .action-title {
      color: #ffffff;
    }
    
    .action-desc {
      color: #cccccc;
    }
  }
  
  .cancel-btn {
    background-color: #333333;
    color: #cccccc;
    
    &:active {
      background-color: #404040;
    }
  }
}
</style>