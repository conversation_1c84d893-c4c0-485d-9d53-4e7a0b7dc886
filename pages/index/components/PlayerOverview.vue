<template>
  <view class="player-overview">
    <!-- 核心数据网格 -->
    <view class="stats-section">
      <view class="stats-more" @tap="goToStatsDetail">
        更多 <uni-icons type="arrowright" size="12" color="#ccc"></uni-icons>
      </view>
      <view class="stats-grid">
        <view class="stat-item">
          <view class="stat-label">场均得分</view>
          <view class="stat-value">{{ coreStatsData.avgPoints || '--' }}</view>
          <view class="stat-rank">第{{ playerStats.pointsRank || '--' }}名</view>
        </view>
        <view class="stat-item">
          <view class="stat-label">场均篮板</view>
          <view class="stat-value">{{ coreStatsData.avgRebounds || '--' }}</view>
          <view class="stat-rank">第{{ playerStats.reboundsRank || '--' }}名</view>
        </view>
        <view class="stat-item">
          <view class="stat-label">场均助攻</view>
          <view class="stat-value">{{ coreStatsData.avgAssists || '--' }}</view>
          <view class="stat-rank">第{{ playerStats.assistsRank || '--' }}名</view>
        </view>
        <view class="stat-item">
          <view class="stat-label">球员贡献度</view>
          <view class="stat-value">{{ coreStatsData.avgEfficiency || '--' }}</view>
          <view class="stat-rank">第{{ playerStats.contributionRank || '--' }}名</view>
        </view>
      </view>
    </view>

    <!-- 雷达图部分 -->
    <view class="info-section-render">
      <view class="radar-selectors">
        <picker @change="onSeasonChange" :value="seasonIndex" :range="seasons" range-key="name">
          <view class="season-select">
            {{ seasons[seasonIndex].name }}
            <uni-icons type="arrowdown" size="12" color="#2c3e50"></uni-icons>
          </view>
        </picker>
        <picker
          @change="onMatchTypeChange"
          :value="matchTypeIndex"
          :range="matchTypes"
          range-key="name"
        >
          <view class="match-type-select">
            {{ matchTypes[matchTypeIndex].name }}
            <uni-icons type="arrowdown" size="12" color="#2c3e50"></uni-icons>
          </view>
        </picker>
      </view>

      <!-- 雷达图容器 -->
      <view class="hexagon-radar-chart">
        <!-- 加载状态 -->
        <view class="loading-overlay" v-if="loading">
          <uni-icons type="spinner-cycle" size="24" color="#4a90e2"></uni-icons>
          <text class="loading-text">数据加载中...</text>
        </view>
        
        <!-- 纯CSS雷达图 - 统一坐标系容器 -->
        <view class="css-radar-container" :class="{ 'loading': loading }">
          <!-- 背景网格 -->
          <view class="radar-background">
            <view class="grid-circle" v-for="level in 3" :key="level" :class="`level-${level}`"></view>
            <view class="grid-line" v-for="line in 6" :key="line" :class="`line-${line}`"></view>
          </view>
          
          <!-- 数据填充区域 -->
          <view class="data-fill-area" :style="getDataAreaStyle()"></view>
          
          <!-- 连接线 -->
          <view class="connection-lines">
            <view 
              class="connection-line" 
              v-for="(line, index) in connectionLines" 
              :key="index"
              :style="line.style"
            ></view>
          </view>
          
          <!-- 数据点 -->
          <view class="data-points">
            <view 
              class="data-point" 
              v-for="(point, index) in cssDataPoints" 
              :key="index"
              :style="point.style"
            ></view>
          </view>
          
          <!-- 动态数据标签 - 移动到容器内部确保坐标系一致 -->
          <view 
            v-for="(label, index) in radarLabels" 
            :key="index"
            class="dynamic-axis-label"
            :style="label.style"
          >
            <view class="label-name">{{ label.name }}</view>
            <view class="label-value">
              {{ label.value }}
              <text class="axis-max-value" v-if="label.maxValue !== 100">/{{ label.maxValue }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 最佳数据部分 -->
    <view class="info-section">
      <view class="best-data-section">
        <view class="section-title">
          <uni-icons type="star" size="16" color="#4a90e2"></uni-icons>
          最佳数据
        </view>

        <view class="season-toggle">
          <view
            class="season-btn"
            :class="{ active: bestDataType === 'current' }"
            @tap="switchBestDataType('current')"
          >
            本赛季
          </view>
          <view
            class="season-btn"
            :class="{ active: bestDataType === 'career' }"
            @tap="switchBestDataType('career')"
          >
            生涯
          </view>
        </view>

        <!-- 本赛季最佳数据 -->
        <view
          class="season-data-panel"
          :class="{ active: bestDataType === 'current' }"
          v-if="bestDataType === 'current'"
        >
          <view class="best-data-grid">
            <view class="best-data-item">
              <view class="best-data-label">单场得分</view>
              <view class="best-data-value">{{ bestData.current.points || '37' }}</view>
              <view class="best-data-meta">
                <view>第{{ bestData.current.pointsRank || '1' }}名</view>
                <view class="best-data-date">{{
                  bestData.current.pointsDate || '2023.11.15'
                }}</view>
              </view>
            </view>
            <view class="best-data-item">
              <view class="best-data-label">单场篮板</view>
              <view class="best-data-value">{{ bestData.current.rebounds || '12' }}</view>
              <view class="best-data-meta">
                <view>第{{ bestData.current.reboundsRank || '3' }}名</view>
                <view class="best-data-date">{{
                  bestData.current.reboundsDate || '2023.09.23'
                }}</view>
              </view>
            </view>
            <view class="best-data-item">
              <view class="best-data-label">单场助攻</view>
              <view class="best-data-value">{{ bestData.current.assists || '9' }}</view>
              <view class="best-data-meta">
                <view>第{{ bestData.current.assistsRank || '8' }}名</view>
                <view class="best-data-date">{{
                  bestData.current.assistsDate || '2023.10.05'
                }}</view>
              </view>
            </view>
            <view class="best-data-item">
              <view class="best-data-label">单场三分</view>
              <view class="best-data-value">{{ bestData.current.threePointers || '7' }}</view>
              <view class="best-data-meta">
                <view>第{{ bestData.current.threePointersRank || '5' }}名</view>
                <view class="best-data-date">{{
                  bestData.current.threePointersDate || '2023.11.15'
                }}</view>
              </view>
            </view>
          </view>
        </view>

        <!-- 生涯最佳数据 -->
        <view
          class="season-data-panel"
          :class="{ active: bestDataType === 'career' }"
          v-if="bestDataType === 'career'"
        >
          <view class="best-data-grid">
            <view class="best-data-item">
              <view class="best-data-label">单场得分</view>
              <view class="best-data-value">{{ bestData.career.points || '42' }}</view>
              <view class="best-data-meta">
                <view>第{{ bestData.career.pointsRank || '3' }}名</view>
                <view class="best-data-date">{{ bestData.career.pointsDate || '2021.07.12' }}</view>
              </view>
            </view>
            <view class="best-data-item">
              <view class="best-data-label">单场篮板</view>
              <view class="best-data-value">{{ bestData.career.rebounds || '15' }}</view>
              <view class="best-data-meta">
                <view>第{{ bestData.career.reboundsRank || '1' }}名</view>
                <view class="best-data-date">{{
                  bestData.career.reboundsDate || '2022.04.18'
                }}</view>
              </view>
            </view>
            <view class="best-data-item">
              <view class="best-data-label">单场助攻</view>
              <view class="best-data-value">{{ bestData.career.assists || '13' }}</view>
              <view class="best-data-meta">
                <view>第{{ bestData.career.assistsRank || '5' }}名</view>
                <view class="best-data-date">{{
                  bestData.career.assistsDate || '2022.09.30'
                }}</view>
              </view>
            </view>
            <view class="best-data-item">
              <view class="best-data-label">单场三分</view>
              <view class="best-data-value">{{ bestData.career.threePointers || '9' }}</view>
              <view class="best-data-meta">
                <view>第{{ bestData.career.threePointersRank || '8' }}名</view>
                <view class="best-data-date">{{
                  bestData.career.threePointersDate || '2021.07.12'
                }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
  import { ref, reactive, onMounted, nextTick, computed } from 'vue';
  import PlayerApi from '@/sheep/api/operation/player';

  // 组件属性
  const props = defineProps({
    playerStats: {
      type: Object,
      default: () => ({}),
    },
    playerId: {
      type: Number,
      required: true,
    },
  });

  // 响应式数据
  const seasonIndex = ref(0);
  const matchTypeIndex = ref(0);
  const bestDataType = ref('current');
  const loading = ref(false);
  const bestDataLoading = ref(false);
  
  // 动态赛季列表 - 从API获取
  const seasons = ref([]);

  const matchTypes = ref([
    { name: '全部比赛', value: 0 },
    { name: '排位赛', value: 1 },
    { name: '友谊赛', value: 2 },
    { name: '联赛', value: 3 },
  ]);

  // 动态雷达图数据 - 直接存储API返回的原始数据
  const rawRadarData = ref([]);
  
  // 保留旧的radarData作为兼容（可以后续移除）

  const bestData = reactive({
    current: {
      points: '37',
      pointsRank: '1',
      pointsDate: '2023.11.15',
      rebounds: '12',
      reboundsRank: '3',
      reboundsDate: '2023.09.23',
      assists: '9',
      assistsRank: '8',
      assistsDate: '2023.10.05',
      threePointers: '7',
      threePointersRank: '5',
      threePointersDate: '2023.11.15',
    },
    career: {
      points: '42',
      pointsRank: '3',
      pointsDate: '2021.07.12',
      rebounds: '15',
      reboundsRank: '1',
      reboundsDate: '2022.04.18',
      assists: '13',
      assistsRank: '5',
      assistsDate: '2022.09.30',
      threePointers: '9',
      threePointersRank: '8',
      threePointersDate: '2021.07.12',
    },
  });

  // 生命周期
  onMounted(() => {
    nextTick(() => {
      console.log('雷达图组件已挂载');
      // 智能混合方案：先加载概览数据（包含赛季列表），再加载最佳数据
      initializeData();
    });
  });

  // 初始化数据加载
  async function initializeData() {
    try {
      // 先加载概览数据，获取赛季列表和雷达图数据
      await updateRadarData();
      
      // 获取到赛季数据后，再加载最佳数据
      await loadBestStats();
      
      console.log('初始化数据加载完成');
    } catch (error) {
      console.error('初始化数据加载失败：', error);
    }
  }

  // 计算属性：动态数据点数组 - 基于API返回的原始数据
  const dataPoints = computed(() => {
    if (!rawRadarData.value || rawRadarData.value.length === 0) {
      // 如果没有API数据，返回空数组
      return [];
    }
    
    return rawRadarData.value.map(point => {
      // 修复：后端已经统一maxValue为100，score为相对百分比
      const score = Number(point.score) || 0;
      const maxValue = Number(point.maxValue) || 100;
      
      return {
        name: point.dimension,           // 直接使用后端返回的dimension
        value: score,                    // 使用实际的score值（已经是百分比）
        maxValue: maxValue,              // 统一的最大值（现在是100）
        normalizedValue: maxValue > 0 ? Math.min(1, score / maxValue) : 0  // 计算归一化值 (0-1)
      };
    });
  });

  // 计算属性：动态CSS数据点数组 - 支持任意数量维度
  const cssDataPoints = computed(() => {
    if (!dataPoints.value || dataPoints.value.length === 0) {
      return [];
    }
    
    const centerX = 160; // 固定rpx值，rpx自动响应式适配
    const centerY = 160; // 固定rpx值，rpx自动响应式适配
    const maxRadius = 100;
    const pointCount = dataPoints.value.length;
    
    // 动态计算角度分布 - 从顶部开始，均匀分布
    const angleStep = 360 / pointCount;
    
    return dataPoints.value.map((point, index) => {
      // 使用归一化值，并确保最小值不为0（否则看不到点）
      const normalizedValue = Math.max(0.1, Math.min(1, point.normalizedValue));
      
      // 修正角度计算：从正上方(-90度)开始，顺时针分布
      // -90度对应正上方，然后顺时针分布
      const angle = -90 + (index * angleStep);
      const angleRad = (angle * Math.PI) / 180;
      
      const radius = maxRadius * normalizedValue;
      const x = centerX + Math.cos(angleRad) * radius;
      const y = centerY + Math.sin(angleRad) * radius;
      
      return { 
        x, 
        y, 
        value: normalizedValue,
        rawData: point,  // 保存原始数据用于标签显示
        angle,           // 保存角度用于标签定位
        style: {
          left: `${x}rpx`,
          top: `${y}rpx`,
          opacity: 0.8 + normalizedValue * 0.2,
          transform: 'translate(-50%, -50%)',
          background: `rgba(74, 144, 226, ${0.6 + normalizedValue * 0.4})`,
          border: '2rpx solid rgba(255, 255, 255, 0.9)',
          boxShadow: '0 4rpx 8rpx rgba(74, 144, 226, 0.3)',
          transition: 'all 0.5s ease'
        }
      };
    });
  });

  // 计算属性：连接线数组
  const connectionLines = computed(() => {
    const points = cssDataPoints.value;
    const lines = [];
    
    for (let i = 0; i < points.length; i++) {
      const current = points[i];
      const next = points[(i + 1) % points.length]; // 闭合连接
      
      // 计算线段长度和角度
      const dx = next.x - current.x;
      const dy = next.y - current.y;
      const length = Math.sqrt(dx * dx + dy * dy);
      const angle = Math.atan2(dy, dx) * 180 / Math.PI;
      
      lines.push({
        style: {
          left: `${current.x}rpx`,
          top: `${current.y}rpx`,
          width: `${length}rpx`,
          height: '2rpx',
          background: 'rgba(74, 144, 226, 0.8)',
          transform: `rotate(${angle}deg)`,
          transformOrigin: '0 center',
          position: 'absolute',
          transition: 'all 0.5s ease'
        }
      });
    }
    
    return lines;
  });

  // 计算属性：动态标签数组 - 用于雷达图标签显示
  const radarLabels = computed(() => {
    if (!cssDataPoints.value || cssDataPoints.value.length === 0) {
      return [];
    }
    
    // 使用固定rpx值，rpx自动响应式适配
    const centerX = 200; // 容器中心点 (400rpx/2)
    const centerY = 200; // 容器中心点 
    const labelRadius = 190; // 标签距离，充分利用空间
    
    return cssDataPoints.value.map((point) => {
      const angleRad = (point.angle * Math.PI) / 180;
      const labelX = centerX + Math.cos(angleRad) * labelRadius;
      const labelY = centerY + Math.sin(angleRad) * labelRadius;
      
      // 修复：显示更有意义的百分比信息
      const percentage = point.rawData.value.toFixed(1);
      
      return {
        name: point.rawData.name,
        value: `${percentage}%`,  // 显示为百分比
        maxValue: point.rawData.maxValue,
        style: {
          position: 'absolute',
          left: `${labelX}rpx`,
          top: `${labelY}rpx`,
          transform: 'translate(-50%, -50%)',
          textAlign: 'center',
          fontSize: '22rpx',
          color: '#2c3e50',
          whiteSpace: 'nowrap',
          zIndex: 10
        }
      };
    });
  });

  // 方法
  async function onSeasonChange(e) {
    seasonIndex.value = e.detail.value;
    // 赛季变更时需要同时更新概览数据和最佳数据
    try {
      await updateRadarData();
      await loadBestStats();
    } catch (error) {
      console.error('赛季切换时数据更新失败：', error);
    }
  }

  async function onMatchTypeChange(e) {
    matchTypeIndex.value = e.detail.value;
    // 比赛类型变更时需要同时更新概览数据和最佳数据
    try {
      await updateRadarData();
      await loadBestStats();
    } catch (error) {
      console.error('比赛类型切换时数据更新失败：', error);
    }
  }

  function switchBestDataType(type) {
    bestDataType.value = type;
    // 切换类型时重新加载最佳数据
    loadBestStats();
  }

  // 加载最佳数据
  async function loadBestStats() {
    if (!props.playerId) {
      console.warn('playerId is required');
      return;
    }

    bestDataLoading.value = true;

    try {
      // 🎯 智能默认策略：与updateRadarData保持一致
      const season = (seasons.value && seasons.value.length > 0 && seasons.value[seasonIndex.value]) 
        ? seasons.value[seasonIndex.value].value 
        : undefined; // 不传season参数，让后端智能默认
      const gameType = matchTypes.value[matchTypeIndex.value].value;
      
      // 🎯 构建API参数：只有在season有值时才传递该参数
      const apiParams = {
        playerId: props.playerId,
        gameType
      };
      
      // 如果season有值，才添加到参数中
      if (season !== undefined) {
        apiParams.season = season;
      }
      
      console.log('调用最佳数据API，参数：', apiParams);

      const response = await PlayerApi.getPlayerBestStats(apiParams);

      if (response.code === 0 && response.data) {
        const { seasonBest, careerBest } = response.data;
        
        // 更新赛季最佳数据
        if (seasonBest && seasonBest.length > 0) {
          seasonBest.forEach(item => {
            if (item.statName === '单场最高得分') {
              bestData.current.points = item.value;
              bestData.current.pointsDate = formatDate(item.achieveDate);
              // 这里暂时没有排名信息，保持原有数据
            } else if (item.statName === '单场最高篮板') {
              bestData.current.rebounds = item.value;
              bestData.current.reboundsDate = formatDate(item.achieveDate);
            } else if (item.statName === '单场最高助攻') {
              bestData.current.assists = item.value;
              bestData.current.assistsDate = formatDate(item.achieveDate);
            } else if (item.statName === '单场最高效率') {
              bestData.current.threePointers = item.value;
              bestData.current.threePointersDate = formatDate(item.achieveDate);
            }
          });
        }
        
        // 更新生涯最佳数据
        if (careerBest && careerBest.length > 0) {
          careerBest.forEach(item => {
            if (item.statName === '单场最高得分') {
              bestData.career.points = item.value;
              bestData.career.pointsDate = formatDate(item.achieveDate);
            } else if (item.statName === '单场最高篮板') {
              bestData.career.rebounds = item.value;
              bestData.career.reboundsDate = formatDate(item.achieveDate);
            } else if (item.statName === '单场最高助攻') {
              bestData.career.assists = item.value;
              bestData.career.assistsDate = formatDate(item.achieveDate);
            } else if (item.statName === '单场最高效率') {
              bestData.career.threePointers = item.value;
              bestData.career.threePointersDate = formatDate(item.achieveDate);
            }
          });
        }

        console.log('最佳数据已更新：', bestData);
      } else {
        console.error('最佳数据API返回错误：', response);
      }
    } catch (error) {
      console.error('获取最佳数据失败：', error);
      uni.showToast({
        title: '最佳数据获取失败',
        icon: 'none'
      });
    } finally {
      bestDataLoading.value = false;
    }
  }

  // 格式化日期
  function formatDate(dateStr) {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}.${month}.${day}`;
  }

  async function updateRadarData() {
    if (!props.playerId) {
      console.warn('playerId is required');
      return;
    }

    loading.value = true;

    try {
      // 🎯 智能默认策略：
      // 1. 如果是初次加载（seasons为空），不传season参数，让后端自动使用当前赛季
      // 2. 如果是用户切换，使用选中的赛季值
      const season = (seasons.value && seasons.value.length > 0 && seasons.value[seasonIndex.value]) 
        ? seasons.value[seasonIndex.value].value 
        : undefined; // 不传season参数，让后端智能默认
      const gameType = matchTypes.value[matchTypeIndex.value].value;
      
      // 🎯 构建API参数：只有在season有值时才传递该参数
      const apiParams = {
        playerId: props.playerId,
        gameType
      };
      
      // 如果season有值，才添加到参数中（让后端智能处理默认值）
      if (season !== undefined) {
        apiParams.season = season;
      }
      
      console.log('调用生涯概览API，参数：', apiParams);

      const response = await PlayerApi.getPlayerCareerOverview(apiParams);

      if (response.code === 0 && response.data) {
        const { radarChart, currentSeason, availableSeasons } = response.data;
        
        // 更新赛季列表 - 智能混合方案的核心
        if (availableSeasons && availableSeasons.length > 0) {
          seasons.value = availableSeasons;
          
          // 设置当前赛季的选中索引
          const currentIndex = availableSeasons.findIndex(season => season.current === true);
          if (currentIndex >= 0) {
            seasonIndex.value = currentIndex;
          }
          
          console.log('赛季列表已更新：', seasons.value);
          console.log('当前赛季：', currentSeason, '选中索引：', seasonIndex.value);
        }
        
        if (radarChart && radarChart.dataPoints) {
          // 直接使用API返回的原始数据，支持动态维度
          rawRadarData.value = radarChart.dataPoints;
          
          console.log('雷达图原始数据已更新：', rawRadarData.value);
          console.log('维度数量：', rawRadarData.value.length);
          
          // 打印每个维度的详细信息
          rawRadarData.value.forEach((point, index) => {
            const normalizedValue = point.maxValue > 0 ? (point.score / point.maxValue * 100).toFixed(1) : '0';
            console.log(`维度${index + 1}: ${point.dimension} - 得分: ${point.score}/${point.maxValue} (${normalizedValue}%)`);
          });
          
        } else {
          console.warn('雷达图数据格式不正确：', radarChart);
        }
      } else {
        console.error('API返回错误：', response);
        uni.showToast({
          title: '数据获取失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('获取生涯概览数据失败：', error);
      uni.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    } finally {
      loading.value = false;
      
      nextTick(() => {
        console.log('雷达图数据更新完成');
      });
    }
  }

  // CSS雷达图相关方法 - 动态填充区域计算
  function getDataAreaStyle() {
    if (!dataPoints.value || dataPoints.value.length === 0) {
      return {
        clipPath: 'polygon(50% 50%)',
        background: 'rgba(74, 144, 226, 0.1)',
        transition: 'all 0.5s ease'
      };
    }
    
    const center = 50; // 50%
    const maxRadius = 40; // 最大40%半径
    const pointCount = dataPoints.value.length;
    const angleStep = 360 / pointCount;
    
    // 动态计算多边形顶点，与数据点角度保持一致
    const points = dataPoints.value.map((point, index) => {
      // 使用归一化值
      const normalizedValue = Math.max(0.1, Math.min(1, point.normalizedValue));
      
      // 修正角度计算：从正上方(-90度)开始，顺时针分布
      const angle = -90 + (index * angleStep);
      const angleRad = (angle * Math.PI) / 180;
      
      const radius = maxRadius * normalizedValue;
      const x = center + Math.cos(angleRad) * radius;
      const y = center + Math.sin(angleRad) * radius;
      return `${x}% ${y}%`;
    }).join(', ');

    return {
      clipPath: `polygon(${points})`,
      background: 'rgba(74, 144, 226, 0.3)',
      border: '2rpx solid rgba(74, 144, 226, 0.6)',
      transition: 'all 0.5s ease'
    };
  }

  function goToStatsDetail() {
    // 跳转到数据详情页面
    uni.showToast({
      title: '跳转到数据详情',
      icon: 'none',
    });
  }
</script>

<style scoped lang="scss">
  .player-overview {
    padding: 16rpx;
  }

  .stats-section {
    background-color: white;
    margin-top: 16rpx;
    border-radius: 16rpx;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
    overflow: hidden;
    position: relative;
  }

  .stats-more {
    top: 16rpx;
    right: 20rpx;
    font-size: 24rpx;
    color: #ccc;
    display: flex;
    align-items: center;
    gap: 4rpx;
    z-index: 10;
    cursor: pointer;
    padding-top: 5rpx;
    justify-content: flex-end;
    padding-right: 10rpx;

    &:active {
      opacity: 0.7;
    }
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0;
    padding: 20rpx 0;
  }

  .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 12rpx;
    position: relative;

    &:not(:last-child) {
      border-right: 2rpx solid #f5f7fa;
    }
  }

  .stat-label {
    font-size: 22rpx;
    color: #2c3e50;
    margin-bottom: 8rpx;
    text-align: center;
    font-weight: 500;
    white-space: nowrap;
  }

  .stat-value {
    font-size: 36rpx;
    font-weight: 700;
    color: #4a90e2;
    margin-bottom: 8rpx;
    line-height: 1;
  }

  .stat-rank {
    font-size: 20rpx;
    color: #7f8c8d;
    background-color: #f5f7fa;
    padding: 4rpx 8rpx;
    border-radius: 16rpx;
    text-align: center;
    white-space: nowrap;
  }

  .info-section {
    background-color: white;
    margin-top: 20rpx;
    padding: 30rpx;
    border-radius: 16rpx;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
  }
    .info-section-render {
    background-color: white;
    margin-top: 20rpx;
    border-radius: 16rpx;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
  }

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 30rpx;
    display: flex;
    align-items: center;
    gap: 16rpx;
  }

  .radar-selectors {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 60rpx;
    padding-top: 10rpx;
  }

  .season-select,
  .match-type-select {
    background: transparent;
    border: none;
    color: #2c3e50;
    font-size: 32rpx;
    font-weight: 600;
    cursor: pointer;
    padding: 0;
    outline: none;
    display: flex;
    align-items: center;
    gap: 8rpx;

    &:active {
      color: #4a90e2;
    }
  }

  .hexagon-radar-chart {
    width: 100%;
    height: 600rpx;
    position: relative;
    margin: 20rpx 0;
  }

  .css-radar-container {
    position: relative;
    width: 400rpx;
    height: 400rpx;
    margin: 0 auto;
    top: 50%;
    transform: translateY(-50%);
    transition: opacity 0.3s ease;

    &.loading {
      opacity: 0.5;
    }
  }

  .loading-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16rpx;
    z-index: 100;
    background: rgba(255, 255, 255, 0.9);
    padding: 30rpx;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  }

  .loading-text {
    font-size: 24rpx;
    color: #4a90e2;
    text-align: center;
  }

  .radar-background {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 240rpx;
    height: 240rpx;
  }

  .grid-circle {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border: 2rpx solid rgba(74, 144, 226, 0.3);
    border-radius: 50%;
    background: transparent;
  }

  .level-1 {
    width: 80rpx;
    height: 80rpx;
    border-color: rgba(74, 144, 226, 0.5);
  }

  .level-2 {
    width: 160rpx;
    height: 160rpx;
    border-color: rgba(74, 144, 226, 0.3);
  }

  .level-3 {
    width: 240rpx;
    height: 240rpx;
    border-color: rgba(74, 144, 226, 0.2);
  }

  .grid-line {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2rpx;
    height: 120rpx;
    background: rgba(74, 144, 226, 0.3);
    transform-origin: center bottom;
  }

  .line-1 {
    transform: translate(-50%, -100%) rotate(0deg);
  }

  .line-2 {
    transform: translate(-50%, -100%) rotate(60deg);
  }

  .line-3 {
    transform: translate(-50%, -100%) rotate(120deg);
  }

  .line-4 {
    transform: translate(-50%, -100%) rotate(180deg);
  }

  .line-5 {
    transform: translate(-50%, -100%) rotate(240deg);
  }

  .line-6 {
    transform: translate(-50%, -100%) rotate(300deg);
  }

  .data-fill-area {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 240rpx;
    height: 240rpx;
    z-index: 5;
  }

  .connection-lines {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 320rpx;
    height: 320rpx;
    z-index: 6;
  }

  .connection-line {
    position: absolute;
    transition: all 0.5s ease;
  }

  .data-points {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 320rpx;
    height: 320rpx;
    z-index: 10;
  }

  .data-point {
    position: absolute;
    width: 12rpx;
    height: 12rpx;
    border-radius: 50%;
    transition: all 0.5s ease;
  }

  .axis-label {
    position: absolute;
    color: #2c3e50;
    font-size: 24rpx;
    font-weight: 500;
    width: 100rpx;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .axis-value {
    display: block;
    color: #4a90e2;
    font-size: 36rpx;
    font-weight: 600;
    margin-top: 8rpx;
    text-align: center;
  }
  
  .axis-max-value {
    color: #999;
    font-size: 22rpx;
    margin-left: 4rpx;
  }
  
  .dynamic-axis-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  
  .label-name {
    font-size: 24rpx;
    color: #2c3e50;
    margin-bottom: 4rpx;
    white-space: nowrap;
  }
  
  .label-value {
    font-size: 36rpx;
    font-weight: 600;
    color: #4a90e2;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .axis-label.top {
    top: 0;
    left: 50%;
    transform: translateX(-50%);
  }

  .axis-label.top-right {
    top: 25%;
    right: 10%;
  }

  .axis-label.bottom-right {
    bottom: 25%;
    right: 10%;
  }

  .axis-label.bottom {
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
  }

  .axis-label.bottom-left {
    bottom: 25%;
    left: 10%;
  }

  .axis-label.top-left {
    top: 25%;
    left: 10%;
  }

  /* 最佳数据相关样式 */
  .best-data-section {
    margin-top: 20rpx;
    margin-right: 20rpx;
    position: relative;
  }

  .season-toggle {
    position: absolute;
    top: -16rpx;
    right: 0;
    display: flex;
    background: #f5f7fa;
    border-radius: 40rpx;
    padding: 4rpx;
    margin: 0;
    max-width: 300rpx;
    align-items: center;
  }

  .season-btn {
    flex: 1;
    text-align: center;
    padding: 12rpx 20rpx;
    font-size: 24rpx;
    border-radius: 40rpx;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;

    &.active {
      background: linear-gradient(135deg, #4a90e2, #5c6bc0);
      color: white;
      box-shadow: 0 4rpx 16rpx rgba(74, 144, 226, 0.3);
    }
  }

  .best-data-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20rpx;
    margin-top: 60rpx;
  }

  .best-data-item {
    background: #f5f7fa;
    border-radius: 20rpx;
    padding: 24rpx;
    display: flex;
    flex-direction: column;
  }

  .best-data-value {
    font-size: 48rpx;
    font-weight: 600;
    color: #4a90e2;
    margin-bottom: 10rpx;
  }

  .best-data-meta {
    font-size: 24rpx;
    color: #7f8c8d;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
  }

  .best-data-label {
    font-size: 26rpx;
    color: #7f8c8d;
    margin-bottom: 4rpx;
  }

  .best-data-date {
    font-size: 22rpx;
    opacity: 0.8;
  }

  .season-data-panel {
    display: none;

    &.active {
      display: block;
    }
  }

  /* rpx单位自动适配不同屏幕，无需额外媒体查询 */
</style>
