<template>
  <view class="career-skeleton">
    <!-- 概览骨架屏 -->
    <template v-if="type === 'overview'">
      <!-- 数据卡片骨架 -->
      <view class="stats-cards">
        <view v-for="n in 4" :key="n" class="stat-card skeleton">
          <view class="stat-value skeleton-text"></view>
          <view class="stat-label skeleton-text short"></view>
        </view>
      </view>
      
      <!-- 雷达图骨架 -->
      <view class="radar-section skeleton">
        <view class="section-title skeleton-text medium"></view>
        <view class="radar-chart skeleton-circle"></view>
        <view class="radar-labels">
          <view v-for="n in 6" :key="n" class="radar-label skeleton-text short"></view>
        </view>
      </view>
      
      <!-- 基础信息骨架 -->
      <view class="info-section skeleton">
        <view class="section-title skeleton-text medium"></view>
        <view class="info-grid">
          <view v-for="n in 6" :key="n" class="info-item">
            <view class="info-label skeleton-text short"></view>
            <view class="info-value skeleton-text"></view>
          </view>
        </view>
      </view>
    </template>

    <!-- 球队历史骨架屏 -->
    <template v-else-if="type === 'teamHistory'">
      <view class="team-list">
        <view v-for="n in 3" :key="n" class="team-item skeleton">
          <view class="team-logo skeleton-circle small"></view>
          <view class="team-info">
            <view class="team-name skeleton-text"></view>
            <view class="team-period skeleton-text short"></view>
          </view>
          <view class="team-stats">
            <view class="stat-item skeleton-text short"></view>
            <view class="stat-item skeleton-text short"></view>
          </view>
        </view>
      </view>
    </template>

    <!-- 荣誉骨架屏 -->
    <template v-else-if="type === 'honors'">
      <view class="honors-grid">
        <view v-for="n in 6" :key="n" class="honor-item skeleton">
          <view class="honor-icon skeleton-circle"></view>
          <view class="honor-name skeleton-text"></view>
          <view class="honor-date skeleton-text short"></view>
        </view>
      </view>
    </template>

    <!-- 数据统计骨架屏 -->
    <template v-else-if="type === 'dataStats'">
      <!-- 统计图表骨架 -->
      <view class="chart-section skeleton">
        <view class="section-title skeleton-text medium"></view>
        <view class="chart-container skeleton-rect tall"></view>
      </view>
      
      <!-- 详细数据列表 -->
      <view class="data-list">
        <view v-for="n in 8" :key="n" class="data-row skeleton">
          <view class="data-label skeleton-text"></view>
          <view class="data-value skeleton-text short"></view>
        </view>
      </view>
    </template>

    <!-- 比赛列表骨架屏 -->
    <template v-else-if="type === 'matchList'">
      <view class="match-list">
        <view v-for="n in 5" :key="n" class="match-item skeleton">
          <view class="match-date skeleton-text short"></view>
          <view class="match-teams">
            <view class="team">
              <view class="team-logo skeleton-circle small"></view>
              <view class="team-name skeleton-text"></view>
            </view>
            <view class="match-score skeleton-text short"></view>
            <view class="team">
              <view class="team-logo skeleton-circle small"></view>
              <view class="team-name skeleton-text"></view>
            </view>
          </view>
          <view class="player-stats">
            <view class="stat skeleton-text short"></view>
            <view class="stat skeleton-text short"></view>
            <view class="stat skeleton-text short"></view>
          </view>
        </view>
      </view>
    </template>

    <!-- 默认骨架屏 -->
    <template v-else>
      <view class="default-skeleton">
        <view v-for="n in 5" :key="n" class="skeleton-item skeleton">
          <view class="skeleton-text"></view>
          <view class="skeleton-text short"></view>
        </view>
      </view>
    </template>
  </view>
</template>

<script setup>
import { defineProps } from 'vue';

// 组件属性
const props = defineProps({
  // 骨架屏类型
  type: {
    type: String,
    default: 'overview',
    validator: (value) => {
      return ['overview', 'teamHistory', 'honors', 'dataStats', 'matchList'].includes(value);
    }
  }
});
</script>

<style scoped lang="scss">
.career-skeleton {
  padding: 20rpx;
  background-color: #f5f7fa;
}

/* 基础骨架样式 */
.skeleton {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
}

.skeleton-text {
  height: 32rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  border-radius: 4rpx;
  margin-bottom: 16rpx;
  animation: skeleton-loading 1.5s ease-in-out infinite;
  
  &.short {
    width: 120rpx;
  }
  
  &.medium {
    width: 200rpx;
  }
}

.skeleton-circle {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  border-radius: 50%;
  animation: skeleton-loading 1.5s ease-in-out infinite;
  
  &.small {
    width: 60rpx;
    height: 60rpx;
  }
  
  &:not(.small) {
    width: 120rpx;
    height: 120rpx;
  }
}

.skeleton-rect {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  border-radius: 8rpx;
  animation: skeleton-loading 1.5s ease-in-out infinite;
  
  &.tall {
    height: 300rpx;
  }
}

/* 动画效果 */
@keyframes skeleton-loading {
  0% {
    background-position: -200rpx 0;
  }
  100% {
    background-position: calc(200rpx + 100%) 0;
  }
}

/* 概览页面骨架 */
.stats-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 24rpx;
  
  .stat-card {
    flex: 1;
    min-width: 160rpx;
    text-align: center;
    
    .stat-value {
      height: 48rpx;
      margin-bottom: 12rpx;
    }
    
    .stat-label {
      height: 24rpx;
      margin: 0 auto;
    }
  }
}

.radar-section {
  text-align: center;
  
  .section-title {
    margin-bottom: 32rpx;
  }
  
  .radar-chart {
    width: 240rpx;
    height: 240rpx;
    margin: 0 auto 32rpx;
  }
  
  .radar-labels {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 16rpx;
  }
}

.info-section {
  .section-title {
    margin-bottom: 24rpx;
  }
  
  .info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24rpx;
    
    .info-item {
      .info-label {
        margin-bottom: 8rpx;
      }
    }
  }
}

/* 球队历史页面骨架 */
.team-list {
  .team-item {
    display: flex;
    align-items: center;
    gap: 24rpx;
    
    .team-info {
      flex: 1;
      
      .team-period {
        margin-top: 8rpx;
      }
    }
    
    .team-stats {
      display: flex;
      flex-direction: column;
      gap: 8rpx;
    }
  }
}

/* 荣誉页面骨架 */
.honors-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
  
  .honor-item {
    text-align: center;
    
    .honor-icon {
      width: 80rpx;
      height: 80rpx;
      margin: 0 auto 16rpx;
    }
    
    .honor-date {
      margin-top: 8rpx;
      margin: 0 auto;
    }
  }
}

/* 数据统计页面骨架 */
.chart-section {
  .section-title {
    margin-bottom: 24rpx;
  }
  
  .chart-container {
    width: 100%;
  }
}

.data-list {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  
  .data-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16rpx 0;
    
    &:not(:last-child) {
      border-bottom: 1rpx solid #f0f0f0;
    }
    
    .data-label {
      flex: 1;
    }
    
    .data-value {
      width: 100rpx;
    }
  }
}

/* 比赛列表页面骨架 */
.match-list {
  .match-item {
    .match-date {
      margin-bottom: 16rpx;
      margin: 0 auto;
    }
    
    .match-teams {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 16rpx 0;
      
      .team {
        display: flex;
        align-items: center;
        gap: 12rpx;
        flex: 1;
        
        .team-name {
          flex: 1;
        }
      }
      
      .match-score {
        margin: 0 24rpx;
        text-align: center;
      }
    }
    
    .player-stats {
      display: flex;
      justify-content: space-around;
      margin-top: 16rpx;
    }
  }
}

/* 默认骨架屏 */
.default-skeleton {
  .skeleton-item {
    display: flex;
    flex-direction: column;
    gap: 12rpx;
  }
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .stats-cards {
    .stat-card {
      min-width: calc(50% - 8rpx);
    }
  }
  
  .honors-grid {
    grid-template-columns: 1fr;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
}
</style>