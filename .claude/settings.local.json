{"permissions": {"allow": ["<PERSON><PERSON>(mvn test:*)", "<PERSON><PERSON>(mvn:*)", "Bash(rm:*)", "<PERSON><PERSON>(mysql:*)", "Bash(grep:*)", "Bash(mv src/test/java/cn/iocoder/yudao/module/operation/processor/impl/DifferenceRefundProcessorTest.java src/test/java/cn/iocoder/yudao/module/operation/processor/impl/DifferenceRefundProcessorTest.java.disabled)", "<PERSON><PERSON>(find . -name \"*PlayerCareer*.java\" -not -name \"*MinimalTest.java\" -exec mv {} {}.disabled ;)", "Bash(find:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(cp:*)"], "deny": []}}