-- 球员生涯表字段修复脚本
-- 用于修复 PlayerCareerStatsDO 与数据库表 sd_player_career_stats 之间的字段不匹配问题

-- 检查当前表结构
-- DESCRIBE sd_player_career_stats;

-- ================================
-- 1. 修复字段映射不一致问题
-- ================================

-- 如果表中没有对应字段，则添加（使用 IF NOT EXISTS 语法）
-- 注意：MySQL 8.0+ 支持 IF NOT EXISTS，较早版本需要使用存储过程

-- 1.1 添加缺失的基础统计字段
ALTER TABLE sd_player_career_stats 
ADD COLUMN IF NOT EXISTS total_two_points_made INT DEFAULT 0 COMMENT '总二分命中数',
ADD COLUMN IF NOT EXISTS total_two_points_attempted INT DEFAULT 0 COMMENT '总二分出手数',
ADD COLUMN IF NOT EXISTS two_point_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT '二分命中率(%)';

-- 1.2 添加高阶统计字段（这些字段在实体类中标记为 exist = false，但可以选择性添加到数据库）
ALTER TABLE sd_player_career_stats 
ADD COLUMN IF NOT EXISTS effective_field_goal_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT '有效投篮命中率(%)',
ADD COLUMN IF NOT EXISTS offensive_rebound_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '进攻篮板率(%)',
ADD COLUMN IF NOT EXISTS defensive_rebound_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '防守篮板率(%)',
ADD COLUMN IF NOT EXISTS assist_turnover_ratio DECIMAL(5,2) DEFAULT 0.00 COMMENT '助攻失误比',
ADD COLUMN IF NOT EXISTS plus_minus DECIMAL(8,2) DEFAULT 0.00 COMMENT '净胜分';

-- ================================
-- 2. 验证表结构修复结果
-- ================================

-- 查看修复后的表结构
DESCRIBE sd_player_career_stats;

-- 查看表的索引情况
SHOW INDEX FROM sd_player_career_stats;

-- ================================
-- 3. 数据完整性检查
-- ================================

-- 检查是否有数据需要更新
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN total_two_points_made IS NULL THEN 1 END) as null_two_points_made,
    COUNT(CASE WHEN two_point_percentage IS NULL THEN 1 END) as null_two_point_percentage,
    COUNT(CASE WHEN effective_field_goal_percentage IS NULL THEN 1 END) as null_effective_fg_percentage
FROM sd_player_career_stats;

-- ================================
-- 4. 示例数据更新（可选）
-- ================================

-- 如果需要计算二分相关数据，可以使用以下更新语句
-- UPDATE sd_player_career_stats 
-- SET 
--     total_two_points_made = total_field_goals_made - total_three_points_made,
--     total_two_points_attempted = total_field_goals_attempted - total_three_points_attempted
-- WHERE total_two_points_made = 0 AND total_field_goals_made > 0;

-- 计算二分命中率
-- UPDATE sd_player_career_stats 
-- SET two_point_percentage = CASE 
--     WHEN total_two_points_attempted > 0 THEN (total_two_points_made / total_two_points_attempted * 100)
--     ELSE 0 
-- END
-- WHERE two_point_percentage = 0;

-- ================================
-- 5. 添加有用的索引（可选）
-- ================================

-- 为新字段添加索引以提高查询性能
-- CREATE INDEX idx_two_point_percentage ON sd_player_career_stats(two_point_percentage DESC);
-- CREATE INDEX idx_effective_fg_percentage ON sd_player_career_stats(effective_field_goal_percentage DESC);

COMMIT;

-- 修复完成提示
SELECT '球员生涯表字段修复完成！' as status;