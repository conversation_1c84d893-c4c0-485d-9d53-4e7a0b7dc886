# 动态统计数据渲染修复

## 🎯 修复目标

将硬编码的统计项改为动态渲染，让前端能够根据后端返回的数据自动调整显示内容。

## 🔧 修复内容

### 1. 模板层修改

**修复前（硬编码）：**
```vue
<view class="stats-grid">
  <view class="stat-item">
    <view class="stat-label">场均得分</view>
    <view class="stat-value">{{ coreStatsData.avgPoints || '--' }}</view>
    <view class="stat-rank">第{{ playerStats.pointsRank || '--' }}名</view>
  </view>
  <view class="stat-item">
    <view class="stat-label">场均篮板</view>
    <view class="stat-value">{{ coreStatsData.avgRebounds || '--' }}</view>
    <view class="stat-rank">第{{ playerStats.reboundsRank || '--' }}名</view>
  </view>
  <!-- 更多硬编码项... -->
</view>
```

**修复后（动态渲染）：**
```vue
<view class="stats-grid" :style="{ gridTemplateColumns: `repeat(${displayBasicStats.length}, 1fr)` }">
  <view class="stat-item" v-for="(stat, index) in displayBasicStats" :key="index">
    <view class="stat-label">{{ stat.label }}</view>
    <view class="stat-value">{{ stat.value || '--' }}</view>
    <view class="stat-rank">第{{ stat.rank || '--' }}名</view>
  </view>
</view>
```

### 2. 逻辑层修改

**修复前（硬编码提取）：**
```javascript
const coreStatsData = computed(() => {
  const basicStats = apiCoreStatsData.value.basicStats;
  const avgPoints = basicStats.find(stat => stat.label === '场均得分')?.value || '--';
  const avgRebounds = basicStats.find(stat => stat.label === '场均篮板')?.value || '--';
  // 硬编码字段提取...
  return { avgPoints, avgRebounds, ... };
});
```

**修复后（动态处理）：**
```javascript
const displayBasicStats = computed(() => {
  if (!apiCoreStatsData.value || !apiCoreStatsData.value.basicStats) {
    return [];
  }
  
  // 直接返回后端提供的基础统计数据，最多显示4个
  return apiCoreStatsData.value.basicStats.slice(0, 4).map(stat => ({
    label: stat.label,
    value: stat.value,
    unit: stat.unit,
    rank: stat.rank || '--'
  }));
});
```

### 3. 样式层修改

**修复前（固定4列）：**
```scss
.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0;
  padding: 20rpx 0;
}
```

**修复后（动态列数）：**
```scss
.stats-grid {
  display: grid;
  /* 动态列数通过内联样式设置 */
  gap: 0;
  padding: 20rpx 0;
}
```

## 🚀 优势

### 1. 灵活性
- 后端可以返回任意数量的基础统计项
- 前端自动适应显示，无需修改代码

### 2. 可维护性
- 消除硬编码，减少维护成本
- 新增统计项只需后端配置，前端自动支持

### 3. 扩展性
- 支持后端动态配置统计项
- 支持不同球员显示不同的统计维度

## 📊 数据流

```
后端API返回 → apiCoreStatsData → displayBasicStats计算属性 → 动态模板渲染
```

### 后端数据结构
```json
{
  "coreStats": {
    "basicStats": [
      {
        "label": "场均得分",
        "value": "18.6",
        "unit": "分",
        "rank": "1"
      },
      {
        "label": "场均篮板",
        "value": "6.0",
        "unit": "个",
        "rank": "3"
      }
      // 后端可以返回更多或更少的统计项
    ]
  }
}
```

### 前端处理结果
```javascript
displayBasicStats = [
  { label: "场均得分", value: "18.6", unit: "分", rank: "1" },
  { label: "场均篮板", value: "6.0", unit: "个", rank: "3" },
  // 动态数量
]
```

## 🎨 视觉效果

- **2个统计项**: 网格显示为 `repeat(2, 1fr)`
- **3个统计项**: 网格显示为 `repeat(3, 1fr)`  
- **4个统计项**: 网格显示为 `repeat(4, 1fr)`
- **超过4个**: 只显示前4个（可配置）

## 🧪 测试场景

1. **标准场景**: 后端返回4个基础统计项
2. **精简场景**: 后端返回2-3个统计项
3. **扩展场景**: 后端返回超过4个统计项
4. **异常场景**: 后端返回空数据或格式错误

## ✅ 验证要点

1. 统计项数量能否正确适应
2. 网格布局是否均匀分布
3. 数据显示是否正确
4. 异常情况处理是否合理

这样修改后，前端就能完全适应后端的动态配置了！
