/*
 Navicat Premium Data Transfer

 Source Server         : localhost_3307
 Source Server Type    : MySQL
 Source Server Version : 80100
 Source Host           : localhost:3307
 Source Schema         : saidian2

 Target Server Type    : MySQL
 Target Server Version : 80100
 File Encoding         : 65001

 Date: 20/04/2024 20:38:30
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for sd_player
-- ----------------------------
DROP TABLE IF EXISTS `sd_player`;
CREATE TABLE `sd_player`
(
    `id`                     bigint                                                       NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `member_user_id`         bigint                                                       NOT NULL COMMENT '用户id',
    `height`                 double                                                       NULL     DEFAULT NULL COMMENT '身高',
    `weight`                 int                                                          NULL     DEFAULT NULL COMMENT '体重',
    `ratings`                int                                                          NULL     DEFAULT NULL COMMENT '能力值',
    `level` int NULL DEFAULT NULL COMMENT '等级',
    `position`               varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL     DEFAULT NULL COMMENT '球员位置',
    `wingspan`               float                                                        NULL     DEFAULT NULL COMMENT '臂展',
    `standing_reach`         float                                                        NULL     DEFAULT NULL COMMENT '站立摸高',
    `vertical_jump`          float                                                        NULL     DEFAULT NULL COMMENT '助跑摸高',
    `speed`                  float                                                        NULL     DEFAULT NULL COMMENT '速度',
    `percentage_of_body_fat` float                                                        NULL     DEFAULT NULL COMMENT '体脂率',
    `attendance_rate`        float                                                        NULL     DEFAULT NULL COMMENT '出勤率',
    `credit_score`           int                                                          NULL     DEFAULT NULL COMMENT '信用度',
    `civilized_level`        int                                                          NULL     DEFAULT NULL COMMENT '文明度',
    `winning_percentage`     float                                                        NULL     DEFAULT NULL COMMENT '胜率',
    `number_of_mvp`          int                                                          NULL     DEFAULT NULL COMMENT 'MVP次数',
    `creator`                varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '创建者',
    `create_time`            datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`                varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL     DEFAULT '' COMMENT '更新者',
    `update_time`            datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`                bit(1)                                                       NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 4
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT = '球员表'
  ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
