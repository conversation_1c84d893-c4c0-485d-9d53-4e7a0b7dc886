package cn.iocoder.yudao.module.operation.service.ability;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 赛点篮球应用-生涯模块能力评分算法 Java实现
 * 
 * 该模块实现了基于球员比赛数据的能力评分计算功能，包括:
 * 1. 根据场上位置确定评分权重
 * 2. 计算各维度评分
 * 3. 加权计算真实能力值
 * 4. 根据比赛结果调整展示能力值
 * 
 * <AUTHOR>
 * @date 2025-07-27
 * @version 2.0.0 (Java实现版本)
 */
@Slf4j
@Component
public class PlayerAbilityRatingCalculator {

    /**
     * 球员场上位置枚举
     */
    public enum Position {
        PG(1, "控球后卫"),
        SG(2, "得分后卫"),
        SF(3, "小前锋"),
        PF(4, "大前锋"),
        C(5, "中锋");

        private final int code;
        private final String name;

        Position(int code, String name) {
            this.code = code;
            this.name = name;
        }

        public int getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static Position fromCode(int code) {
            for (Position position : values()) {
                if (position.code == code) {
                    return position;
                }
            }
            throw new IllegalArgumentException("Invalid position code: " + code);
        }
    }

    /**
     * 评分维度枚举
     */
    public enum RatingDimension {
        EFFICIENCY("效率"),
        SCORING("得分"),
        REBOUNDING("篮板"),
        ASSISTING("助攻"),
        DEFENSE("防守"),
        TURNOVER("失误控制"),
        FOUL("犯规控制");

        private final String name;

        RatingDimension(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }
    }

    /**
     * 球员统计数据VO
     */
    @Data
    public static class PlayerStatsVO {
        // 基础数据
        private Double points;
        private Double rebounds;
        private Double offensiveRebounds;
        private Double defensiveRebounds;
        private Double assists;
        private Double steals;
        private Double blocks;
        private Double turnovers;
        private Double fouls;

        // 投篮数据
        private Double fieldGoalsMade;
        private Double fieldGoalsAttempted;
        private Double threePointersMade;
        private Double threePointersAttempted;
        private Double freeThrowsMade;
        private Double freeThrowsAttempted;

        // 命中率
        private Double fieldGoalPercentage;
        private Double twoPointPercentage;
        private Double threePointPercentage;
        private Double freeThrowPercentage;

        // 高级数据
        private Double trueShootingPercentage;
        private Double effectiveFieldGoalPercentage;
        private Double assistToTurnoverRatio;
        private Double efficiency;
    }

    /**
     * 球员排名数据VO
     */
    @Data
    public static class PlayerRankVO {
        private Integer efficiencyRank;
        private Integer scoringRank;
        private Integer trueShootingRank;
        private Integer twoPointPercentageRank;
        private Integer threePointPercentageRank;
        private Integer freeThrowPercentageRank;
        private Integer offensiveReboundRank;
        private Integer defensiveReboundRank;
        private Integer assistRank;
        private Integer assistToTurnoverRatioRank;
        private Integer stealRank;
        private Integer blockRank;
        private Integer turnoverRank;
        private Integer foulRank;
    }

    /**
     * 能力评分结果VO
     */
    @Data
    public static class AbilityRatingResultVO {
        // 7维度评分
        private Double efficiencyRating;
        private Double scoringRating;
        private Double reboundingRating;
        private Double assistRating;
        private Double defenseRating;
        private Double turnoverRating;
        private Double foulRating;

        // 综合评分
        private Double realAbilityRating;
        private Double displayedAbilityRating;

        // 位置适应度
        private Map<Position, Double> positionFit;
        private Position bestPosition;
    }

    /**
     * 数据池大小
     */
    private final int dataPoolSize;

    /**
     * 不同场上位置的能力值计算权重
     */
    private final Map<Position, Map<RatingDimension, Double>> positionWeights;

    /**
     * 防守评分中抢断与盖帽的权重
     */
    private final Map<Position, Map<String, Double>> defenseWeights;

    public PlayerAbilityRatingCalculator() {
        this.dataPoolSize = 1000; // 默认数据池大小
        this.positionWeights = initializePositionWeights();
        this.defenseWeights = initializeDefenseWeights();
    }

    /**
     * 初始化位置权重配置
     */
    private Map<Position, Map<RatingDimension, Double>> initializePositionWeights() {
        Map<Position, Map<RatingDimension, Double>> weights = new HashMap<>();

        // 控球后卫权重
        Map<RatingDimension, Double> pgWeights = new HashMap<>();
        pgWeights.put(RatingDimension.EFFICIENCY, 0.20);
        pgWeights.put(RatingDimension.SCORING, 0.30);
        pgWeights.put(RatingDimension.REBOUNDING, 0.10);
        pgWeights.put(RatingDimension.ASSISTING, 0.40);
        pgWeights.put(RatingDimension.DEFENSE, 0.20);
        pgWeights.put(RatingDimension.TURNOVER, -0.10);
        pgWeights.put(RatingDimension.FOUL, -0.10);
        weights.put(Position.PG, pgWeights);

        // 得分后卫权重
        Map<RatingDimension, Double> sgWeights = new HashMap<>();
        sgWeights.put(RatingDimension.EFFICIENCY, 0.20);
        sgWeights.put(RatingDimension.SCORING, 0.40);
        sgWeights.put(RatingDimension.REBOUNDING, 0.10);
        sgWeights.put(RatingDimension.ASSISTING, 0.20);
        sgWeights.put(RatingDimension.DEFENSE, 0.30);
        sgWeights.put(RatingDimension.TURNOVER, -0.10);
        sgWeights.put(RatingDimension.FOUL, -0.10);
        weights.put(Position.SG, sgWeights);

        // 小前锋权重
        Map<RatingDimension, Double> sfWeights = new HashMap<>();
        sfWeights.put(RatingDimension.EFFICIENCY, 0.20);
        sfWeights.put(RatingDimension.SCORING, 0.40);
        sfWeights.put(RatingDimension.REBOUNDING, 0.20);
        sfWeights.put(RatingDimension.ASSISTING, 0.20);
        sfWeights.put(RatingDimension.DEFENSE, 0.20);
        sfWeights.put(RatingDimension.TURNOVER, -0.10);
        sfWeights.put(RatingDimension.FOUL, -0.10);
        weights.put(Position.SF, sfWeights);

        // 大前锋权重
        Map<RatingDimension, Double> pfWeights = new HashMap<>();
        pfWeights.put(RatingDimension.EFFICIENCY, 0.20);
        pfWeights.put(RatingDimension.SCORING, 0.30);
        pfWeights.put(RatingDimension.REBOUNDING, 0.40);
        pfWeights.put(RatingDimension.ASSISTING, 0.10);
        pfWeights.put(RatingDimension.DEFENSE, 0.20);
        pfWeights.put(RatingDimension.TURNOVER, -0.10);
        pfWeights.put(RatingDimension.FOUL, -0.10);
        weights.put(Position.PF, pfWeights);

        // 中锋权重
        Map<RatingDimension, Double> cWeights = new HashMap<>();
        cWeights.put(RatingDimension.EFFICIENCY, 0.20);
        cWeights.put(RatingDimension.SCORING, 0.20);
        cWeights.put(RatingDimension.REBOUNDING, 0.40);
        cWeights.put(RatingDimension.ASSISTING, 0.10);
        cWeights.put(RatingDimension.DEFENSE, 0.30);
        cWeights.put(RatingDimension.TURNOVER, -0.10);
        cWeights.put(RatingDimension.FOUL, -0.10);
        weights.put(Position.C, cWeights);

        return weights;
    }

    /**
     * 初始化防守权重配置
     */
    private Map<Position, Map<String, Double>> initializeDefenseWeights() {
        Map<Position, Map<String, Double>> weights = new HashMap<>();

        weights.put(Position.PG, Map.of("steal", 0.70, "block", 0.30));
        weights.put(Position.SG, Map.of("steal", 0.70, "block", 0.30));
        weights.put(Position.SF, Map.of("steal", 0.50, "block", 0.50));
        weights.put(Position.PF, Map.of("steal", 0.30, "block", 0.70));
        weights.put(Position.C, Map.of("steal", 0.30, "block", 0.70));

        return weights;
    }

    /**
     * 计算效率评分
     * 
     * @param efficiencyRank 出场效率在数据池中的排名
     * @return 效率评分(0-100)
     */
    public Double calculateEfficiencyRating(Integer efficiencyRank) {
        if (efficiencyRank == null || efficiencyRank <= 0) {
            return 0.0;
        }
        // 效率评分 = (数据池球员总数-球员出场效率排名)/数据池球员总数*100
        return (double) (dataPoolSize - efficiencyRank) / dataPoolSize * 100;
    }

    /**
     * 计算得分评分
     * 
     * @param scoringRank 得分排名
     * @param trueShootingRank 真实命中率排名
     * @param twoPointPercentageRank 二分命中率排名
     * @param threePointPercentageRank 三分命中率排名
     * @param freeThrowPercentageRank 罚球命中率排名
     * @return 得分评分(0-100)
     */
    public Double calculateScoringRating(Integer scoringRank, Integer trueShootingRank,
                                       Integer twoPointPercentageRank, Integer threePointPercentageRank,
                                       Integer freeThrowPercentageRank) {
        // 得分评分 = 真实命中率排名(40%) + 得分排名(30%) + 二分命中率排名(10%) + 三分命中率排名(10%) + 罚球命中率排名(10%)
        double tsScore = calculateRankScore(trueShootingRank) * 0.40;
        double scoringScore = calculateRankScore(scoringRank) * 0.30;
        double twoPointScore = calculateRankScore(twoPointPercentageRank) * 0.10;
        double threePointScore = calculateRankScore(threePointPercentageRank) * 0.10;
        double freeThrowScore = calculateRankScore(freeThrowPercentageRank) * 0.10;

        return tsScore + scoringScore + twoPointScore + threePointScore + freeThrowScore;
    }

    /**
     * 计算篮板评分
     * 
     * @param offensiveReboundRank 进攻篮板排名
     * @param defensiveReboundRank 防守篮板排名
     * @return 篮板评分(0-100)
     */
    public Double calculateReboundingRating(Integer offensiveReboundRank, Integer defensiveReboundRank) {
        // 篮板评分 = 进攻篮板排名(50%) + 防守篮板排名(50%)
        double offensiveScore = calculateRankScore(offensiveReboundRank) * 0.50;
        double defensiveScore = calculateRankScore(defensiveReboundRank) * 0.50;

        return offensiveScore + defensiveScore;
    }

    /**
     * 计算助攻评分
     * 
     * @param assistRank 助攻排名
     * @param assistToTurnoverRatioRank 助攻失误比排名
     * @return 助攻评分(0-100)
     */
    public Double calculateAssistRating(Integer assistRank, Integer assistToTurnoverRatioRank) {
        // 助攻评分 = 助攻排名(50%) + 助攻失误比排名(50%)
        double assistScore = calculateRankScore(assistRank) * 0.50;
        double ratioScore = calculateRankScore(assistToTurnoverRatioRank) * 0.50;

        return assistScore + ratioScore;
    }

    /**
     * 计算防守评分
     * 
     * @param stealRank 抢断排名
     * @param blockRank 盖帽排名
     * @param position 球员位置
     * @return 防守评分(0-100)
     */
    public Double calculateDefenseRating(Integer stealRank, Integer blockRank, Position position) {
        // 根据场上位置不同，抢断和盖帽的权重不同
        Map<String, Double> weights = defenseWeights.get(position);

        // 防守评分 = 抢断排名 * 抢断权重 + 盖帽排名 * 盖帽权重
        double stealScore = calculateRankScore(stealRank) * weights.get("steal");
        double blockScore = calculateRankScore(blockRank) * weights.get("block");

        return stealScore + blockScore;
    }

    /**
     * 计算失误控制评分
     * 
     * @param turnoverRank 失误排名(越少越好)
     * @return 失误控制评分(0-100)
     */
    public Double calculateTurnoverRating(Integer turnoverRank) {
        // 失误评分 = (数据池球员总数-球员失误排名)/数据池球员总数*100
        // 注意：失误是负向指标，排名越高越好(失误越少)
        return calculateRankScore(turnoverRank);
    }

    /**
     * 计算犯规控制评分
     * 
     * @param foulRank 犯规排名(越少越好)
     * @return 犯规控制评分(0-100)
     */
    public Double calculateFoulRating(Integer foulRank) {
        // 犯规评分 = (数据池球员总数-球员犯规排名)/数据池球员总数*100
        // 注意：犯规是负向指标，排名越高越好(犯规越少)
        return calculateRankScore(foulRank);
    }

    /**
     * 计算真实能力值
     * 
     * @param ratings 各维度评分
     * @param position 球员位置
     * @return 真实能力值(0-100)
     */
    public Double calculateRealAbilityRating(Map<RatingDimension, Double> ratings, Position position) {
        // 获取对应场上位置的权重配置
        Map<RatingDimension, Double> weights = positionWeights.get(position);

        // 加权计算真实能力值
        double realRating = 0.0;
        for (Map.Entry<RatingDimension, Double> entry : ratings.entrySet()) {
            RatingDimension dimension = entry.getKey();
            Double rating = entry.getValue();
            Double weight = weights.get(dimension);
            
            if (rating != null && weight != null) {
                realRating += rating * weight;
            }
        }

        // 确保评分在0-100范围内
        return Math.max(0.0, Math.min(100.0, realRating));
    }

    /**
     * 计算新的展示能力值
     * 
     * @param realRating 真实能力值
     * @param currentDisplayedRating 当前展示能力值
     * @param isWin 是否获胜
     * @param seasonGames 赛季参赛次数
     * @param streak 连胜/连败场次(胜利为正，失败为负)
     * @return 新的展示能力值
     */
    public Double calculateNewDisplayedRating(Double realRating, Double currentDisplayedRating,
                                            Boolean isWin, Integer seasonGames, Integer streak) {
        double change = calculateDisplayedRatingChange(realRating, currentDisplayedRating, 
                                                     isWin, seasonGames, streak);

        double newRating;
        if (isWin) {
            // 胜利增加能力值
            newRating = currentDisplayedRating + change;
        } else {
            // 失败减少能力值
            newRating = currentDisplayedRating - change;
        }

        // 确保能力值在0-100范围内
        return Math.max(0.0, Math.min(100.0, newRating));
    }

    /**
     * 计算各位置适应度
     * 
     * @param ratings 各维度评分
     * @return 位置适应度Map
     */
    public Map<Position, Double> calculatePositionFit(Map<RatingDimension, Double> ratings) {
        Map<Position, Double> positionFit = new HashMap<>();

        for (Position position : Position.values()) {
            // 获取对应场上位置的权重配置
            Map<RatingDimension, Double> weights = positionWeights.get(position);

            // 计算匹配度分数
            double fitScore = 0.0;
            for (Map.Entry<RatingDimension, Double> entry : ratings.entrySet()) {
                RatingDimension dimension = entry.getKey();
                Double rating = entry.getValue();
                Double weight = weights.get(dimension);

                if (rating != null && weight != null) {
                    fitScore += rating * Math.abs(weight);  // 使用权重绝对值计算匹配度
                }
            }

            positionFit.put(position, fitScore);
        }

        // 将匹配度转换为百分比
        double totalFit = positionFit.values().stream().mapToDouble(Double::doubleValue).sum();
        if (totalFit > 0) {
            for (Position position : Position.values()) {
                double fitPercentage = (positionFit.get(position) / totalFit) * 100;
                positionFit.put(position, BigDecimal.valueOf(fitPercentage)
                    .setScale(2, RoundingMode.HALF_UP).doubleValue());
            }
        }

        return positionFit;
    }

    /**
     * 获取最适合位置
     * 
     * @param positionFit 位置适应度Map
     * @return 最适合的位置
     */
    public Position getBestPosition(Map<Position, Double> positionFit) {
        return positionFit.entrySet().stream()
            .max(Map.Entry.comparingByValue())
            .map(Map.Entry::getKey)
            .orElse(Position.SF); // 默认小前锋
    }

    /**
     * 完整的能力评分计算
     * 
     * @param playerRank 球员排名数据
     * @param position 球员位置
     * @param currentDisplayedRating 当前展示能力值
     * @param isWin 是否获胜
     * @param seasonGames 赛季场次
     * @param streak 连胜连败
     * @return 完整的能力评分结果
     */
    public AbilityRatingResultVO calculateAbilityRating(PlayerRankVO playerRank, Position position,
                                                       Double currentDisplayedRating, Boolean isWin,
                                                       Integer seasonGames, Integer streak) {
        AbilityRatingResultVO result = new AbilityRatingResultVO();

        try {
            // 计算各维度评分
            Double efficiencyRating = calculateEfficiencyRating(playerRank.getEfficiencyRank());
            Double scoringRating = calculateScoringRating(
                playerRank.getScoringRank(),
                playerRank.getTrueShootingRank(),
                playerRank.getTwoPointPercentageRank(),
                playerRank.getThreePointPercentageRank(),
                playerRank.getFreeThrowPercentageRank()
            );
            Double reboundingRating = calculateReboundingRating(
                playerRank.getOffensiveReboundRank(),
                playerRank.getDefensiveReboundRank()
            );
            Double assistRating = calculateAssistRating(
                playerRank.getAssistRank(),
                playerRank.getAssistToTurnoverRatioRank()
            );
            Double defenseRating = calculateDefenseRating(
                playerRank.getStealRank(),
                playerRank.getBlockRank(),
                position
            );
            Double turnoverRating = calculateTurnoverRating(playerRank.getTurnoverRank());
            Double foulRating = calculateFoulRating(playerRank.getFoulRank());

            // 设置各维度评分
            result.setEfficiencyRating(efficiencyRating);
            result.setScoringRating(scoringRating);
            result.setReboundingRating(reboundingRating);
            result.setAssistRating(assistRating);
            result.setDefenseRating(defenseRating);
            result.setTurnoverRating(turnoverRating);
            result.setFoulRating(foulRating);

            // 汇总各维度评分
            Map<RatingDimension, Double> ratings = new HashMap<>();
            ratings.put(RatingDimension.EFFICIENCY, efficiencyRating);
            ratings.put(RatingDimension.SCORING, scoringRating);
            ratings.put(RatingDimension.REBOUNDING, reboundingRating);
            ratings.put(RatingDimension.ASSISTING, assistRating);
            ratings.put(RatingDimension.DEFENSE, defenseRating);
            ratings.put(RatingDimension.TURNOVER, turnoverRating);
            ratings.put(RatingDimension.FOUL, foulRating);

            // 计算真实能力值
            Double realAbilityRating = calculateRealAbilityRating(ratings, position);
            result.setRealAbilityRating(realAbilityRating);

            // 计算新的展示能力值
            Double newDisplayedRating = calculateNewDisplayedRating(
                realAbilityRating, currentDisplayedRating, isWin, seasonGames, streak
            );
            result.setDisplayedAbilityRating(newDisplayedRating);

            // 计算位置适应度
            Map<Position, Double> positionFit = calculatePositionFit(ratings);
            result.setPositionFit(positionFit);
            result.setBestPosition(getBestPosition(positionFit));

        } catch (Exception e) {
            log.error("计算能力评分时发生错误: {}", e.getMessage(), e);
            throw new RuntimeException("能力评分计算失败", e);
        }

        return result;
    }

    /**
     * 根据排名计算评分
     * 
     * @param rank 排名
     * @return 评分(0-100)
     */
    private Double calculateRankScore(Integer rank) {
        if (rank == null || rank <= 0) {
            return 0.0;
        }
        return (double) (dataPoolSize - rank) / dataPoolSize * 100;
    }

    /**
     * 计算展示能力值的变化量
     * 
     * @param realRating 真实能力值
     * @param currentDisplayedRating 当前展示能力值
     * @param isWin 是否获胜
     * @param seasonGames 赛季参赛次数
     * @param streak 连胜/连败场次
     * @return 展示能力值变化量
     */
    private Double calculateDisplayedRatingChange(Double realRating, Double currentDisplayedRating,
                                                Boolean isWin, Integer seasonGames, Integer streak) {
        // 设置保底变化值
        double minChange = 0.1;

        if (isWin) {
            // 胜利后能力值变化 = (真实能力值-当前展示能力值) * ((赛季参赛次数+连胜场次)/13) + 0.1
            // 确保(真实能力值-当前展示能力值)>=0
            double ratingDiff = Math.max(0, realRating - currentDisplayedRating);
            // 确保系数<=1，避免短时间内能力值触达上限
            double coefficient = Math.min(1.0, (seasonGames + Math.abs(streak)) / 13.0);
            return ratingDiff * coefficient + minChange;
        } else {
            // 失利后能力值变化 = (当前展示能力值-真实能力值) * ((赛季参赛次数-连败场次)/13) + 0.1
            // 确保(当前展示能力值-真实能力值)>=0
            double ratingDiff = Math.max(0, currentDisplayedRating - realRating);
            // 确保系数<=1且>0，避免短时间内能力值触达下限
            double coefficient = Math.max(0, Math.min(1.0, (seasonGames - Math.abs(streak)) / 13.0));
            return ratingDiff * coefficient + minChange;
        }
    }
}