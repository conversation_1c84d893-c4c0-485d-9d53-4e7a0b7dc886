# 能力值评分模块详细架构设计

## 一、模块概述

### 1.1 设计目标
基于现有球员生涯功能，完善能力值评分的功能模块，实现科学、准确、实时的球员能力评估体系。

### 1.2 核心功能
- **7维度能力评分**：效率、得分、篮板、助攻、防守、失误控制、犯规控制
- **动态能力值更新**：基于比赛结果自动调整展示能力值
- **位置适应度分析**：分析球员最适合的场上位置
- **能力值趋势追踪**：基于真实数据的历史趋势分析
- **联盟对比分析**：与联盟平均水平和最佳水平对比

### 1.3 技术原则
- **数据驱动**：基于真实比赛数据，避免模拟生成
- **算法科学**：采用标准化评分算法，确保公平性
- **实时响应**：事件驱动的数据更新机制
- **高性能**：多级缓存确保查询效率
- **可扩展**：模块化设计便于算法优化和功能扩展

## 二、整体架构设计

### 2.1 架构总览

```mermaid
graph TB
    subgraph "能力值评分模块"
        subgraph "Controller层"
            ARC[AbilityRatingController]
            AAC[AbilityAnalysisController]
        end
        
        subgraph "Service层"
            ARS[AbilityRatingService]
            ACS[AbilityCalculationService] 
            ATS[AbilityTrendService]
            AUS[AbilityUpdateService]
            PAS[PositionAnalysisService]
        end
        
        subgraph "计算引擎层"
            ARC_ENGINE[AbilityRatingCalculator]
            LAS[LeagueAnalysisService]
            DSS[DataStandardizationService]
            ATS_ENGINE[AbilityTrendCalculator]
        end
        
        subgraph "数据访问层"
            ARM[AbilityRatingMapper]
            ATM[AbilityTrendMapper]
            LSM[LeagueStatsMapper]
        end
        
        subgraph "事件处理层"
            AREL[AbilityRatingEventListener]
            AEP[AbilityEventPublisher]
        end
        
        subgraph "定时任务层"
            ARS_JOB[AbilityRatingScheduler]
            LAS_JOB[LeagueStatsScheduler]
            ATS_JOB[AbilityTrendScheduler]
        end
        
        subgraph "缓存层"
            ACS_CACHE[AbilityCacheService]
            LCS[LeagueCacheService]
        end
    end
    
    subgraph "外部系统"
        GAME_SYS[比赛系统]
        PLAYER_SYS[球员系统]
        STATS_SYS[统计系统]
    end
    
    subgraph "数据存储"
        MYSQL[(MySQL数据库)]
        REDIS[(Redis缓存)]
    end
    
    ARC --> ARS
    AAC --> ATS
    ARS --> ACS
    ARS --> AUS
    ATS --> ATS_ENGINE
    ACS --> ARC_ENGINE
    AUS --> AREL
    PAS --> LAS
    
    ARC_ENGINE --> DSS
    ARC_ENGINE --> LAS
    LAS --> LSM
    ATS_ENGINE --> ATM
    
    AREL --> AEP
    ARS_JOB --> AUS
    LAS_JOB --> LAS
    ATS_JOB --> ATS_ENGINE
    
    ACS --> ACS_CACHE
    LAS --> LCS
    ACS_CACHE --> REDIS
    LCS --> REDIS
    
    ARM --> MYSQL
    ATM --> MYSQL
    LSM --> MYSQL
    
    GAME_SYS --> AREL
    PLAYER_SYS --> ARS
    STATS_SYS --> ACS
```

### 2.2 分层职责

| 层级 | 职责描述 |
|------|----------|
| **Controller层** | 提供RESTful API接口，处理HTTP请求响应 |
| **Service层** | 核心业务逻辑，协调各组件完成业务功能 |
| **计算引擎层** | 专门的算法计算逻辑，封装复杂的数学运算 |
| **数据访问层** | 数据库操作，SQL查询和数据持久化 |
| **事件处理层** | 事件驱动机制，异步处理数据更新 |
| **定时任务层** | 定时数据计算和同步任务 |
| **缓存层** | 多级缓存管理，提升查询性能 |

## 三、核心类设计

### 3.1 Controller层设计

#### 3.1.1 AbilityRatingController（能力评分控制器）

```mermaid
classDiagram
    class AbilityRatingController {
        -AbilityRatingService abilityRatingService
        -AbilityTrendService abilityTrendService
        -PositionAnalysisService positionAnalysisService
        +CommonResult~PlayerAbilityRatingVO~ getPlayerAbilityRating(Long playerId, Integer gameType)
        +CommonResult~List~AbilityTrendVO~~ getPlayerAbilityTrend(Long playerId, Integer gameType, String timeRange)
        +CommonResult~PositionAnalysisVO~ getPlayerPositionAnalysis(Long playerId, Integer gameType)
        +CommonResult~Boolean~ updatePlayerAbilityRating(Long playerId, Integer gameType)
        +CommonResult~Boolean~ batchUpdateAbilityRatings(BatchUpdateReqVO reqVO)
        +CommonResult~AbilityComparisonVO~ compareWithLeague(Long playerId, Integer gameType)
        +CommonResult~List~PlayerAbilityRankVO~~ getAbilityRankings(AbilityRankingReqVO reqVO)
    }
    
    class AbilityAnalysisController {
        -AbilityAnalysisService analysisService
        -LeagueAnalysisService leagueAnalysisService
        +CommonResult~LeagueAbilityStatsVO~ getLeagueAbilityStats(Integer gameType)
        +CommonResult~List~AbilityDistributionVO~~ getAbilityDistribution(String dimension, Integer gameType)
        +CommonResult~AbilityCorrelationVO~ getAbilityCorrelation(Integer gameType)
        +CommonResult~PlayerAbilityReportVO~ generateAbilityReport(Long playerId, Integer gameType)
        +CommonResult~Boolean~ refreshLeagueStats(Integer gameType)
    }
```

**设计要点**：
- **双控制器设计**：分离基础评分功能和高级分析功能
- **完整的参数校验**：确保输入数据的有效性
- **统一的响应格式**：使用CommonResult包装返回结果
- **权限控制**：管理端接口需要相应权限校验

### 3.2 Service层设计

#### 3.2.1 核心Service类关系

```mermaid
classDiagram
    class AbilityRatingService {
        <<interface>>
        +PlayerAbilityRatingVO getPlayerAbilityRating(Long playerId, Integer gameType)
        +void updatePlayerAbilityRating(Long playerId, Integer gameType)
        +List~PlayerAbilityRankVO~ getAbilityRankings(AbilityRankingReqVO reqVO)
        +AbilityComparisonVO compareWithLeague(Long playerId, Integer gameType)
        +void batchUpdateAbilityRatings(List~Long~ playerIds, Integer gameType)
    }
    
    class AbilityRatingServiceImpl {
        -AbilityCalculationService calculationService
        -AbilityCacheService cacheService
        -AbilityRatingMapper abilityRatingMapper
        -LeagueAnalysisService leagueAnalysisService
        +PlayerAbilityRatingVO getPlayerAbilityRating(Long playerId, Integer gameType)
        +void updatePlayerAbilityRating(Long playerId, Integer gameType)
        -PlayerAbilityRatingDO calculateAbilityRating(Long playerId, Integer gameType)
        -void saveAbilityRating(PlayerAbilityRatingDO rating)
        -void evictRelatedCache(Long playerId, Integer gameType)
    }
    
    class AbilityCalculationService {
        -AbilityRatingCalculator calculator
        -DataStandardizationService standardizationService
        -PlayerCareerStatsService careerStatsService
        +AbilityRatingResultVO calculatePlayerAbility(Long playerId, Integer gameType)
        +DisplayAbilityVO calculateDisplayAbility(Long playerId, GameResultVO gameResult)
        +LeagueAbilityStatsVO calculateLeagueStats(Integer gameType)
        +List~AbilityTrendPointVO~ calculateAbilityTrend(Long playerId, Integer gameType, LocalDate startDate, LocalDate endDate)
    }
    
    class AbilityTrendService {
        -AbilityTrendCalculator trendCalculator
        -AbilityTrendMapper trendMapper
        -AbilityCacheService cacheService
        +List~AbilityTrendVO~ getPlayerAbilityTrend(Long playerId, Integer gameType, String timeRange)
        +void updateAbilityTrendData(Long playerId, AbilityRatingVO newRating)
        +AbilityProgressVO getAbilityProgress(Long playerId, Integer gameType)
        -List~AbilityTrendPointVO~ calculateTrendFromHistory(Long playerId, Integer gameType, LocalDate startDate, LocalDate endDate)
    }
    
    AbilityRatingService <|-- AbilityRatingServiceImpl
    AbilityRatingServiceImpl --> AbilityCalculationService
    AbilityRatingServiceImpl --> AbilityCacheService
    AbilityCalculationService --> AbilityRatingCalculator
    AbilityTrendService --> AbilityTrendCalculator
```

### 3.3 计算引擎层设计

#### 3.3.1 AbilityRatingCalculator（能力评分计算器）

```mermaid
classDiagram
    class AbilityRatingCalculator {
        -Map~Position, Map~RatingDimension, Double~~ positionWeights
        -Map~Position, DefenseWeights~ defenseWeights
        -int dataPoolSize
        +AbilityRatingResultVO calculateAbilityRating(PlayerStatsVO playerStats, LeagueStatsVO leagueStats, Position position)
        +Double calculateDimensionRating(RatingDimension dimension, PlayerStatsVO playerStats, LeagueStatsVO leagueStats)
        +Double calculateRealAbilityValue(Map~RatingDimension, Double~ ratings, Position position)
        +Double calculateDisplayAbilityChange(Double realRating, Double currentDisplay, GameResultVO gameResult)
        +Map~Position, Double~ calculatePositionFit(Map~RatingDimension, Double~ ratings)
        -Double calculateEfficiencyRating(PlayerStatsVO playerStats, LeagueStatsVO leagueStats)
        -Double calculateScoringRating(PlayerStatsVO playerStats, LeagueStatsVO leagueStats)
        -Double calculateReboundingRating(PlayerStatsVO playerStats, LeagueStatsVO leagueStats)
        -Double calculateAssistingRating(PlayerStatsVO playerStats, LeagueStatsVO leagueStats)
        -Double calculateDefenseRating(PlayerStatsVO playerStats, LeagueStatsVO leagueStats, Position position)
        -Double calculateTurnoverRating(PlayerStatsVO playerStats, LeagueStatsVO leagueStats)
        -Double calculateFoulRating(PlayerStatsVO playerStats, LeagueStatsVO leagueStats)
    }
    
    class AbilityTrendCalculator {
        -AbilityRatingMapper ratingMapper
        -PlayerCareerStatsMapper careerStatsMapper
        +List~AbilityTrendPointVO~ calculateTrendData(Long playerId, Integer gameType, LocalDate startDate, LocalDate endDate)
        +AbilityProgressVO calculateProgress(Long playerId, Integer gameType)
        +List~AbilityMilestoneVO~ identifyMilestones(List~AbilityTrendPointVO~ trendData)
        -List~AbilityTrendPointVO~ interpolateMissingData(List~AbilityTrendPointVO~ rawData)
        -Double calculateMovingAverage(List~AbilityTrendPointVO~ data, int window)
    }
    
    class LeagueAnalysisService {
        -LeagueStatsMapper leagueStatsMapper
        -AbilityCacheService cacheService
        +LeagueAbilityStatsVO calculateLeagueStats(Integer gameType)
        +List~AbilityDistributionVO~ getAbilityDistribution(String dimension, Integer gameType)
        +AbilityCorrelationVO calculateAbilityCorrelation(Integer gameType)
        +Map~String, Double~ getLeagueAverages(Integer gameType)
        +Map~String, Double~ getLeagueStandardDeviations(Integer gameType)
        -void updateLeagueStatsCache(Integer gameType, LeagueAbilityStatsVO stats)
    }
    
    class DataStandardizationService {
        +PlayerStatsVO standardizeByTime(PlayerStatsVO rawStats, Integer actualMinutes, Integer targetMinutes)
        +PlayerStatsVO standardizeByGameType(PlayerStatsVO rawStats, Integer gameType)
        +List~PlayerStatsVO~ batchStandardize(List~PlayerStatsVO~ rawStatsList, Integer gameType)
        -Double applyTimeRatio(Double value, Double ratio, Boolean isCumulative)
        -PlayerStatsVO validateStandardizedData(PlayerStatsVO standardized)
    }
```

### 3.4 事件处理机制设计

#### 3.4.1 事件驱动流程

```mermaid
sequenceDiagram
    participant Game as 比赛系统
    participant Publisher as AbilityEventPublisher
    participant Listener as AbilityRatingEventListener
    participant Service as AbilityUpdateService
    participant Calculator as AbilityRatingCalculator
    participant Cache as AbilityCacheService
    participant DB as 数据库
    
    Game->>Publisher: 发布比赛结果事件
    Publisher->>Publisher: 构建AbilityUpdateEvent
    Publisher->>Listener: 异步事件通知
    
    Listener->>Service: 处理能力值更新
    Service->>Calculator: 计算新的能力值
    Calculator->>Calculator: 基于比赛结果调整展示能力值
    Calculator-->>Service: 返回更新后的能力值
    
    Service->>DB: 保存能力值数据
    Service->>Cache: 清除相关缓存
    
    alt 如果是重要比赛
        Service->>Service: 触发能力值趋势更新
        Service->>DB: 保存趋势数据点
    end
    
    Service-->>Listener: 更新完成
    Listener->>Publisher: 发布能力值更新完成事件
```

#### 3.4.2 事件类设计

```mermaid
classDiagram
    class AbilityUpdateEvent {
        -Long playerId
        -Integer gameType
        -GameResultVO gameResult
        -LocalDateTime eventTime
        -String eventSource
        +AbilityUpdateEvent(Long playerId, Integer gameType, GameResultVO gameResult)
        +getters()
    }
    
    class BatchAbilityUpdateEvent {
        -List~Long~ playerIds
        -Integer gameType
        -Long gameId
        -LocalDateTime eventTime
        +BatchAbilityUpdateEvent(List~Long~ playerIds, Integer gameType, Long gameId)
        +getters()
    }
    
    class AbilityRatingEventListener {
        -AbilityUpdateService updateService
        -AbilityTrendService trendService
        @EventListener
        @Async("abilityTaskExecutor")
        +void handleAbilityUpdate(AbilityUpdateEvent event)
        @EventListener  
        @Async("abilityTaskExecutor")
        +void handleBatchAbilityUpdate(BatchAbilityUpdateEvent event)
        -void processAbilityUpdate(Long playerId, Integer gameType, GameResultVO gameResult)
        -void updateTrendDataIfNeeded(Long playerId, Integer gameType)
    }
    
    class AbilityEventPublisher {
        -ApplicationEventPublisher eventPublisher
        +void publishAbilityUpdateEvent(Long playerId, Integer gameType, GameResultVO gameResult)
        +void publishBatchAbilityUpdateEvent(List~Long~ playerIds, Integer gameType, Long gameId)
        +void publishLeagueStatsUpdateEvent(Integer gameType)
    }
```

### 3.5 定时任务设计

#### 3.5.1 任务调度架构

```mermaid
graph TD
    subgraph "定时任务调度器"
        ARS[AbilityRatingScheduler]
        LAS[LeagueStatsScheduler] 
        ATS[AbilityTrendScheduler]
        CCS[CacheCleanupScheduler]
    end
    
    subgraph "任务执行器"
        ARTP[AbilityRatingTaskProcessor]
        LSTP[LeagueStatsTaskProcessor]
        ATTP[AbilityTrendTaskProcessor]
    end
    
    subgraph "任务监控"
        TM[TaskMonitor]
        NM[NotificationManager]
    end
    
    ARS -->|每日02:00| ARTP
    LAS -->|每小时整点| LSTP
    ATS -->|每日03:00| ATTP
    CCS -->|每日04:00| Cache
    
    ARTP --> TM
    LSTP --> TM
    ATTP --> TM
    
    TM --> NM
    
    Cache[(Redis缓存)]
```

#### 3.5.2 定时任务类设计

```mermaid
classDiagram
    class AbilityRatingScheduler {
        -AbilityUpdateService updateService
        -TaskMonitor taskMonitor
        @Scheduled(cron = "0 0 2 * * ?")
        +void refreshAllPlayersAbilityRating()
        @Scheduled(cron = "0 30 2 * * ?")
        +void cleanupExpiredAbilityData()
        @Scheduled(cron = "0 0 6 * * SUN")
        +void weeklyAbilityRatingRecalculation()
        -void processPlayerBatch(List~Long~ playerIds)
        -void logTaskProgress(String taskName, int processed, int total)
    }
    
    class LeagueStatsScheduler {
        -LeagueAnalysisService leagueAnalysisService
        -TaskMonitor taskMonitor
        @Scheduled(cron = "0 0 * * * ?")
        +void refreshLeagueStats()
        @Scheduled(cron = "0 15 0 * * ?")
        +void updateLeagueDistribution()
        @Scheduled(cron = "0 30 0 1 * ?")
        +void monthlyLeagueAnalysis()
        -void validateLeagueStatsIntegrity()
    }
    
    class AbilityTrendScheduler {
        -AbilityTrendService trendService
        -TaskMonitor taskMonitor
        @Scheduled(cron = "0 0 3 * * ?")
        +void calculateDailyTrendData()
        @Scheduled(cron = "0 30 3 * * SUN")
        +void weeklyTrendAnalysis()
        @Scheduled(cron = "0 0 4 1 * ?")
        +void monthlyTrendSummary()
        -void identifyTrendAnomalies()
        -void generateTrendReports()
    }
    
    class TaskMonitor {
        -TaskExecutionRepository taskRepo
        -NotificationManager notificationManager
        +void recordTaskStart(String taskName, Map~String, Object~ params)
        +void recordTaskProgress(String taskName, int processed, int total)
        +void recordTaskCompletion(String taskName, TaskResult result)
        +void recordTaskFailure(String taskName, Exception error)
        +List~TaskExecutionLogVO~ getTaskExecutionHistory(String taskName, LocalDate startDate, LocalDate endDate)
        -void checkTaskTimeout(String taskName)
        -void sendAlertIfNeeded(String taskName, TaskStatus status)
    }
```

### 3.6 缓存策略设计

#### 3.6.1 缓存架构

```mermaid
graph TD
    subgraph "应用层"
        API[API请求]
    end
    
    subgraph "L1缓存-本地缓存"
        LC[LocalCache<br/>CaffeineCache]
    end
    
    subgraph "L2缓存-Redis分布式缓存"
        subgraph "能力值缓存"
            AC[AbilityCache<br/>30分钟]
            ATC[AbilityTrendCache<br/>1小时]
            PAC[PositionAnalysisCache<br/>2小时]
        end
        
        subgraph "联盟数据缓存"
            LSC[LeagueStatsCache<br/>1小时]
            ADC[AbilityDistributionCache<br/>6小时]
            ACC[AbilityCorrelationCache<br/>12小时]
        end
        
        subgraph "排行榜缓存"
            ARC[AbilityRankingCache<br/>10分钟]
            LBC[LeaderboardCache<br/>5分钟]
        end
    end
    
    subgraph "L3缓存-数据库聚合表"
        AGG[(聚合表<br/>sd_player_ability_rating)]
    end
    
    API --> LC
    LC -->|Miss| AC
    AC -->|Miss| AGG
    
    API --> ATC
    API --> LSC
    API --> ARC
    
    LC -.->|定期失效| LC
    AC -.->|事件驱动失效| AC
    ATC -.->|数据更新失效| ATC
```

#### 3.6.2 缓存管理类设计

```mermaid
classDiagram
    class AbilityCacheService {
        -RedisTemplate redisTemplate
        -CacheManager localCacheManager
        +PlayerAbilityRatingVO getPlayerAbilityFromCache(Long playerId, Integer gameType)
        +void cachePlayerAbility(Long playerId, Integer gameType, PlayerAbilityRatingVO ability)
        +List~AbilityTrendVO~ getAbilityTrendFromCache(Long playerId, Integer gameType, String timeRange)
        +void cacheAbilityTrend(Long playerId, Integer gameType, String timeRange, List~AbilityTrendVO~ trend)
        +void evictPlayerAbilityCache(Long playerId, Integer gameType)
        +void evictPlayerAllCache(Long playerId)
        +void evictGameTypeCache(Integer gameType)
        +CacheStatsVO getCacheStatistics()
        -String buildCacheKey(String prefix, Object... params)
        -Duration getCacheExpiration(String cacheType)
    }
    
    class LeagueCacheService {
        -RedisTemplate redisTemplate
        +LeagueAbilityStatsVO getLeagueStatsFromCache(Integer gameType)
        +void cacheLeagueStats(Integer gameType, LeagueAbilityStatsVO stats)
        +List~AbilityDistributionVO~ getDistributionFromCache(String dimension, Integer gameType)
        +void cacheDistribution(String dimension, Integer gameType, List~AbilityDistributionVO~ distribution)
        +AbilityCorrelationVO getCorrelationFromCache(Integer gameType)
        +void cacheCorrelation(Integer gameType, AbilityCorrelationVO correlation)
        +void evictLeagueCache(Integer gameType)
        +void refreshAllLeagueCache()
        -void validateCacheData(Object data)
    }
    
    class CacheWarmupService {
        -AbilityCacheService abilityCacheService
        -LeagueCacheService leagueCacheService
        -PlayerService playerService
        @PostConstruct
        +void warmupCriticalCache()
        @Scheduled(cron = "0 0 1 * * ?")
        +void dailyCacheWarmup()
        +void warmupPlayerAbilityCache(List~Long~ playerIds)
        +void warmupLeagueStatsCache()
        -List~Long~ getActivePlayerIds()
        -void logWarmupProgress(String cacheType, int processed, int total)
    }
```

## 四、数据库设计

### 4.1 能力值评分相关表

#### 4.1.1 球员能力评分表

```sql
CREATE TABLE `sd_player_ability_rating` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `player_id` BIGINT NOT NULL COMMENT '球员ID',
    `game_type` TINYINT NOT NULL DEFAULT 0 COMMENT '比赛类型:0-全部,1-排位赛,2-友谊赛,3-联赛',
    
    -- 7维度能力评分 (0-100分)
    `efficiency_rating` DECIMAL(5,2) DEFAULT 60.00 COMMENT '效率能力评分',
    `scoring_rating` DECIMAL(5,2) DEFAULT 60.00 COMMENT '得分能力评分', 
    `rebounding_rating` DECIMAL(5,2) DEFAULT 60.00 COMMENT '篮板能力评分',
    `assist_rating` DECIMAL(5,2) DEFAULT 60.00 COMMENT '助攻能力评分',
    `defense_rating` DECIMAL(5,2) DEFAULT 60.00 COMMENT '防守能力评分',
    `turnover_rating` DECIMAL(5,2) DEFAULT 60.00 COMMENT '失误控制评分',
    `foul_rating` DECIMAL(5,2) DEFAULT 60.00 COMMENT '犯规控制评分',
    
    -- 综合评分
    `real_ability_rating` DECIMAL(5,2) DEFAULT 60.00 COMMENT '真实能力评分',
    `display_ability_rating` DECIMAL(5,2) DEFAULT 60.00 COMMENT '展示能力评分',
    `overall_rating` DECIMAL(5,2) DEFAULT 60.00 COMMENT '综合能力评分',
    
    -- 位置适应度 (0-100分)
    `pg_fit_rating` DECIMAL(5,2) DEFAULT 20.00 COMMENT '控球后卫适应度',
    `sg_fit_rating` DECIMAL(5,2) DEFAULT 20.00 COMMENT '得分后卫适应度',
    `sf_fit_rating` DECIMAL(5,2) DEFAULT 20.00 COMMENT '小前锋适应度', 
    `pf_fit_rating` DECIMAL(5,2) DEFAULT 20.00 COMMENT '大前锋适应度',
    `c_fit_rating` DECIMAL(5,2) DEFAULT 20.00 COMMENT '中锋适应度',
    `best_position` TINYINT DEFAULT 3 COMMENT '最适合位置:1-PG,2-SG,3-SF,4-PF,5-C',
    
    -- 排名信息
    `efficiency_rank` INT DEFAULT 0 COMMENT '效率排名',
    `scoring_rank` INT DEFAULT 0 COMMENT '得分排名',
    `rebounding_rank` INT DEFAULT 0 COMMENT '篮板排名',
    `assist_rank` INT DEFAULT 0 COMMENT '助攻排名',
    `defense_rank` INT DEFAULT 0 COMMENT '防守排名',
    `overall_rank` INT DEFAULT 0 COMMENT '综合排名',
    
    -- 与联盟对比
    `league_percentile` DECIMAL(5,2) DEFAULT 50.00 COMMENT '联盟百分位',
    `above_league_avg` TINYINT DEFAULT 0 COMMENT '是否高于联盟平均:0-否,1-是',
    
    -- 更新信息
    `last_game_id` BIGINT COMMENT '最后一场比赛ID',
    `last_update_time` DATETIME COMMENT '最后更新时间',
    `calculation_version` VARCHAR(20) DEFAULT 'v1.0' COMMENT '计算版本',
    
    -- 系统字段
    `creator` VARCHAR(64) DEFAULT '' COMMENT '创建者',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` VARCHAR(64) DEFAULT '' COMMENT '更新者', 
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_player_game_type` (`player_id`, `game_type`, `deleted`),
    KEY `idx_overall_rating` (`overall_rating` DESC),
    KEY `idx_efficiency_rating` (`efficiency_rating` DESC),
    KEY `idx_scoring_rating` (`scoring_rating` DESC),
    KEY `idx_overall_rank` (`overall_rank`),
    KEY `idx_last_update` (`last_update_time`)
) COMMENT='球员能力评分表';
```

#### 4.1.2 能力值趋势数据表

```sql
CREATE TABLE `sd_player_ability_trend` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `player_id` BIGINT NOT NULL COMMENT '球员ID',
    `game_type` TINYINT NOT NULL DEFAULT 0 COMMENT '比赛类型',
    
    -- 趋势数据点
    `record_date` DATE NOT NULL COMMENT '记录日期',
    `games_played_at_date` INT DEFAULT 0 COMMENT '截至该日期的总场次',
    
    -- 7维度历史评分
    `efficiency_rating` DECIMAL(5,2) DEFAULT 60.00 COMMENT '效率评分',
    `scoring_rating` DECIMAL(5,2) DEFAULT 60.00 COMMENT '得分评分',
    `rebounding_rating` DECIMAL(5,2) DEFAULT 60.00 COMMENT '篮板评分',
    `assist_rating` DECIMAL(5,2) DEFAULT 60.00 COMMENT '助攻评分',
    `defense_rating` DECIMAL(5,2) DEFAULT 60.00 COMMENT '防守评分',
    `turnover_rating` DECIMAL(5,2) DEFAULT 60.00 COMMENT '失误控制评分',
    `foul_rating` DECIMAL(5,2) DEFAULT 60.00 COMMENT '犯规控制评分',
    `overall_rating` DECIMAL(5,2) DEFAULT 60.00 COMMENT '综合评分',
    
    -- 趋势分析
    `rating_change` DECIMAL(5,2) DEFAULT 0.00 COMMENT '与前一记录的评分变化',
    `trend_direction` TINYINT DEFAULT 0 COMMENT '趋势方向:-1-下降,0-平稳,1-上升',
    `is_milestone` TINYINT DEFAULT 0 COMMENT '是否里程碑:0-否,1-是',
    `milestone_desc` VARCHAR(200) DEFAULT '' COMMENT '里程碑描述',
    
    -- 触发信息
    `trigger_game_id` BIGINT COMMENT '触发比赛ID',
    `trigger_event` VARCHAR(50) DEFAULT '' COMMENT '触发事件',
    
    -- 系统字段
    `creator` VARCHAR(64) DEFAULT '' COMMENT '创建者',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` VARCHAR(64) DEFAULT '' COMMENT '更新者',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_player_date_type` (`player_id`, `record_date`, `game_type`, `deleted`),
    KEY `idx_player_trend` (`player_id`, `record_date`),
    KEY `idx_milestone` (`is_milestone`, `record_date`),
    KEY `idx_trigger_game` (`trigger_game_id`)
) COMMENT='球员能力值趋势表';
```

#### 4.1.3 联盟能力统计表

```sql
CREATE TABLE `sd_league_ability_stats` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `game_type` TINYINT NOT NULL DEFAULT 0 COMMENT '比赛类型',
    `stat_date` DATE NOT NULL COMMENT '统计日期',
    
    -- 7维度统计数据
    `efficiency_avg` DECIMAL(5,2) DEFAULT 0.00 COMMENT '效率平均分',
    `efficiency_std` DECIMAL(5,2) DEFAULT 0.00 COMMENT '效率标准差',
    `efficiency_max` DECIMAL(5,2) DEFAULT 0.00 COMMENT '效率最高分',
    `efficiency_min` DECIMAL(5,2) DEFAULT 0.00 COMMENT '效率最低分',
    
    `scoring_avg` DECIMAL(5,2) DEFAULT 0.00 COMMENT '得分平均分',
    `scoring_std` DECIMAL(5,2) DEFAULT 0.00 COMMENT '得分标准差',
    `scoring_max` DECIMAL(5,2) DEFAULT 0.00 COMMENT '得分最高分',
    `scoring_min` DECIMAL(5,2) DEFAULT 0.00 COMMENT '得分最低分',
    
    `rebounding_avg` DECIMAL(5,2) DEFAULT 0.00 COMMENT '篮板平均分',
    `rebounding_std` DECIMAL(5,2) DEFAULT 0.00 COMMENT '篮板标准差',
    `rebounding_max` DECIMAL(5,2) DEFAULT 0.00 COMMENT '篮板最高分',
    `rebounding_min` DECIMAL(5,2) DEFAULT 0.00 COMMENT '篮板最低分',
    
    `assist_avg` DECIMAL(5,2) DEFAULT 0.00 COMMENT '助攻平均分',
    `assist_std` DECIMAL(5,2) DEFAULT 0.00 COMMENT '助攻标准差',
    `assist_max` DECIMAL(5,2) DEFAULT 0.00 COMMENT '助攻最高分',
    `assist_min` DECIMAL(5,2) DEFAULT 0.00 COMMENT '助攻最低分',
    
    `defense_avg` DECIMAL(5,2) DEFAULT 0.00 COMMENT '防守平均分',
    `defense_std` DECIMAL(5,2) DEFAULT 0.00 COMMENT '防守标准差',
    `defense_max` DECIMAL(5,2) DEFAULT 0.00 COMMENT '防守最高分',
    `defense_min` DECIMAL(5,2) DEFAULT 0.00 COMMENT '防守最低分',
    
    `turnover_avg` DECIMAL(5,2) DEFAULT 0.00 COMMENT '失误控制平均分',
    `turnover_std` DECIMAL(5,2) DEFAULT 0.00 COMMENT '失误控制标准差',
    `turnover_max` DECIMAL(5,2) DEFAULT 0.00 COMMENT '失误控制最高分',
    `turnover_min` DECIMAL(5,2) DEFAULT 0.00 COMMENT '失误控制最低分',
    
    `foul_avg` DECIMAL(5,2) DEFAULT 0.00 COMMENT '犯规控制平均分',
    `foul_std` DECIMAL(5,2) DEFAULT 0.00 COMMENT '犯规控制标准差',
    `foul_max` DECIMAL(5,2) DEFAULT 0.00 COMMENT '犯规控制最高分',
    `foul_min` DECIMAL(5,2) DEFAULT 0.00 COMMENT '犯规控制最低分',
    
    `overall_avg` DECIMAL(5,2) DEFAULT 0.00 COMMENT '综合平均分',
    `overall_std` DECIMAL(5,2) DEFAULT 0.00 COMMENT '综合标准差',
    `overall_max` DECIMAL(5,2) DEFAULT 0.00 COMMENT '综合最高分',
    `overall_min` DECIMAL(5,2) DEFAULT 0.00 COMMENT '综合最低分',
    
    -- 统计元信息
    `total_players` INT DEFAULT 0 COMMENT '统计球员总数',
    `qualified_players` INT DEFAULT 0 COMMENT '合格球员数(>=5场比赛)',
    `calculation_version` VARCHAR(20) DEFAULT 'v1.0' COMMENT '计算版本',
    
    -- 系统字段
    `creator` VARCHAR(64) DEFAULT '' COMMENT '创建者',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` VARCHAR(64) DEFAULT '' COMMENT '更新者',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_game_type_date` (`game_type`, `stat_date`, `deleted`),
    KEY `idx_stat_date` (`stat_date` DESC)
) COMMENT='联盟能力统计表';
```

## 五、核心业务流程设计

### 5.1 能力值计算完整流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Controller as AbilityRatingController
    participant Service as AbilityRatingService
    participant Calculator as AbilityRatingCalculator
    participant League as LeagueAnalysisService
    participant Standardization as DataStandardizationService
    participant Cache as AbilityCacheService
    participant DB as 数据库
    
    Client->>Controller: 请求球员能力评分
    Controller->>Service: getPlayerAbilityRating(playerId, gameType)
    
    Service->>Cache: 检查缓存
    alt 缓存命中
        Cache-->>Service: 返回缓存数据
        Service-->>Controller: 返回能力评分
    else 缓存未命中
        Service->>League: 获取联盟统计数据
        League->>Cache: 检查联盟数据缓存
        alt 联盟缓存命中
            Cache-->>League: 返回联盟数据
        else 联盟缓存未命中
            League->>DB: 查询联盟统计
            DB-->>League: 返回原始数据
            League->>League: 计算联盟平均值和标准差
            League->>Cache: 缓存联盟数据
        end
        League-->>Service: 返回联盟统计
        
        Service->>DB: 查询球员统计数据
        DB-->>Service: 返回球员原始数据
        
        Service->>Standardization: 标准化球员数据
        Standardization-->>Service: 返回标准化数据
        
        Service->>Calculator: 计算7维度能力评分
        Calculator->>Calculator: 计算效率评分
        Calculator->>Calculator: 计算得分评分
        Calculator->>Calculator: 计算篮板评分
        Calculator->>Calculator: 计算助攻评分
        Calculator->>Calculator: 计算防守评分
        Calculator->>Calculator: 计算失误控制评分
        Calculator->>Calculator: 计算犯规控制评分
        Calculator->>Calculator: 加权计算综合能力值
        Calculator->>Calculator: 计算位置适应度
        Calculator-->>Service: 返回完整能力评分
        
        Service->>DB: 保存能力评分结果
        Service->>Cache: 缓存能力评分
        Service-->>Controller: 返回能力评分
    end
    
    Controller-->>Client: 返回API响应
```

### 5.2 能力值更新触发流程

```mermaid
graph TD
    subgraph "触发源"
        A[比赛结束]
        B[统计数据录入]
        C[手动刷新]
        D[定时任务]
    end
    
    subgraph "事件发布"
        E[AbilityEventPublisher]
    end
    
    subgraph "事件处理"
        F[AbilityRatingEventListener]
    end
    
    subgraph "更新逻辑"
        G[AbilityUpdateService]
        H[实时能力值计算]
        I[展示能力值调整]
        J[趋势数据更新]
        K[排名重新计算]
    end
    
    subgraph "数据持久化"
        L[(能力评分表)]
        M[(趋势数据表)]
        N[(排名缓存)]
    end
    
    A --> E
    B --> E
    C --> E
    D --> E
    
    E --> F
    F --> G
    
    G --> H
    G --> I
    G --> J
    G --> K
    
    H --> L
    I --> L
    J --> M
    K --> N
```

### 5.3 能力值趋势计算流程

```mermaid
sequenceDiagram
    participant Scheduler as AbilityTrendScheduler
    participant Service as AbilityTrendService
    participant Calculator as AbilityTrendCalculator
    participant Mapper as AbilityTrendMapper
    participant Rating as AbilityRatingMapper
    participant DB as 数据库
    
    Note over Scheduler: 每日03:00定时执行
    Scheduler->>Service: calculateDailyTrendData()
    
    Service->>Rating: 获取活跃球员列表
    Rating-->>Service: 返回球员ID列表
    
    loop 处理每个球员
        Service->>Calculator: calculateTrendData(playerId, gameType, dateRange)
        
        Calculator->>Rating: 查询历史能力评分数据
        Rating-->>Calculator: 返回历史评分
        
        Calculator->>Calculator: 计算趋势点数据
        Calculator->>Calculator: 识别重要里程碑
        Calculator->>Calculator: 分析趋势方向
        Calculator-->>Service: 返回趋势数据点
        
        Service->>Mapper: 保存趋势数据
        Mapper->>DB: INSERT INTO sd_player_ability_trend
        
        alt 发现重要里程碑
            Service->>Service: 标记里程碑事件
            Service->>Service: 生成里程碑描述
        end
    end
    
    Service-->>Scheduler: 完成趋势计算
    
    Note over Scheduler: 记录执行日志
    Scheduler->>Scheduler: 记录任务执行结果
```

## 六、性能优化设计

### 6.1 查询优化策略

#### 6.1.1 索引设计策略

```sql
-- 能力评分表优化索引
CREATE INDEX idx_ability_composite ON sd_player_ability_rating 
    (game_type, overall_rating DESC, player_id);

CREATE INDEX idx_ability_ranking ON sd_player_ability_rating 
    (game_type, overall_rank ASC, last_update_time DESC);

CREATE INDEX idx_ability_position ON sd_player_ability_rating 
    (best_position, overall_rating DESC);

-- 趋势表优化索引  
CREATE INDEX idx_trend_player_date ON sd_player_ability_trend 
    (player_id, game_type, record_date DESC);

CREATE INDEX idx_trend_milestone ON sd_player_ability_trend 
    (is_milestone, record_date DESC, game_type);

-- 联盟统计表索引
CREATE INDEX idx_league_stats_latest ON sd_league_ability_stats 
    (game_type, stat_date DESC);
```

#### 6.1.2 查询优化技巧

```java
/**
 * 高性能查询实现示例
 */
@Mapper
public interface AbilityRatingMapper extends BaseMapper<PlayerAbilityRatingDO> {
    
    /**
     * 批量查询球员能力评分（避免N+1问题）
     */
    @Select("""
        SELECT par.*, p.name as player_name, p.avatar as player_avatar
        FROM sd_player_ability_rating par
        LEFT JOIN sd_player p ON par.player_id = p.id
        WHERE par.player_id IN 
        <foreach collection="playerIds" item="playerId" open="(" separator="," close=")">
            #{playerId}
        </foreach>
        AND par.game_type = #{gameType} AND par.deleted = 0
        ORDER BY par.overall_rating DESC
    """)
    List<PlayerAbilityRatingWithInfoDTO> selectBatchWithPlayerInfo(
        @Param("playerIds") Collection<Long> playerIds,
        @Param("gameType") Integer gameType
    );
    
    /**
     * 分页查询排行榜（使用覆盖索引）
     */
    @Select("""
        SELECT player_id, overall_rating, efficiency_rating, scoring_rating, 
               rebounding_rating, assist_rating, defense_rating, overall_rank
        FROM sd_player_ability_rating
        WHERE game_type = #{gameType} AND deleted = 0 
        ORDER BY overall_rating DESC
        LIMIT #{offset}, #{limit}
    """)
    List<AbilityRankingDTO> selectRankingPage(
        @Param("gameType") Integer gameType,
        @Param("offset") Long offset,
        @Param("limit") Long limit
    );
}
```

### 6.2 缓存性能优化

#### 6.2.1 缓存预热策略

```java
/**
 * 智能缓存预热服务
 */
@Component
public class AbilityCacheWarmupService {
    
    /**
     * 系统启动时预热关键缓存
     */
    @PostConstruct
    public void warmupCriticalCache() {
        CompletableFuture.runAsync(() -> {
            // 预热联盟统计数据
            warmupLeagueStatsCache();
            
            // 预热活跃球员能力数据
            warmupActivePlayersCache();
            
            // 预热排行榜数据
            warmupRankingCache();
        });
    }
    
    /**
     * 预热活跃球员缓存
     */
    private void warmupActivePlayersCache() {
        List<Long> activePlayerIds = getActivePlayerIds();
        
        // 分批预热，避免系统压力
        Lists.partition(activePlayerIds, 50).forEach(batch -> {
            batch.parallelStream().forEach(playerId -> {
                try {
                    // 预热各比赛类型的能力数据
                    Arrays.asList(0, 1, 2, 3).forEach(gameType -> {
                        abilityRatingService.getPlayerAbilityRating(playerId, gameType);
                    });
                    Thread.sleep(10); // 控制预热速度
                } catch (Exception e) {
                    log.warn("预热球员{}能力缓存失败", playerId, e);
                }
            });
        });
    }
}
```

#### 6.2.2 缓存更新策略

```java
/**
 * 智能缓存更新策略
 */
@Component
public class SmartCacheUpdateStrategy {
    
    /**
     * 基于数据变化程度决定缓存更新策略
     */
    public void updateAbilityCache(Long playerId, Integer gameType, 
                                 PlayerAbilityRatingVO oldRating, 
                                 PlayerAbilityRatingVO newRating) {
        
        // 计算评分变化幅度
        double ratingChange = Math.abs(newRating.getOverallRating() - oldRating.getOverallRating());
        
        if (ratingChange >= 5.0) {
            // 大幅变化：立即更新所有相关缓存
            evictAllRelatedCache(playerId, gameType);
            preloadUpdatedData(playerId, gameType);
        } else if (ratingChange >= 1.0) {
            // 中等变化：延迟更新相关缓存
            scheduleDelayedCacheUpdate(playerId, gameType);
        } else {
            // 微小变化：仅更新当前球员缓存
            evictPlayerCache(playerId, gameType);
        }
        
        // 如果排名发生变化，更新排行榜缓存
        if (!Objects.equals(oldRating.getOverallRank(), newRating.getOverallRank())) {
            evictRankingCache(gameType);
        }
    }
}
```

## 七、开发计划

### 7.1 迭代规划

#### Sprint 1: 核心能力评分算法（1周）
**目标**：实现基础的7维度能力评分计算

**开发任务**：
- [ ] 创建AbilityRatingCalculator核心计算类
- [ ] 实现7维度评分算法
- [ ] 实现位置适应度计算
- [ ] 创建数据库表结构
- [ ] 单元测试覆盖率达到90%

**验收标准**：
- 算法计算结果与Python参考实现一致
- 支持所有5个位置的评分计算
- 性能测试：单次计算耗时<50ms

#### Sprint 2: 数据更新机制（1周）
**目标**：实现基于比赛结果的能力值自动更新

**开发任务**：
- [ ] 实现AbilityUpdateService
- [ ] 创建事件驱动更新机制
- [ ] 实现展示能力值动态调整
- [ ] 实现缓存更新策略
- [ ] 集成现有比赛结果事件

**验收标准**：
- 比赛结束后5秒内完成能力值更新
- 事件处理成功率>99.5%
- 缓存一致性得到保证

#### Sprint 3: 趋势分析功能（1周）
**目标**：实现基于真实数据的能力值趋势分析

**开发任务**：
- [ ] 修复当前模拟数据生成问题
- [ ] 实现AbilityTrendCalculator
- [ ] 创建趋势数据存储机制
- [ ] 实现里程碑识别算法
- [ ] 定时任务计算趋势数据

**验收标准**：
- 趋势数据基于真实比赛历史
- 里程碑识别准确率>90%
- 趋势图表数据流畅无断点

#### Sprint 4: 联盟分析和对比（1周）
**目标**：实现与联盟数据的对比分析

**开发任务**：
- [ ] 实现LeagueAnalysisService
- [ ] 联盟统计数据计算和缓存
- [ ] 实现能力分布分析
- [ ] 球员与联盟对比功能
- [ ] 排行榜功能优化

**验收标准**：
- 联盟数据计算准确
- 对比分析结果合理
- 排行榜查询性能<100ms

#### Sprint 5: 高级功能和优化（1周）
**目标**：完善高级功能和性能优化

**开发任务**：
- [ ] 实现能力相关性分析
- [ ] 位置推荐算法优化
- [ ] 能力报告生成功能
- [ ] 数据质量监控
- [ ] 性能优化和压力测试

**验收标准**：
- 所有API响应时间<200ms
- 系统并发能力达到1000用户
- 数据质量监控完善

### 7.2 每周交付物

| Sprint | 主要交付物 | 可运行功能 |
|--------|------------|------------|
| Sprint 1 | 核心算法实现 | 能力评分基础计算 |
| Sprint 2 | 数据更新机制 | 比赛后自动更新能力值 |
| Sprint 3 | 趋势分析 | 能力值历史趋势查看 |
| Sprint 4 | 联盟对比 | 与联盟数据对比分析 |
| Sprint 5 | 完整功能 | 所有能力评分功能完整可用 |

### 7.3 风险控制

#### 7.3.1 技术风险
- **算法一致性风险**：Java实现与Python参考可能不一致
  - **缓解措施**：建立自动化对比测试，确保算法结果一致
  
- **性能风险**：大量计算可能影响系统性能
  - **缓解措施**：异步处理、分批计算、智能缓存

- **数据一致性风险**：多数据源更新可能导致数据不一致
  - **缓解措施**：事务控制、数据校验、监控告警

#### 7.3.2 业务风险
- **算法合理性风险**：评分结果可能不符合用户预期
  - **缓解措施**：A/B测试、用户反馈收集、算法可调整

- **历史数据迁移风险**：现有数据迁移可能出现问题
  - **缓解措施**：分步迁移、数据备份、回滚机制

### 7.4 成功标准

#### 7.4.1 功能完整性
- ✅ 7维度能力评分算法完全实现
- ✅ 基于真实数据的趋势分析
- ✅ 位置适应度分析准确
- ✅ 与联盟数据对比功能
- ✅ 实时能力值更新机制

#### 7.4.2 性能指标
- ✅ 能力评分计算：<50ms
- ✅ 趋势数据查询：<100ms
- ✅ 排行榜查询：<100ms
- ✅ 联盟数据对比：<200ms
- ✅ 系统并发能力：1000用户

#### 7.4.3 质量标准
- ✅ 代码测试覆盖率：>90%
- ✅ 数据准确性：>99.9%
- ✅ 系统可用性：>99.5%
- ✅ 用户满意度：>85%

---

## 八、简化架构完善

### 8.1 简化架构要点

#### 📊 Z-Score标准化算法设计（新算法核心）
```java
// 新增：Z-Score标准化算法核心实现
@Component
public class EnhancedAbilityRatingCalculator {
    private static final Double BASE_RATING = 60.0;        // 基础评分
    private static final Double SCALE_FACTOR = 15.0;       // 缩放系数
    private static final Integer MIN_SAMPLE_SIZE = 20;     // 最小样本数量
    
    /**
     * Z-Score标准化评分计算
     * 核心创新：科学的统计分布理论替代排名算法
     */
    public Double calculateZScoreRating(Double playerValue, Double leagueMean, 
                                      Double leagueStdDev, Integer sampleSize) {
        // 小样本置信度调整 - 针对业余联赛特点优化
        double confidence = Math.min(1.0, (double) sampleSize / MIN_SAMPLE_SIZE);
        double adjustedValue = playerValue * confidence + leagueMean * (1 - confidence);
        
        // Z-Score计算 - 标准统计分布
        double zScore = (adjustedValue - leagueMean) / leagueStdDev;
        
        // 转换为60-100分区间
        double rating = BASE_RATING + zScore * SCALE_FACTOR;
        
        // 边界处理
        return Math.max(20.0, Math.min(100.0, rating));
    }
    
    /**
     * 时间衰减权重计算
     * 最近表现的权重更高
     */
    public Double calculateTimeDecayWeight(LocalDate gameDate, LocalDate currentDate) {
        long daysDiff = ChronoUnit.DAYS.between(gameDate, currentDate);
        // 指数衰减：每30天衰减一半
        return Math.exp(-daysDiff / 30.0);
    }
    
    /**
     * 10维度扩展评分体系（可选升级）
     */
    public Map<String, Double> calculateExtendedRatings(PlayerStatsVO stats, LeagueStatsVO league) {
        Map<String, Double> ratings = new HashMap<>();
        
        // 原有7维度
        ratings.put("efficiency", calculateZScoreRating(stats.getEfficiency(), 
            league.getEfficiencyMean(), league.getEfficiencyStd(), league.getSampleSize()));
        ratings.put("scoring", calculateZScoreRating(stats.getPoints(), 
            league.getPointsMean(), league.getPointsStd(), league.getSampleSize()));
        // ... 其他7维度
        
        // 新增3维度（可选）
        ratings.put("clutch", calculateClutchRating(stats));      // 关键时刻表现
        ratings.put("consistency", calculateConsistencyRating(stats)); // 稳定性
        ratings.put("improvement", calculateImprovementRating(stats)); // 进步幅度
        
        return ratings;
    }
}
```

#### 🚀 配置化权重管理系统
```java
// 支持算法参数动态调整
@ConfigurationProperties(prefix = "ability.rating")
public class AbilityRatingConfig {
    private String defaultAlgorithm = "ranking";
    private boolean enableZScore = false;
    
    // 位置权重配置（支持热更新）
    private Map<String, Map<String, Double>> positionWeights = new HashMap<>();
    
    // Z-Score算法参数
    private Double baseRating = 60.0;
    private Double scaleFactor = 15.0;
    private Integer minSampleSize = 20;
    
    // 时间衰减参数
    private Double decayDays = 30.0;
    private Boolean enableTimeDecay = false;
    
    // 算法切换配置
    public void switchToZScore() {
        this.enableZScore = true;
        this.defaultAlgorithm = "z_score";
    }
    
    public void switchToRanking() {
        this.enableZScore = false;
        this.defaultAlgorithm = "ranking";
    }
}
```

#### 📊 简化联盟统计数据模型
```sql
-- 简化：为Z-Score算法新增必要的联盟统计字段
ALTER TABLE sd_player_ability_rating ADD COLUMN (
    -- 联盟统计数据（用于Z-Score计算）
    league_mean_efficiency DECIMAL(10,4) DEFAULT 0 COMMENT '联盟平均效率',
    league_std_efficiency DECIMAL(10,4) DEFAULT 1 COMMENT '联盟效率标准差',
    league_mean_scoring DECIMAL(10,4) DEFAULT 0 COMMENT '联盟平均得分',
    league_std_scoring DECIMAL(10,4) DEFAULT 1 COMMENT '联盟得分标准差',
    -- 其他维度的联盟统计...
    
    -- 算法版本标识
    algorithm_version VARCHAR(20) DEFAULT 'ranking_v1' COMMENT '算法版本'
);
```

### 8.2 管理界面要求

#### 🖥️ 内管能力值查看功能
```java
@RestController
@RequestMapping("/admin/ability")
public class AdminAbilityController {
    // 查看球员能力值详情
    @GetMapping("/player/{playerId}")
    public PlayerAbilityDetailVO getPlayerAbilityDetail(@PathVariable Long playerId);
    
    // 能力值排行榜
    @GetMapping("/ranking")
    public List<PlayerAbilityRankVO> getAbilityRanking(AbilityRankingReqVO reqVO);
    
    // 算法配置管理
    @GetMapping("/config")
    public AbilityConfigVO getAlgorithmConfig();
    
    @PostMapping("/config")
    public void updateAlgorithmConfig(@RequestBody AbilityConfigVO config);
}
```

#### 📊 数据准确性人工评判
```java
// 人工评判工具
@Component
public class ManualAbilityAssessment {
    // 获取异常评分数据
    public List<AnomalyRatingVO> getAnomalyRatings();
    
    // 标记评分准确性
    public void markRatingAccuracy(Long playerId, Boolean isAccurate, String comments);
    
    // 生成准确性报告
    public AccuracyReportVO generateAccuracyReport(LocalDate startDate, LocalDate endDate);
}
```

### 8.3 基本回滚机制

#### ⚡ 配置回滚保障
```java
@Service
public class ConfigRollbackService {
    // 简单配置回滚到上一版本
    public void rollbackAlgorithmConfig(String reason) {
        // 1. 切换到默认算法
        configService.resetToDefault();
        
        // 2. 清除相关缓存
        cacheManager.evictAll();
        
        // 3. 记录回滚日志
        auditService.logRollback(reason);
        
        log.warn("执行配置回滚: {}", reason);
    }
}
```

---

## 九、简化实施方案

### 9.1 简化4步策略
- **V1.0**: 现有算法优化（1周） - 立即改善用户体验
- **V1.1**: 联盟统计基础（1周） - 增加对比功能
- **V1.2**: Z-Score算法试点（1周） - 小范围验证
- **V1.3**: 算法全面升级（1周） - 基于反馈决策

### 9.2 质量保证
- ✅ **向后兼容**：新功能不影响现有功能
- ✅ **内管查看**：管理员可查看球员能力值信息和排名
- ✅ **人工评判**：人工评判能力值准确性，无需复杂监控
- ✅ **配置回滚**：可快速切换算法配置

---

**文档最后更新：2025年7月27日**  
**版本：v2.0 - 简化新算法实现方案**

**架构师：Claude**  
**设计日期：2025年7月27日**  
**文档版本：v2.0**