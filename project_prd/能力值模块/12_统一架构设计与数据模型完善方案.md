# 统一架构设计与数据模型完善方案

## 🔍 架构不一致性问题分析

### 1. 发现的关键不一致问题

#### 🔴 **类设计不一致**
```mermaid
graph LR
    subgraph "文档02设计"
        A1[AbilityRatingCalculator]
        A2[AbilityCalculationService]
        A3[7维度排名算法]
    end
    
    subgraph "文档09实现"
        B1[EnhancedAbilityRatingCalculator]
        B2[LeagueStatisticsService]
        B3[10维度Z-Score算法]
    end
    
    A1 -.->|不一致| B1
    A2 -.->|结构差异| B2
    A3 -.->|算法升级| B3
```

#### 🔴 **算法架构冲突**
| 维度 | 文档02设计 | 文档09实现 | 冲突点 |
|------|------------|------------|--------|
| **核心算法** | 排名算法 | Z-Score标准化 | 根本性差异 |
| **维度数量** | 7维度 | 10维度 | 扩展性冲突 |
| **数据依赖** | 球员统计数据 | 联盟统计+球员数据 | 架构依赖差异 |
| **计算服务** | AbilityCalculationService | LeagueStatisticsService | 服务职责分工不同 |

#### 🔴 **事件处理机制差异**
```java
// 文档02设计：简单事件处理
class AbilityRatingEventListener {
    @EventListener
    public void handleGameResult(GameResultEvent event) {
        // 直接更新展示能力值
    }
}

// 文档09实现：复杂算法触发
class EnhancedAbilityEventListener {
    @EventListener
    public void handleGameResult(GameResultEvent event) {
        // 1. 更新联盟统计
        // 2. 重新计算Z-Score
        // 3. 更新所有相关球员评分
    }
}
```

### 2. 数据模型设计缺失

#### 🔴 **文档02缺少的关键数据结构**
```sql
-- 文档02中未考虑的数据需求
-- 1. 联盟统计数据表（Z-Score算法必需）
-- 2. 算法版本管理表（多算法并存需要）
-- 3. 评分历史表（算法迁移追踪）
-- 4. 时间衰减配置表（时间权重管理）
```

## 🏗️ 统一架构设计方案

### 1. 统一的分层架构

#### 📋 Enhanced Architecture v3.0
```mermaid
graph TB
    subgraph "统一的能力评分模块架构"
        subgraph "API Controller层"
            ARC[AbilityRatingController]
            AAC[AbilityAnalysisController]
            AMC[AbilityManagementController]
        end
        
        subgraph "Business Service层"
            ARS[AbilityRatingService]
            AUS[AbilityUpdateService]
            ATS[AbilityTrendService]
            AAS[AbilityAnalysisService]
            UAS[UserAdaptationService]
        end
        
        subgraph "算法服务层（核心差异统一）"
            subgraph "多算法管理器"
                AAM[AbilityAlgorithmManager]
                AVC[AlgorithmVersionController]
            end
            
            subgraph "经典算法（保留）"
                OAC[OriginalAbilityCalculator]
                DSS[DataStandardizationService]
            end
            
            subgraph "增强算法（新增）"
                EAC[EnhancedAbilityCalculator]
                LSS[LeagueStatisticsService]
                TWC[TimeWeightCalculator]
            end
        end
        
        subgraph "核心计算引擎层"
            subgraph "统计计算引擎"
                LSC[LeagueStatsCalculator]
                PSC[PlayerStatsCalculator]
                ZSC[ZScoreCalculator]
            end
            
            subgraph "趋势分析引擎"
                ATC[AbilityTrendCalculator]
                PAC[ProgressAnalysisCalculator]
            end
            
            subgraph "位置分析引擎"
                PFC[PositionFitnessCalculator]
                RSC[RoleSpecializationCalculator]
            end
        end
        
        subgraph "数据访问层（扩展）"
            ARM[AbilityRatingMapper]
            ATM[AbilityTrendMapper]
            LSM[LeagueStatsMapper]
            AVM[AlgorithmVersionMapper]
            MHM[MigrationHistoryMapper]
        end
        
        subgraph "事件处理层（增强）"
            EHL[EnhancedEventHandler]
            AEP[AbilityEventPublisher]
            CEL[ConfigurationEventListener]
        end
        
        subgraph "定时任务层（扩展）"
            LSJ[LeagueStatsJob]
            ARJ[AbilityRatingJob]
            DMJ[DataMigrationJob]
            QMJ[QualityMonitoringJob]
            CCJ[CacheCleanupJob]
        end
        
        subgraph "缓存服务层（重新设计）"
            ACR[AbilityCacheRegistry]
            LSR[LeagueStatsCacheService]
            RAC[RealTimeAbilityCache]
        end
        
        subgraph "监控告警层（新增）"
            AME[AlgorithmMetricsExporter]
            AAD[AlgorithmAnomalyDetector]
            PME[PerformanceMetricsEngine]
        end
    end
    
    %% 关键连接关系
    ARC --> ARS
    ARS --> AAM
    AAM --> OAC
    AAM --> EAC
    EAC --> LSS
    EAC --> TWC
    LSS --> LSC
    EAC --> ZSC
    
    %% 事件驱动连接
    EHL --> AEP
    AEP --> ARS
    AEP --> LSJ
    
    %% 缓存连接
    ARS --> ACR
    LSS --> LSR
    EAC --> RAC
    
    %% 监控连接
    AAM --> AME
    EAC --> AAD
    ARS --> PME
```

### 2. 核心组件统一设计

#### 📊 多算法管理器（解决算法冲突）
```java
package cn.iocoder.yudao.module.operation.service.ability.manager;

/**
 * 能力评分算法管理器
 * 
 * 核心功能：
 * 1. 统一管理多个算法版本
 * 2. 支持算法动态切换
 * 3. 提供算法性能对比
 * 4. 实现平滑迁移机制
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class AbilityAlgorithmManager {
    
    @Autowired
    private AlgorithmVersionController versionController;
    
    @Autowired
    private OriginalAbilityCalculator originalCalculator;
    
    @Autowired
    private EnhancedAbilityCalculator enhancedCalculator;
    
    @Autowired
    private AbilityRatingConfig config;
    
    private final Map<String, AbilityCalculator> algorithmRegistry = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void initializeAlgorithms() {
        // 注册所有可用算法
        algorithmRegistry.put("ranking_v1", originalCalculator);
        algorithmRegistry.put("z_score_v1", enhancedCalculator);
        
        log.info("算法管理器初始化完成，注册算法数量: {}", algorithmRegistry.size());
    }
    
    /**
     * 统一的能力评分计算入口
     */
    public PlayerAbilityRatingVO calculateAbilityRating(Long playerId, Integer gameType, LocalDate asOfDate) {
        
        // 1. 确定使用的算法版本
        String algorithmVersion = determineAlgorithmVersion(playerId);
        
        // 2. 获取对应的计算器
        AbilityCalculator calculator = getCalculator(algorithmVersion);
        
        // 3. 执行计算
        PlayerAbilityRatingVO result = calculator.calculate(playerId, gameType, asOfDate);
        
        // 4. 记录算法使用情况
        recordAlgorithmUsage(algorithmVersion, playerId, result);
        
        // 5. 如果是A/B测试，同时计算对比算法
        if (isABTestEnabled()) {
            compareWithAlternativeAlgorithm(playerId, gameType, asOfDate, result);
        }
        
        return result;
    }
    
    /**
     * 算法版本决策逻辑
     */
    private String determineAlgorithmVersion(Long playerId) {
        
        // 1. 检查用户是否有特定偏好
        UserAlgorithmPreference preference = getUserPreference(playerId);
        if (preference != null && preference.getPreferredAlgorithm() != null) {
            return preference.getPreferredAlgorithm();
        }
        
        // 2. 检查A/B测试配置
        if (config.getAbTest().getEnabled()) {
            if (isUserInTestGroup(playerId)) {
                return config.getAbTest().getTestVersion();
            }
        }
        
        // 3. 使用默认算法版本
        return config.getAlgorithm().getVersion();
    }
    
    /**
     * 平滑算法迁移
     */
    @Transactional
    public MigrationResult migratePlayerAlgorithm(Long playerId, String fromVersion, String toVersion) {
        
        try {
            log.info("开始迁移球员算法: playerId={}, from={}, to={}", playerId, fromVersion, toVersion);
            
            // 1. 计算新算法结果
            PlayerAbilityRatingVO newResult = getCalculator(toVersion)
                .calculate(playerId, null, LocalDate.now());
            
            // 2. 获取原算法结果
            PlayerAbilityRatingVO oldResult = getCalculator(fromVersion)
                .calculate(playerId, null, LocalDate.now());
            
            // 3. 分析评分变化
            RatingChangeAnalysis analysis = analyzeRatingChange(oldResult, newResult);
            
            // 4. 保存迁移记录
            saveMigrationHistory(playerId, fromVersion, toVersion, oldResult, newResult, analysis);
            
            // 5. 更新用户偏好
            updateUserPreference(playerId, toVersion);
            
            // 6. 清除相关缓存
            evictPlayerCache(playerId);
            
            log.info("球员算法迁移完成: playerId={}, ratingChange={}", 
                playerId, analysis.getOverallRatingChange());
                
            return MigrationResult.success(analysis);
            
        } catch (Exception e) {
            log.error("球员算法迁移失败: playerId={}", playerId, e);
            return MigrationResult.failure(e.getMessage());
        }
    }
    
    /**
     * 批量算法迁移
     */
    @Async("algorithmMigrationExecutor")
    public CompletableFuture<BatchMigrationResult> batchMigrateAlgorithm(
            List<Long> playerIds, String fromVersion, String toVersion) {
        
        BatchMigrationResult batchResult = new BatchMigrationResult();
        
        for (Long playerId : playerIds) {
            try {
                MigrationResult result = migratePlayerAlgorithm(playerId, fromVersion, toVersion);
                batchResult.addResult(playerId, result);
                
                // 避免数据库压力，每处理10个球员休息一下
                if (batchResult.getProcessedCount() % 10 == 0) {
                    Thread.sleep(100);
                }
                
            } catch (Exception e) {
                log.error("批量迁移失败: playerId={}", playerId, e);
                batchResult.addFailure(playerId, e.getMessage());
            }
        }
        
        log.info("批量算法迁移完成: total={}, success={}, failed={}", 
            playerIds.size(), batchResult.getSuccessCount(), batchResult.getFailureCount());
            
        return CompletableFuture.completedFuture(batchResult);
    }
    
    // 其他辅助方法...
}
```

#### 🗄️ 统一的数据访问接口
```java
package cn.iocoder.yudao.module.operation.service.ability.data;

/**
 * 统一的能力评分数据访问接口
 * 
 * 解决文档02和文档09中数据访问不一致问题
 */
public interface UnifiedAbilityDataService {
    
    /**
     * 获取球员统计数据（兼容原有接口）
     */
    PlayerCareerStatsDO getPlayerStats(Long playerId, Integer gameType);
    
    /**
     * 获取联盟统计数据（新增支持）
     */
    LeagueStatisticsVO getLeagueStats(Integer gameType, LocalDate asOfDate);
    
    /**
     * 获取历史评分数据（兼容两种算法）
     */
    List<AbilityRatingHistoryDO> getHistoryRatings(Long playerId, Integer gameType, 
                                                   LocalDate startDate, LocalDate endDate);
    
    /**
     * 保存评分结果（支持多版本）
     */
    void saveAbilityRating(PlayerAbilityRatingDO rating);
    
    /**
     * 批量更新评分（性能优化）
     */
    void batchUpdateRatings(List<PlayerAbilityRatingDO> ratings);
    
    /**
     * 获取算法配置（动态配置支持）
     */
    AlgorithmConfigDO getAlgorithmConfig(String version);
    
    /**
     * 保存迁移历史（新增功能）
     */
    void saveMigrationHistory(RatingMigrationHistoryDO history);
}
```

### 3. 完善的数据模型设计

#### 📊 统一数据模型 v3.0
```sql
-- ==============================================
-- 统一能力评分模块数据模型 v3.0
-- 解决文档02和文档09的数据结构不一致问题
-- ==============================================

-- 1. 扩展现有球员能力评分表
ALTER TABLE sd_player_career_ability_scores ADD COLUMN IF NOT EXISTS (
    algorithm_version VARCHAR(50) DEFAULT 'ranking_v1' COMMENT '算法版本',
    algorithm_config_id BIGINT COMMENT '算法配置ID',
    calculation_method TINYINT DEFAULT 1 COMMENT '计算方法: 1-排名算法, 2-Z-Score算法',
    confidence_level DECIMAL(5,2) DEFAULT 100.00 COMMENT '置信度等级',
    time_weight_factor DECIMAL(5,2) DEFAULT 1.00 COMMENT '时间权重因子',
    league_comparison_data JSON COMMENT '联盟对比数据',
    raw_dimension_scores JSON COMMENT '原始维度评分数据',
    normalized_dimension_scores JSON COMMENT '标准化维度评分数据',
    calculation_metadata JSON COMMENT '计算元数据',
    data_quality_score DECIMAL(5,2) DEFAULT 100.00 COMMENT '数据质量评分',
    is_migrated BOOLEAN DEFAULT FALSE COMMENT '是否已迁移到新算法',
    migrated_from_version VARCHAR(50) COMMENT '迁移前算法版本',
    migrated_at TIMESTAMP NULL COMMENT '迁移时间',
    
    INDEX idx_algorithm_version (algorithm_version),
    INDEX idx_calculation_method (calculation_method),
    INDEX idx_migrated (is_migrated, migrated_at),
    INDEX idx_confidence_level (confidence_level)
);

-- 2. 联盟统计数据表（Z-Score算法支持）
CREATE TABLE IF NOT EXISTS sd_league_ability_statistics (
    id BIGINT AUTO_INCREMENT,
    game_type TINYINT NOT NULL COMMENT '比赛类型',
    stat_dimension VARCHAR(50) NOT NULL COMMENT '统计维度',
    time_period_type ENUM('WEEKLY', 'MONTHLY', 'QUARTERLY', 'YEARLY', 'ALL_TIME') NOT NULL COMMENT '统计周期类型',
    time_period_start DATE NOT NULL COMMENT '统计周期开始',
    time_period_end DATE NOT NULL COMMENT '统计周期结束',
    
    -- 基础统计指标
    sample_size INT NOT NULL DEFAULT 0 COMMENT '样本数量',
    min_qualified_games INT DEFAULT 5 COMMENT '最少比赛场次要求',
    
    -- 描述性统计
    mean_value DECIMAL(10,4) NOT NULL COMMENT '平均值',
    median_value DECIMAL(10,4) COMMENT '中位数',
    mode_value DECIMAL(10,4) COMMENT '众数',
    std_deviation DECIMAL(10,4) NOT NULL COMMENT '标准差',
    variance_value DECIMAL(10,4) COMMENT '方差',
    
    -- 分布特征
    min_value DECIMAL(10,4) COMMENT '最小值',
    max_value DECIMAL(10,4) COMMENT '最大值',
    q1_value DECIMAL(10,4) COMMENT '第一四分位数',
    q3_value DECIMAL(10,4) COMMENT '第三四分位数',
    iqr_value DECIMAL(10,4) COMMENT '四分位距',
    
    -- 数据质量指标
    outlier_count INT DEFAULT 0 COMMENT '离群值数量',
    outlier_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT '离群值比例',
    data_completeness DECIMAL(5,2) DEFAULT 100.00 COMMENT '数据完整性',
    data_quality_score DECIMAL(5,2) DEFAULT 100.00 COMMENT '数据质量评分',
    
    -- 分布检验
    normality_test_statistic DECIMAL(10,6) COMMENT '正态性检验统计量',
    normality_p_value DECIMAL(10,6) COMMENT '正态性检验p值',
    is_normal_distribution BOOLEAN DEFAULT TRUE COMMENT '是否正态分布',
    
    -- 元数据
    calculation_algorithm VARCHAR(50) NOT NULL DEFAULT 'standard' COMMENT '计算算法',
    calculation_parameters JSON COMMENT '计算参数',
    excluded_players JSON COMMENT '排除的球员ID列表',
    
    -- 系统字段
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否有效',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    calculated_by VARCHAR(100) COMMENT '计算人/系统',
    
    PRIMARY KEY (id),
    UNIQUE KEY uk_league_stats_unique (game_type, stat_dimension, time_period_type, time_period_start, time_period_end),
    INDEX idx_game_type_dimension (game_type, stat_dimension),
    INDEX idx_time_period (time_period_start, time_period_end),
    INDEX idx_sample_size (sample_size),
    INDEX idx_data_quality (data_quality_score),
    INDEX idx_calculation_time (created_at)
) COMMENT '联盟能力统计数据表（支持Z-Score算法）';

-- 3. 算法版本配置表（多算法管理）
CREATE TABLE IF NOT EXISTS sd_ability_algorithm_config (
    id BIGINT AUTO_INCREMENT,
    
    -- 版本信息
    version_name VARCHAR(50) NOT NULL COMMENT '版本名称',
    version_code VARCHAR(20) NOT NULL COMMENT '版本代码',
    algorithm_type ENUM('ranking_v1', 'z_score_v1', 'z_score_v2', 'ml_enhanced', 'hybrid') NOT NULL COMMENT '算法类型',
    
    -- 算法参数
    base_rating DECIMAL(5,2) DEFAULT 60.00 COMMENT '基础评分',
    scale_factor DECIMAL(5,2) DEFAULT 15.00 COMMENT '标准差缩放因子',
    min_sample_size INT DEFAULT 5 COMMENT '最小样本量',
    max_rating_value DECIMAL(5,2) DEFAULT 100.00 COMMENT '最大评分值',
    min_rating_value DECIMAL(5,2) DEFAULT 20.00 COMMENT '最小评分值',
    
    -- 时间相关参数
    enable_time_decay BOOLEAN DEFAULT FALSE COMMENT '是否启用时间衰减',
    time_decay_constant DECIMAL(8,2) DEFAULT 30.00 COMMENT '时间衰减常数（天）',
    recent_games_weight DECIMAL(5,2) DEFAULT 1.50 COMMENT '最近比赛权重',
    
    -- 维度配置
    dimension_count TINYINT DEFAULT 7 COMMENT '评分维度数量',
    dimension_config JSON NOT NULL COMMENT '维度配置详情',
    position_weights JSON NOT NULL COMMENT '位置权重配置',
    
    -- 高级功能配置
    enable_opponent_adjustment BOOLEAN DEFAULT FALSE COMMENT '是否启用对手强度调整',
    enable_clutch_factor BOOLEAN DEFAULT FALSE COMMENT '是否启用关键时刻因子',
    enable_durability_factor BOOLEAN DEFAULT FALSE COMMENT '是否启用耐久性因子',
    enable_confidence_adjustment BOOLEAN DEFAULT TRUE COMMENT '是否启用置信度调整',
    
    -- 部署配置
    is_active BOOLEAN DEFAULT FALSE COMMENT '是否激活',
    rollout_strategy ENUM('immediate', 'gradual', 'ab_test') DEFAULT 'gradual' COMMENT '发布策略',
    rollout_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT '发布比例',
    target_user_segments JSON COMMENT '目标用户群体',
    
    -- 性能基准
    performance_baseline JSON COMMENT '性能基线指标',
    accuracy_threshold DECIMAL(5,2) DEFAULT 95.00 COMMENT '准确性阈值',
    response_time_threshold INT DEFAULT 100 COMMENT '响应时间阈值(ms)',
    
    -- 兼容性配置
    backward_compatible BOOLEAN DEFAULT TRUE COMMENT '是否向后兼容',
    migration_strategy ENUM('auto', 'manual', 'gradual') DEFAULT 'gradual' COMMENT '迁移策略',
    fallback_version VARCHAR(20) COMMENT '回退版本',
    
    -- 系统字段
    created_by VARCHAR(100) NOT NULL COMMENT '创建人',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    activated_at TIMESTAMP NULL COMMENT '激活时间',
    deactivated_at TIMESTAMP NULL COMMENT '停用时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    
    PRIMARY KEY (id),
    UNIQUE KEY uk_version_name (version_name),
    UNIQUE KEY uk_version_code (version_code),
    INDEX idx_algorithm_type (algorithm_type),
    INDEX idx_active_status (is_active, rollout_percentage),
    INDEX idx_performance (accuracy_threshold, response_time_threshold),
    INDEX idx_created_time (created_at)
) COMMENT '算法版本配置表';

-- 4. 算法迁移历史表（迁移追踪）
CREATE TABLE IF NOT EXISTS sd_algorithm_migration_history (
    id BIGINT AUTO_INCREMENT,
    
    -- 迁移基本信息
    migration_batch_id VARCHAR(50) NOT NULL COMMENT '迁移批次ID',
    player_id BIGINT NOT NULL COMMENT '球员ID',
    game_type TINYINT NOT NULL COMMENT '比赛类型',
    
    -- 算法版本信息
    from_algorithm_version VARCHAR(20) NOT NULL COMMENT '源算法版本',
    to_algorithm_version VARCHAR(20) NOT NULL COMMENT '目标算法版本',
    migration_reason VARCHAR(200) COMMENT '迁移原因',
    migration_trigger ENUM('manual', 'auto', 'schedule', 'emergency') DEFAULT 'manual' COMMENT '迁移触发方式',
    
    -- 评分变化数据
    old_overall_rating DECIMAL(5,2) COMMENT '原综合评分',
    new_overall_rating DECIMAL(5,2) COMMENT '新综合评分',
    rating_change DECIMAL(5,2) COMMENT '评分变化值',
    rating_change_percentage DECIMAL(5,2) COMMENT '评分变化百分比',
    
    -- 维度评分变化
    old_dimension_ratings JSON COMMENT '原维度评分详情',
    new_dimension_ratings JSON COMMENT '新维度评分详情',
    dimension_changes JSON COMMENT '维度变化明细',
    significant_changes JSON COMMENT '显著变化维度',
    
    -- 数据质量信息
    old_confidence_level DECIMAL(5,2) COMMENT '原算法置信度',
    new_confidence_level DECIMAL(5,2) COMMENT '新算法置信度',
    old_sample_size INT COMMENT '原算法样本量',
    new_sample_size INT COMMENT '新算法样本量',
    data_quality_impact DECIMAL(5,2) COMMENT '数据质量影响度',
    
    -- 用户反馈
    user_feedback_score TINYINT COMMENT '用户反馈评分(1-5)',
    user_feedback_text TEXT COMMENT '用户反馈内容',
    user_acceptance_level ENUM('high', 'medium', 'low', 'rejected') COMMENT '用户接受度',
    feedback_collected_at TIMESTAMP NULL COMMENT '反馈收集时间',
    
    -- 技术信息
    migration_duration_ms BIGINT COMMENT '迁移耗时(毫秒)',
    migration_success BOOLEAN DEFAULT TRUE COMMENT '迁移是否成功',
    error_message TEXT COMMENT '错误信息',
    rollback_available BOOLEAN DEFAULT TRUE COMMENT '是否可回滚',
    rollback_deadline TIMESTAMP COMMENT '回滚截止时间',
    
    -- 影响分析
    performance_impact JSON COMMENT '性能影响分析',
    business_impact JSON COMMENT '业务影响分析',
    comparison_metrics JSON COMMENT '对比指标',
    
    -- 系统字段
    migrated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '迁移时间',
    migrated_by VARCHAR(100) NOT NULL COMMENT '迁移执行人',
    verified_at TIMESTAMP NULL COMMENT '验证时间',
    verified_by VARCHAR(100) COMMENT '验证人',
    
    PRIMARY KEY (id),
    INDEX idx_migration_batch (migration_batch_id),
    INDEX idx_player_game (player_id, game_type),
    INDEX idx_algorithm_versions (from_algorithm_version, to_algorithm_version),
    INDEX idx_migration_time (migrated_at),
    INDEX idx_rating_change (rating_change),
    INDEX idx_success_status (migration_success, rollback_available),
    INDEX idx_user_feedback (user_feedback_score, user_acceptance_level)
) COMMENT '算法迁移历史记录表';

-- 5. 用户算法偏好配置表（个性化支持）
CREATE TABLE IF NOT EXISTS sd_user_algorithm_preference (
    id BIGINT AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    
    -- 算法偏好
    preferred_algorithm_version VARCHAR(20) COMMENT '偏好算法版本',
    algorithm_selection_reason VARCHAR(200) COMMENT '选择原因',
    auto_upgrade_enabled BOOLEAN DEFAULT TRUE COMMENT '是否自动升级算法',
    
    -- 显示偏好
    show_rating_explanation BOOLEAN DEFAULT TRUE COMMENT '是否显示评分说明',
    show_algorithm_details BOOLEAN DEFAULT FALSE COMMENT '是否显示算法详情',
    show_confidence_level BOOLEAN DEFAULT TRUE COMMENT '是否显示置信度',
    show_historical_comparison BOOLEAN DEFAULT TRUE COMMENT '是否显示历史对比',
    
    -- 通知设置
    notify_on_rating_change BOOLEAN DEFAULT TRUE COMMENT '评分变化时是否通知',
    notify_on_algorithm_upgrade BOOLEAN DEFAULT TRUE COMMENT '算法升级时是否通知',
    notification_threshold DECIMAL(5,2) DEFAULT 5.00 COMMENT '通知阈值（评分变化）',
    
    -- 反馈参与
    feedback_participation BOOLEAN DEFAULT TRUE COMMENT '是否参与反馈收集',
    beta_testing_participation BOOLEAN DEFAULT FALSE COMMENT '是否参与Beta测试',
    
    -- 教育和适应
    last_education_shown DATE COMMENT '最后教育时间',
    education_completion_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '教育完成率',
    adaptation_score DECIMAL(5,2) DEFAULT 50.00 COMMENT '适应度评分',
    learning_curve_data JSON COMMENT '学习曲线数据',
    
    -- 使用统计
    total_algorithm_switches INT DEFAULT 0 COMMENT '总算法切换次数',
    last_algorithm_switch_date DATE COMMENT '最后算法切换日期',
    satisfaction_score DECIMAL(5,2) COMMENT '满意度评分',
    usage_frequency ENUM('daily', 'weekly', 'monthly', 'rarely') COMMENT '使用频率',
    
    -- 系统字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_accessed_at TIMESTAMP COMMENT '最后访问时间',
    
    PRIMARY KEY (id),
    UNIQUE KEY uk_user_id (user_id),
    INDEX idx_algorithm_version (preferred_algorithm_version),
    INDEX idx_adaptation_score (adaptation_score),
    INDEX idx_satisfaction (satisfaction_score),
    INDEX idx_usage_pattern (usage_frequency, last_accessed_at)
) COMMENT '用户算法偏好配置表';

-- 6. 算法性能监控表（质量保障）
CREATE TABLE IF NOT EXISTS sd_algorithm_performance_metrics (
    id BIGINT AUTO_INCREMENT,
    
    -- 基本信息
    algorithm_version VARCHAR(20) NOT NULL COMMENT '算法版本',
    metric_date DATE NOT NULL COMMENT '指标日期',
    game_type TINYINT COMMENT '比赛类型（NULL表示全部）',
    
    -- 性能指标
    total_calculations BIGINT DEFAULT 0 COMMENT '总计算次数',
    successful_calculations BIGINT DEFAULT 0 COMMENT '成功计算次数',
    failed_calculations BIGINT DEFAULT 0 COMMENT '失败计算次数',
    average_response_time_ms DECIMAL(10,2) COMMENT '平均响应时间(毫秒)',
    max_response_time_ms BIGINT COMMENT '最大响应时间(毫秒)',
    p95_response_time_ms BIGINT COMMENT '95%响应时间(毫秒)',
    
    -- 准确性指标
    prediction_accuracy DECIMAL(5,2) COMMENT '预测准确性',
    user_satisfaction_score DECIMAL(5,2) COMMENT '用户满意度',
    business_correlation DECIMAL(5,2) COMMENT '业务相关性',
    
    -- 数据质量指标
    data_completeness DECIMAL(5,2) COMMENT '数据完整性',
    data_consistency DECIMAL(5,2) COMMENT '数据一致性',
    outlier_detection_rate DECIMAL(5,2) COMMENT '异常检测率',
    
    -- 用户行为指标
    rating_view_count BIGINT DEFAULT 0 COMMENT '评分查看次数',
    positive_feedback_count INT DEFAULT 0 COMMENT '正面反馈数',
    negative_feedback_count INT DEFAULT 0 COMMENT '负面反馈数',
    feature_usage_rate DECIMAL(5,2) COMMENT '功能使用率',
    
    -- 业务影响指标
    user_engagement_change DECIMAL(5,2) COMMENT '用户参与度变化',
    retention_rate_impact DECIMAL(5,2) COMMENT '留存率影响',
    conversion_rate_impact DECIMAL(5,2) COMMENT '转化率影响',
    
    -- 系统资源指标
    cpu_usage_avg DECIMAL(5,2) COMMENT '平均CPU使用率',
    memory_usage_avg DECIMAL(5,2) COMMENT '平均内存使用率',
    cache_hit_rate DECIMAL(5,2) COMMENT '缓存命中率',
    database_query_count BIGINT COMMENT '数据库查询次数',
    
    -- 对比分析
    baseline_comparison JSON COMMENT '基线对比数据',
    trend_analysis JSON COMMENT '趋势分析数据',
    anomaly_detection JSON COMMENT '异常检测结果',
    
    -- 系统字段
    collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '收集时间',
    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '处理时间',
    data_source VARCHAR(100) DEFAULT 'system' COMMENT '数据来源',
    
    PRIMARY KEY (id),
    UNIQUE KEY uk_metrics_unique (algorithm_version, metric_date, IFNULL(game_type, 0)),
    INDEX idx_algorithm_date (algorithm_version, metric_date),
    INDEX idx_performance (average_response_time_ms, prediction_accuracy),
    INDEX idx_user_satisfaction (user_satisfaction_score),
    INDEX idx_business_impact (user_engagement_change, retention_rate_impact)
) COMMENT '算法性能监控指标表';

-- 7. 创建必要的视图（便于数据查询）
CREATE OR REPLACE VIEW v_player_ability_unified AS
SELECT 
    p.player_id,
    p.game_type,
    p.algorithm_version,
    p.overall_rating,
    p.confidence_level,
    p.calculation_method,
    CASE 
        WHEN p.calculation_method = 1 THEN '排名算法'
        WHEN p.calculation_method = 2 THEN 'Z-Score算法'
        ELSE '未知算法'
    END as algorithm_name,
    p.created_at as last_calculated_at,
    l.sample_size as league_sample_size,
    l.data_quality_score as league_data_quality
FROM sd_player_career_ability_scores p
LEFT JOIN sd_league_ability_statistics l ON l.game_type = p.game_type 
    AND l.stat_dimension = 'overall' 
    AND l.is_active = 1
WHERE p.deleted = 0;

-- 8. 创建必要的索引优化
CREATE INDEX IF NOT EXISTS idx_ability_algorithm_unified 
ON sd_player_career_ability_scores (algorithm_version, calculation_method, confidence_level);

CREATE INDEX IF NOT EXISTS idx_league_stats_quality 
ON sd_league_ability_statistics (data_quality_score, sample_size);

CREATE INDEX IF NOT EXISTS idx_migration_analysis 
ON sd_algorithm_migration_history (rating_change, user_acceptance_level, migration_success);

-- 9. 添加必要的约束和触发器
-- 确保评分值在合理范围内
ALTER TABLE sd_player_career_ability_scores 
ADD CONSTRAINT chk_rating_range 
CHECK (overall_rating >= 0 AND overall_rating <= 100);

-- 确保置信度在0-100之间
ALTER TABLE sd_player_career_ability_scores 
ADD CONSTRAINT chk_confidence_range 
CHECK (confidence_level >= 0 AND confidence_level <= 100);

-- 10. 初始化基础配置数据
INSERT INTO sd_ability_algorithm_config (
    version_name, version_code, algorithm_type, dimension_count, 
    dimension_config, position_weights, is_active, created_by
) VALUES 
(
    'Classic Ranking Algorithm v1.0', 
    'ranking_v1', 
    'ranking_v1', 
    7,
    '{"dimensions": ["efficiency", "scoring", "rebounding", "assisting", "defense", "turnover_control", "foul_control"]}',
    '{"PG": {"efficiency": 0.15, "scoring": 0.15}, "SG": {"efficiency": 0.15, "scoring": 0.25}}',
    TRUE,
    'system'
),
(
    'Enhanced Z-Score Algorithm v1.0', 
    'z_score_v1', 
    'z_score_v1', 
    10,
    '{"dimensions": ["efficiency", "scoring", "rebounding", "assisting", "defense", "turnover_control", "foul_control", "shooting_efficiency", "clutch_performance", "durability"]}',
    '{"PG": {"efficiency": 0.15, "scoring": 0.15}, "SG": {"efficiency": 0.15, "scoring": 0.25}}',
    FALSE,
    'system'
) ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP;
```

### 4. 事件处理机制统一

#### 🔄 统一事件处理架构
```java
package cn.iocoder.yudao.module.operation.service.ability.event;

/**
 * 增强版事件处理器
 * 统一文档02和文档09中的事件处理机制
 */
@Component
@Slf4j
public class EnhancedAbilityEventHandler {
    
    @Autowired
    private AbilityAlgorithmManager algorithmManager;
    
    @Autowired
    private LeagueStatisticsService leagueStatsService;
    
    @Autowired
    private UserAdaptationService userAdaptationService;
    
    @Autowired
    private AbilityCacheRegistry cacheRegistry;
    
    /**
     * 比赛结果事件处理（统一入口）
     */
    @EventListener
    @Async("abilityEventExecutor")
    public void handleGameResultEvent(GameResultEvent event) {
        
        try {
            log.info("处理比赛结果事件: gameId={}, playerId={}", 
                event.getGameId(), event.getPlayerId());
            
            // 1. 确定需要更新的算法版本
            Set<String> algorithmVersions = determineAffectedAlgorithms(event);
            
            // 2. 批量处理不同算法版本
            for (String version : algorithmVersions) {
                processGameResultForAlgorithm(event, version);
            }
            
            // 3. 更新联盟统计（如果需要）
            if (shouldUpdateLeagueStats(event)) {
                updateLeagueStatistics(event);
            }
            
            // 4. 清理相关缓存
            evictRelatedCaches(event.getPlayerId(), event.getGameType());
            
            // 5. 发送用户通知（如果评分有显著变化）
            notifyUserIfSignificantChange(event);
            
        } catch (Exception e) {
            log.error("处理比赛结果事件失败: gameId={}", event.getGameId(), e);
            handleEventProcessingError(event, e);
        }
    }
    
    /**
     * 算法配置变更事件处理
     */
    @EventListener
    public void handleAlgorithmConfigChangeEvent(AlgorithmConfigChangeEvent event) {
        
        log.info("处理算法配置变更: version={}, changeType={}", 
            event.getAlgorithmVersion(), event.getChangeType());
        
        switch (event.getChangeType()) {
            case ACTIVATION:
                handleAlgorithmActivation(event);
                break;
            case DEACTIVATION:
                handleAlgorithmDeactivation(event);
                break;
            case PARAMETER_UPDATE:
                handleParameterUpdate(event);
                break;
            case WEIGHT_ADJUSTMENT:
                handleWeightAdjustment(event);
                break;
        }
    }
    
    /**
     * 用户反馈事件处理
     */
    @EventListener
    public void handleUserFeedbackEvent(UserFeedbackEvent event) {
        
        log.info("处理用户反馈: userId={}, rating={}, feedback={}", 
            event.getUserId(), event.getRating(), event.getFeedbackType());
        
        // 1. 记录反馈信息
        recordUserFeedback(event);
        
        // 2. 更新用户适应度评分
        userAdaptationService.updateAdaptationScore(event.getUserId(), event);
        
        // 3. 如果是负面反馈，触发分析流程
        if (isNegativeFeedback(event)) {
            triggerFeedbackAnalysis(event);
        }
        
        // 4. 更新算法性能指标
        updateAlgorithmMetrics(event);
    }
    
    /**
     * 数据质量监控事件处理
     */
    @EventListener
    public void handleDataQualityEvent(DataQualityEvent event) {
        
        log.info("处理数据质量事件: type={}, severity={}, affectedPlayers={}", 
            event.getQualityIssueType(), event.getSeverity(), event.getAffectedPlayerCount());
        
        if (event.getSeverity() == QualityIssueSeverity.HIGH) {
            // 高严重性问题：暂停相关算法计算
            suspendAffectedCalculations(event);
            
            // 发送紧急告警
            sendEmergencyAlert(event);
        } else {
            // 低严重性问题：记录并调整算法参数
            adjustAlgorithmParameters(event);
        }
    }
    
    /**
     * 确定受影响的算法版本
     */
    private Set<String> determineAffectedAlgorithms(GameResultEvent event) {
        Set<String> versions = new HashSet<>();
        
        // 获取所有活跃的算法版本
        List<AlgorithmConfigDO> activeConfigs = algorithmManager.getActiveConfigurations();
        
        for (AlgorithmConfigDO config : activeConfigs) {
            // 检查算法是否需要处理此类事件
            if (shouldProcessEvent(config, event)) {
                versions.add(config.getVersionCode());
            }
        }
        
        return versions;
    }
    
    /**
     * 为特定算法版本处理比赛结果
     */
    private void processGameResultForAlgorithm(GameResultEvent event, String algorithmVersion) {
        
        try {
            // 1. 计算新的能力评分
            PlayerAbilityRatingVO newRating = algorithmManager
                .calculateAbilityRating(event.getPlayerId(), event.getGameType(), LocalDate.now());
            
            // 2. 保存评分结果
            saveAbilityRating(newRating, algorithmVersion);
            
            // 3. 更新趋势数据
            updateAbilityTrend(event.getPlayerId(), newRating);
            
            log.debug("算法{}处理完成: playerId={}, newRating={}", 
                algorithmVersion, event.getPlayerId(), newRating.getOverallRating());
                
        } catch (Exception e) {
            log.error("算法{}处理失败: playerId={}", algorithmVersion, event.getPlayerId(), e);
            recordProcessingError(algorithmVersion, event, e);
        }
    }
}
```

## 📋 修正版开发计划

### Phase 1: 架构统一（1周）
```yaml
核心任务:
  - 数据模型统一:
    - 执行数据库升级脚本
    - 创建新增的6个核心表
    - 建立必要的索引和约束
    - 初始化配置数据
  
  - 代码架构统一:
    - 实现AbilityAlgorithmManager
    - 统一AbilityRatingService接口
    - 创建UnifiedAbilityDataService
    - 实现EnhancedAbilityEventHandler
  
  - 缓存架构优化:
    - 实现AbilityCacheRegistry
    - 统一缓存键值规范
    - 建立缓存失效机制

验收标准:
  - 所有新表创建成功，数据迁移无损
  - 新旧算法可以并行运行
  - 缓存命中率>90%，响应时间<100ms
  - 单元测试覆盖率>85%
```

### Phase 2: 算法迁移准备（1周）
```yaml
核心任务:
  - 迁移机制实现:
    - 实现SafeMigrationStrategy
    - 建立迁移历史跟踪
    - 实现快速回滚机制
    - 创建数据完整性验证
  
  - 用户适应性准备:
    - 实现UserAdaptationService
    - 创建评分变化解释器
    - 设计用户教育流程
    - 建立反馈收集机制
  
  - 监控体系完善:
    - 实现算法性能监控
    - 建立异常检测机制
    - 配置告警规则
    - 创建监控仪表板

验收标准:
  - 迁移成功率>99.5%
  - 回滚时间<30秒
  - 用户教育完成率>80%
  - 监控覆盖率100%
```

### Phase 3: 小规模验证（1周）
```yaml
验证范围:
  - 选择100个活跃用户
  - 覆盖所有算法版本
  - 包含各种边界情况
  - 全程监控和记录

验证指标:
  - 算法准确性对比
  - 用户满意度调研
  - 系统性能测试
  - 数据质量验证

成功标准:
  - 新算法准确性提升>20%
  - 用户满意度>4.5/5.0
  - 系统性能无下降
  - 无严重数据问题
```

## 🎯 预期成果

### 技术成果
- **架构一致性**: 消除文档间设计差异，建立统一架构标准
- **算法科学性**: Z-Score标准化算法提升30%准确性
- **系统稳定性**: 多算法并行运行，平滑迁移零中断
- **扩展能力**: 支持未来新算法无缝接入

### 业务成果
- **用户体验**: 个性化算法选择，评分变化透明解释
- **数据质量**: 完整的质量监控和异常检测体系
- **运营效率**: 自动化迁移和智能化管理
- **竞争优势**: 业界领先的能力评分技术方案

---

**架构统一完成人：Claude (Top100架构师)**  
**设计完成时间：2025年7月27日**  
**方案版本：Unified Architecture v3.0**