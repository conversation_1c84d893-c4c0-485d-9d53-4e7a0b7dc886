#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
赛点篮球应用-生涯模块能力评分算法

该模块实现了基于球员比赛数据的能力评分计算功能，包括:
1. 根据场上位置确定评分权重
2. 计算各维度评分
3. 加权计算真实能力值
4. 根据比赛结果调整展示能力值

作者: 赛点篮球应用开发团队
日期: 2025-05-24
版本: 1.0.0
"""

# 尝试导入依赖，如果不存在提供安装指南
try:
    import numpy as np
    import pandas as pd
except ImportError:
    print("错误: 缺少必要的依赖库。")
    print("请执行以下命令安装所需依赖:")
    print("pip install numpy pandas")
    print("\n安装完成后重新运行此程序。")
    exit(1)

from typing import Dict, List, Tuple, Union, Optional
from enum import Enum


class Position(Enum):
    """球员场上位置枚举"""
    PG = "控球后卫"
    SG = "得分后卫"
    SF = "小前锋"
    PF = "大前锋"
    C = "中锋"


class RatingDimension(Enum):
    """评分维度枚举"""
    EFFICIENCY = "效率"
    SCORING = "得分"
    REBOUNDING = "篮板"
    ASSISTING = "助攻"
    DEFENSE = "防守"
    TURNOVER = "失误"
    FOUL = "犯规"


class PlayerRatingCalculator:
    """球员能力评分计算器"""
    
    def __init__(self, data_pool_size: int):
        """
        初始化评分计算器
        
        Args:
            data_pool_size: 数据池中球员总数，用于计算排名百分比
        """
        self.data_pool_size = data_pool_size
        # 不同场上位置的能力值计算权重
        self.position_weights = {
            Position.PG: {  # 控球后卫权重
                RatingDimension.EFFICIENCY: 0.20,
                RatingDimension.SCORING: 0.30,
                RatingDimension.REBOUNDING: 0.10,
                RatingDimension.ASSISTING: 0.40,
                RatingDimension.DEFENSE: 0.20,
                RatingDimension.TURNOVER: -0.10,
                RatingDimension.FOUL: -0.10
            },
            Position.SG: {  # 得分后卫权重
                RatingDimension.EFFICIENCY: 0.20,
                RatingDimension.SCORING: 0.40,
                RatingDimension.REBOUNDING: 0.10,
                RatingDimension.ASSISTING: 0.20,
                RatingDimension.DEFENSE: 0.30,
                RatingDimension.TURNOVER: -0.10,
                RatingDimension.FOUL: -0.10
            },
            Position.SF: {  # 小前锋权重
                RatingDimension.EFFICIENCY: 0.20,
                RatingDimension.SCORING: 0.40,
                RatingDimension.REBOUNDING: 0.20,
                RatingDimension.ASSISTING: 0.20,
                RatingDimension.DEFENSE: 0.20,
                RatingDimension.TURNOVER: -0.10,
                RatingDimension.FOUL: -0.10
            },
            Position.PF: {  # 大前锋权重
                RatingDimension.EFFICIENCY: 0.20,
                RatingDimension.SCORING: 0.30,
                RatingDimension.REBOUNDING: 0.40,
                RatingDimension.ASSISTING: 0.10,
                RatingDimension.DEFENSE: 0.20,
                RatingDimension.TURNOVER: -0.10,
                RatingDimension.FOUL: -0.10
            },
            Position.C: {  # 中锋权重
                RatingDimension.EFFICIENCY: 0.20,
                RatingDimension.SCORING: 0.20,
                RatingDimension.REBOUNDING: 0.40,
                RatingDimension.ASSISTING: 0.10,
                RatingDimension.DEFENSE: 0.30,
                RatingDimension.TURNOVER: -0.10,
                RatingDimension.FOUL: -0.10
            }
        }
        
        # 防守评分中抢断与盖帽的权重
        self.defense_weights = {
            Position.PG: {"steal": 0.70, "block": 0.30},
            Position.SG: {"steal": 0.70, "block": 0.30},
            Position.SF: {"steal": 0.50, "block": 0.50},
            Position.PF: {"steal": 0.30, "block": 0.70},
            Position.C: {"steal": 0.30, "block": 0.70}
        }
    
    def calculate_efficiency_rating(self, efficiency_rank: int) -> float:
        """
        计算效率评分
        
        Args:
            efficiency_rank: 出场效率在数据池中的排名
            
        Returns:
            效率评分(0-100)
        """
        # 效率评分 = (数据池球员总数-球员出场效率排名)/数据池球员总数*100
        return (self.data_pool_size - efficiency_rank) / self.data_pool_size * 100
    
    def calculate_scoring_rating(self, 
                               scoring_rank: int, 
                               true_shooting_rank: int,
                               fg2_pct_rank: int,
                               fg3_pct_rank: int,
                               ft_pct_rank: int) -> float:
        """
        计算得分评分
        
        Args:
            scoring_rank: 得分在数据池中的排名
            true_shooting_rank: 真实命中率在数据池中的排名
            fg2_pct_rank: 二分命中率在数据池中的排名
            fg3_pct_rank: 三分命中率在数据池中的排名
            ft_pct_rank: 罚球命中率在数据池中的排名
            
        Returns:
            得分评分(0-100)
        """
        # 得分评分 = 真实命中率排名(40%) + 得分排名(30%) + 二分命中率排名(10%) + 三分命中率排名(10%) + 罚球命中率排名(10%)
        ts_score = (self.data_pool_size - true_shooting_rank) / self.data_pool_size * 40
        scoring_score = (self.data_pool_size - scoring_rank) / self.data_pool_size * 30
        fg2_score = (self.data_pool_size - fg2_pct_rank) / self.data_pool_size * 10
        fg3_score = (self.data_pool_size - fg3_pct_rank) / self.data_pool_size * 10
        ft_score = (self.data_pool_size - ft_pct_rank) / self.data_pool_size * 10
        
        return ts_score + scoring_score + fg2_score + fg3_score + ft_score
    
    def calculate_rebounding_rating(self, offensive_reb_rank: int, defensive_reb_rank: int) -> float:
        """
        计算篮板评分
        
        Args:
            offensive_reb_rank: 进攻篮板在数据池中的排名
            defensive_reb_rank: 防守篮板在数据池中的排名
            
        Returns:
            篮板评分(0-100)
        """
        # 篮板评分 = 进攻篮板排名(50%) + 防守篮板排名(50%)
        o_reb_score = (self.data_pool_size - offensive_reb_rank) / self.data_pool_size * 50
        d_reb_score = (self.data_pool_size - defensive_reb_rank) / self.data_pool_size * 50
        
        return o_reb_score + d_reb_score
    
    def calculate_assisting_rating(self, assist_rank: int, ast_to_ratio_rank: int) -> float:
        """
        计算助攻评分
        
        Args:
            assist_rank: 助攻在数据池中的排名
            ast_to_ratio_rank: 助攻失误比在数据池中的排名
            
        Returns:
            助攻评分(0-100)
        """
        # 助攻评分 = 助攻排名(50%) + 助攻失误比排名(50%)
        assist_score = (self.data_pool_size - assist_rank) / self.data_pool_size * 50
        ratio_score = (self.data_pool_size - ast_to_ratio_rank) / self.data_pool_size * 50
        
        return assist_score + ratio_score
    
    def calculate_defense_rating(self, steal_rank: int, block_rank: int, position: Position) -> float:
        """
        计算防守评分
        
        Args:
            steal_rank: 抢断在数据池中的排名
            block_rank: 盖帽在数据池中的排名
            position: 球员场上位置
            
        Returns:
            防守评分(0-100)
        """
        # 根据场上位置不同，抢断和盖帽的权重不同
        weights = self.defense_weights[position]
        
        # 防守评分 = 抢断排名 * 抢断权重 + 盖帽排名 * 盖帽权重
        steal_score = (self.data_pool_size - steal_rank) / self.data_pool_size * 100 * weights["steal"]
        block_score = (self.data_pool_size - block_rank) / self.data_pool_size * 100 * weights["block"]
        
        return steal_score + block_score
    
    def calculate_turnover_rating(self, turnover_rank: int) -> float:
        """
        计算失误评分
        
        Args:
            turnover_rank: 失误在数据池中的排名(越少越好)
            
        Returns:
            失误评分(0-100)
        """
        # 失误评分 = (数据池球员总数-球员失误排名)/数据池球员总数*100
        # 注意：失误是负向指标，排名越高越好(失误越少)
        return (self.data_pool_size - turnover_rank) / self.data_pool_size * 100
    
    def calculate_foul_rating(self, foul_rank: int) -> float:
        """
        计算犯规评分
        
        Args:
            foul_rank: 犯规在数据池中的排名(越少越好)
            
        Returns:
            犯规评分(0-100)
        """
        # 犯规评分 = (数据池球员总数-球员犯规排名)/数据池球员总数*100
        # 注意：犯规是负向指标，排名越高越好(犯规越少)
        return (self.data_pool_size - foul_rank) / self.data_pool_size * 100
    
    def calculate_real_ability_rating(self, ratings: Dict[RatingDimension, float], position: Position) -> float:
        """
        计算真实能力值
        
        Args:
            ratings: 各维度评分字典
            position: 球员场上位置
            
        Returns:
            真实能力值(0-100)
        """
        # 获取对应场上位置的权重配置
        weights = self.position_weights[position]
        
        # 加权计算真实能力值
        real_rating = 0
        for dimension, rating in ratings.items():
            real_rating += rating * weights[dimension]
        
        # 确保评分在0-100范围内
        return max(0, min(100, real_rating))
    
    def calculate_displayed_rating_change(self, 
                                        real_rating: float, 
                                        current_displayed_rating: float, 
                                        is_win: bool, 
                                        season_games: int, 
                                        streak: int) -> float:
        """
        计算展示能力值的变化量
        
        Args:
            real_rating: 真实能力值
            current_displayed_rating: 当前展示能力值
            is_win: 是否获胜
            season_games: 赛季参赛次数
            streak: 连胜/连败场次(胜利为正，失败为负)
            
        Returns:
            展示能力值变化量
        """
        # 设置保底变化值
        min_change = 0.1
        
        if is_win:
            # 胜利后能力值变化 = (真实能力值-当前展示能力值) * ((赛季参赛次数+连胜场次)/13) + 0.1
            # 确保(真实能力值-当前展示能力值)>=0
            rating_diff = max(0, real_rating - current_displayed_rating)
            # 确保系数<=1，避免短时间内能力值触达上限
            coefficient = min(1, (season_games + abs(streak)) / 13)
            change = rating_diff * coefficient + min_change
        else:
            # 失利后能力值变化 = (当前展示能力值-真实能力值) * ((赛季参赛次数-连败场次)/13) + 0.1
            # 确保(当前展示能力值-真实能力值)>=0
            rating_diff = max(0, current_displayed_rating - real_rating)
            # 确保系数<=1且>0，避免短时间内能力值触达下限
            coefficient = max(0, min(1, (season_games - abs(streak)) / 13))
            change = rating_diff * coefficient + min_change
        
        return change
    
    def calculate_new_displayed_rating(self, 
                                     real_rating: float, 
                                     current_displayed_rating: float, 
                                     is_win: bool, 
                                     season_games: int, 
                                     streak: int) -> float:
        """
        计算新的展示能力值
        
        Args:
            real_rating: 真实能力值
            current_displayed_rating: 当前展示能力值
            is_win: 是否获胜
            season_games: 赛季参赛次数
            streak: 连胜/连败场次(胜利为正，失败为负)
            
        Returns:
            新的展示能力值
        """
        change = self.calculate_displayed_rating_change(
            real_rating, current_displayed_rating, is_win, season_games, streak
        )
        
        if is_win:
            # 胜利增加能力值
            new_rating = current_displayed_rating + change
        else:
            # 失败减少能力值
            new_rating = current_displayed_rating - change
        
        # 确保能力值在0-100范围内
        return max(0, min(100, new_rating))
    
    def get_position_fit(self, ratings: Dict[RatingDimension, float]) -> Dict[Position, float]:
        """
        计算球员各场上位置的匹配度
        
        Args:
            ratings: 各维度评分字典
            
        Returns:
            各场上位置的匹配度字典
        """
        position_fit = {}
        
        for position in Position:
            # 获取对应场上位置的权重配置
            weights = self.position_weights[position]
            
            # 计算匹配度分数
            fit_score = 0
            for dimension, rating in ratings.items():
                fit_score += rating * abs(weights[dimension])  # 使用权重绝对值计算匹配度
            
            # 标准化为0-100分
            position_fit[position] = fit_score
        
        # 将匹配度转换为百分比
        total_fit = sum(position_fit.values())
        if total_fit > 0:
            for position in Position:
                position_fit[position] = (position_fit[position] / total_fit) * 100
        
        return position_fit


def calculate_player_stats(game_data: Dict) -> Dict:
    """
    计算球员单场比赛的统计数据
    
    Args:
        game_data: 球员单场比赛原始数据
        
    Returns:
        计算后的统计数据
    """
    # 基础数据提取
    points = game_data.get('points', 0)
    rebounds = game_data.get('total_rebounds', 0)
    offensive_rebounds = game_data.get('offensive_rebounds', 0)
    defensive_rebounds = game_data.get('defensive_rebounds', 0)
    assists = game_data.get('assists', 0)
    steals = game_data.get('steals', 0)
    blocks = game_data.get('blocks', 0)
    turnovers = game_data.get('turnovers', 0)
    fouls = game_data.get('fouls', 0)
    
    # 投篮数据
    fg_made = game_data.get('field_goals_made', 0)
    fg_attempted = game_data.get('field_goals_attempted', 0)
    fg3_made = game_data.get('three_pointers_made', 0)
    fg3_attempted = game_data.get('three_pointers_attempted', 0)
    ft_made = game_data.get('free_throws_made', 0)
    ft_attempted = game_data.get('free_throws_attempted', 0)
    
    # 计算二分球数据
    fg2_made = fg_made - fg3_made
    fg2_attempted = fg_attempted - fg3_attempted
    
    # 命中率计算
    fg_pct = fg_made / fg_attempted if fg_attempted > 0 else 0
    fg2_pct = fg2_made / fg2_attempted if fg2_attempted > 0 else 0
    fg3_pct = fg3_made / fg3_attempted if fg3_attempted > 0 else 0
    ft_pct = ft_made / ft_attempted if ft_attempted > 0 else 0
    
    # 高级数据计算
    # 1. 真实命中率 = 全场得分/(2×全场出手次数 + 0.44×罚球出手次数)
    true_shooting_pct = points / (2 * fg_attempted + 0.44 * ft_attempted) if (2 * fg_attempted + 0.44 * ft_attempted) > 0 else 0
    
    # 2. 有效投篮命中率 = (投篮命中数+三分命中数*0.5)/投篮出手数
    effective_fg_pct = (fg_made + 0.5 * fg3_made) / fg_attempted if fg_attempted > 0 else 0
    
    # 3. 助攻失误比 = 助攻数/失误数
    ast_to_ratio = assists / turnovers if turnovers > 0 else assists
    
    # 4. 出场效率 = (得分+篮板+助攻+抢断+盖帽)-(投篮不中数+罚球不中数+失误)
    efficiency = (points + rebounds + assists + steals + blocks) - \
                ((fg_attempted - fg_made) + (ft_attempted - ft_made) + turnovers)
    
    # 返回计算结果
    return {
        # 基础数据
        'points': points,
        'rebounds': rebounds,
        'offensive_rebounds': offensive_rebounds,
        'defensive_rebounds': defensive_rebounds,
        'assists': assists,
        'steals': steals,
        'blocks': blocks,
        'turnovers': turnovers,
        'fouls': fouls,
        
        # 投篮数据
        'fg_made': fg_made,
        'fg_attempted': fg_attempted,
        'fg2_made': fg2_made,
        'fg2_attempted': fg2_attempted,
        'fg3_made': fg3_made,
        'fg3_attempted': fg3_attempted,
        'ft_made': ft_made,
        'ft_attempted': ft_attempted,
        
        # 命中率
        'fg_pct': fg_pct,
        'fg2_pct': fg2_pct,
        'fg3_pct': fg3_pct,
        'ft_pct': ft_pct,
        
        # 高级数据
        'true_shooting_pct': true_shooting_pct,
        'effective_fg_pct': effective_fg_pct,
        'ast_to_ratio': ast_to_ratio,
        'efficiency': efficiency
    }


def normalize_stats_by_time(stats: Dict, actual_minutes: float, target_minutes: float) -> Dict:
    """
    按照时间标准化统计数据
    
    Args:
        stats: 原始统计数据
        actual_minutes: 实际比赛时间(分钟)
        target_minutes: 目标标准时间(分钟)
        
    Returns:
        标准化后的统计数据
    """
    if actual_minutes <= 0:
        return stats
    
    normalized_stats = {}
    
    # 需要按时间比例调整的累积统计数据
    cumulative_stats = [
        'points', 'rebounds', 'offensive_rebounds', 'defensive_rebounds', 
        'assists', 'steals', 'blocks', 'turnovers', 'fouls',
        'fg_made', 'fg_attempted', 'fg2_made', 'fg2_attempted', 
        'fg3_made', 'fg3_attempted', 'ft_made', 'ft_attempted'
    ]
    
    # 不需要调整的比率统计数据
    ratio_stats = [
        'fg_pct', 'fg2_pct', 'fg3_pct', 'ft_pct', 
        'true_shooting_pct', 'effective_fg_pct', 'ast_to_ratio'
    ]
    
    # 标准化系数
    ratio = target_minutes / actual_minutes
    
    # 应用标准化
    for stat in cumulative_stats:
        if stat in stats:
            normalized_stats[stat] = stats[stat] * ratio
    
    # 保持比率不变
    for stat in ratio_stats:
        if stat in stats:
            normalized_stats[stat] = stats[stat]
    
    # 重新计算效率值
    normalized_stats['efficiency'] = (normalized_stats['points'] + normalized_stats['rebounds'] + 
                                  normalized_stats['assists'] + normalized_stats['steals'] + 
                                  normalized_stats['blocks']) - \
                                 ((normalized_stats['fg_attempted'] - normalized_stats['fg_made']) + 
                                  (normalized_stats['ft_attempted'] - normalized_stats['ft_made']) + 
                                  normalized_stats['turnovers'])
    
    return normalized_stats


def run_player_rating_example():
    """运行球员能力评分计算示例"""
    print("=" * 50)
    print("赛点篮球应用-生涯模块能力评分算法示例")
    print("=" * 50)
    
    try:
        # 示例数据池大小
        data_pool_size = 100
        
        # 初始化评分计算器
        calculator = PlayerRatingCalculator(data_pool_size)
        
        # 示例球员统计数据排名
        # 注：排名越小越好，1为最佳
        player_ranks = {
            'efficiency_rank': 20,
            'scoring_rank': 15,
            'true_shooting_rank': 18,
            'fg2_pct_rank': 25,
            'fg3_pct_rank': 30,
            'ft_pct_rank': 10,
            'offensive_reb_rank': 40,
            'defensive_reb_rank': 35,
            'assist_rank': 12,
            'ast_to_ratio_rank': 8,
            'steal_rank': 22,
            'block_rank': 45,
            'turnover_rank': 15,  # 失误少，排名靠前
            'foul_rank': 30       # 犯规少，排名靠前
        }
        
        # 球员场上位置
        position = Position.SF  # 小前锋
        
        print(f"\n计算场上位置为 {position.value} 的球员能力评分...")
        
        # 计算各维度评分
        efficiency_rating = calculator.calculate_efficiency_rating(player_ranks['efficiency_rank'])
        scoring_rating = calculator.calculate_scoring_rating(
            player_ranks['scoring_rank'],
            player_ranks['true_shooting_rank'],
            player_ranks['fg2_pct_rank'],
            player_ranks['fg3_pct_rank'],
            player_ranks['ft_pct_rank']
        )
        rebounding_rating = calculator.calculate_rebounding_rating(
            player_ranks['offensive_reb_rank'],
            player_ranks['defensive_reb_rank']
        )
        assisting_rating = calculator.calculate_assisting_rating(
            player_ranks['assist_rank'],
            player_ranks['ast_to_ratio_rank']
        )
        defense_rating = calculator.calculate_defense_rating(
            player_ranks['steal_rank'],
            player_ranks['block_rank'],
            position
        )
        turnover_rating = calculator.calculate_turnover_rating(player_ranks['turnover_rank'])
        foul_rating = calculator.calculate_foul_rating(player_ranks['foul_rank'])
        
        # 汇总各维度评分
        ratings = {
            RatingDimension.EFFICIENCY: efficiency_rating,
            RatingDimension.SCORING: scoring_rating,
            RatingDimension.REBOUNDING: rebounding_rating,
            RatingDimension.ASSISTING: assisting_rating,
            RatingDimension.DEFENSE: defense_rating,
            RatingDimension.TURNOVER: turnover_rating,
            RatingDimension.FOUL: foul_rating
        }
        
        # 计算真实能力值
        real_rating = calculator.calculate_real_ability_rating(ratings, position)
        
        # 计算各场上位置的匹配度
        position_fit = calculator.get_position_fit(ratings)
        
        # 计算新的展示能力值
        # 假设当前展示能力值为70，比赛胜利，赛季第10场比赛，连胜3场
        current_displayed_rating = 70
        is_win = True
        season_games = 10
        streak = 3
        
        new_displayed_rating = calculator.calculate_new_displayed_rating(
            real_rating, current_displayed_rating, is_win, season_games, streak
        )
        
        # 打印结果
        print("\n球员各维度评分:")
        for dimension, rating in ratings.items():
            print(f"{dimension.value}: {rating:.2f}")
        
        print(f"\n球员真实能力值: {real_rating:.2f}")
        
        print("\n球员各场上位置匹配度:")
        sorted_positions = sorted(position_fit.items(), key=lambda x: x[1], reverse=True)
        for position, fit in sorted_positions:
            print(f"{position.value}: {fit:.2f}%")
        
        print(f"\n当前展示能力值: {current_displayed_rating}")
        print(f"新的展示能力值: {new_displayed_rating:.2f}")
        print(f"能力值变化: {new_displayed_rating - current_displayed_rating:.2f}")
        
        # 测试数据标准化
        print("\n数据标准化示例:")
        # 模拟一场40分钟比赛的数据
        game_data = {
            'points': 18,
            'total_rebounds': 8,
            'offensive_rebounds': 3,
            'defensive_rebounds': 5,
            'assists': 6,
            'steals': 2,
            'blocks': 1,
            'turnovers': 3,
            'fouls': 2,
            'field_goals_made': 7,
            'field_goals_attempted': 15,
            'three_pointers_made': 2,
            'three_pointers_attempted': 5,
            'free_throws_made': 2,
            'free_throws_attempted': 3
        }
        
        # 计算统计数据
        stats = calculate_player_stats(game_data)
        
        # 标准化至100分钟
        normalized_stats = normalize_stats_by_time(stats, 40, 100)
        
        print("原始数据(40分钟):")
        print(f"得分: {stats['points']:.1f}, 篮板: {stats['rebounds']:.1f}, 助攻: {stats['assists']:.1f}")
        print(f"抢断: {stats['steals']:.1f}, 盖帽: {stats['blocks']:.1f}, 失误: {stats['turnovers']:.1f}")
        
        print("\n标准化数据(100分钟):")
        print(f"得分: {normalized_stats['points']:.1f}, 篮板: {normalized_stats['rebounds']:.1f}, 助攻: {normalized_stats['assists']:.1f}")
        print(f"抢断: {normalized_stats['steals']:.1f}, 盖帽: {normalized_stats['blocks']:.1f}, 失误: {normalized_stats['turnovers']:.1f}")
        
        print("\n示例运行完成!")
        
    except Exception as e:
        print(f"\n运行过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    run_player_rating_example() 