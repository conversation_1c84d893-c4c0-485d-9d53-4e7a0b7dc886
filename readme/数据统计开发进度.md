# 篮球数据统计功能开发计划与进度 (重构版)

## 总体目标

基于现有 `@basketballData` 代码进行重构，使其符合最新的模型 (`数据统计模型和数据流.md`)、组件设计 (`数据统计组件说明.md`) 和需求 (`数据统计需求.md`)。优先保证核心数据录入流程的稳定和准确，分阶段完善各项功能。

*文档参考说明：*
*   `[模型文档]` 指 `数据统计模型和数据流.md`
*   `[组件文档]` 指 `数据统计组件说明.md`
*   `[需求文档]` 指 `数据统计需求.md`

## Sprint 1: 核心模型、状态与API对齐 (预计 3-4 天)

**目标:** 更新核心的数据结构、状态管理和基础API调用，为后续组件开发或替换打下基础。

**状态:** (已完成)

**任务列表:**

*   [✓] **模型定义 - 更新类型:** 对比 `src/views/operation/basketballData/types.ts` 与 `[模型文档]` (1.1, 1.2)，更新或替换 `ActionTypeEnum`, `GameFlowTypeEnum`, `DataRecord`, `Player`, `Team`, `PlayerStats`, `TeamStats` 等核心接口/枚举，移除冗余或过时的类型。
*   [✓] **状态管理 - Pinia 实现/替换:**
    *   检查或创建 `src/views/operation/basketballData/store/basketballGame.ts` (或类似文件)。
    *   实现或替换 `Pinia store` 的 `state`，使其符合 `[模型文档]` (6. GameState) 结构。如果现有 store 不合适，直接创建新的。
    *   实现或调整 `actions` 和 `getters` 以适应新的 `state` 结构。
*   [✓] **API 定义 - 对齐:**
    *   检查 `src/views/operation/basketballData/api.ts`。
    *   确保存在或创建与 `[模型文档]` (4. API接口设计, 7. 请求和响应模型) 一致的API调用函数：`getGameData`, `saveDataRecord`, `updateDataRecord`, `deleteDataRecord`, `saveGameDataBulk`。重点关注 `saveDataRecord`，确保其接收新的 `DataRecord` 类型 (`[模型文档]` 1.2.2)。如果现有 API 定义混乱，可以直接重写。
*   [✓] **主页面 - 基础状态连接:**
    *   检查/调整 `GameDataEntry.vue` (`[组件文档]` 2.2.2)。
    *   确保能正确从路由获取 `gameId`。
    *   连接到符合新模型的 `Pinia store`，能读取和（初步）修改基础状态。
    *   在 `onMounted` 中调用正确的 `getGameData` API (`[模型文档]` 7. GetGameDataParams, GetGameDataResponse)，并将获取的数据填充到 `Pinia store`。
*   [✓] **数据流 - 状态更新 (犯规):** (可选) 如果 `ScoreBoard` 需要显示队犯规，在保存成功后更新 `Pinia` store (`[模型文档]` 6. GameState)。

**验收标准:**

*   `types.ts` 文件与 `[模型文档]` 中的核心模型定义一致。
*   存在一个 `Pinia store`，其结构和核心状态符合 `[模型文档]` (6. GameState) 定义。
*   `api.ts` 中包含必要的 API 函数定义，参数和返回类型（预期）与 `[模型文档]` (4, 7) 一致。
*   进入 `GameDataEntry.vue` 页面时，能从 API 加载基础比赛信息并更新到 `Pinia store`。

## Sprint 2: 基础组件与非得分操作实现 (预计 3-4 天)

**目标:** 实现或替换核心展示组件 (`ScoreBoard`, `TeamPlayerList`, `PlayerCard`)，并打通基础非得分统计操作（犯规、篮板等）的录入流程。

**状态:** (已完成)

**任务列表:**

*   [✓] **组件实现/替换 - ScoreBoard:**
    *   检查/创建 `components/ScoreBoard.vue` (`[组件文档]` 2.1.3)。
    *   确保其 props 符合文档定义，并从 `Pinia store` 获取数据正确显示。如果现有组件不适用，直接创建新的。
*   [✓] **组件实现/替换 - TeamPlayerList:**
    *   检查/创建 `components/TeamPlayerList.vue` (`[组件文档]` 2.1.2)。
    *   确保其 props (`team`, `activePlayers` 等) 符合文档定义，并能从 `Pinia store` 获取 `activePlayers` 正确渲染 `PlayerCard`。
*   [✓] **组件实现/替换 - PlayerCard (显示):**
    *   检查/创建 `components/PlayerCard.vue` (`[组件文档]` 2.1.1)。
    *   确保其 props (`player`, `isActive` 等) 符合文档定义，并显示基础信息。
*   [✓] **组件实现/调整 - PlayerCard (按钮):**
    *   实现/调整 `PlayerCard.vue` 上的按钮布局，参考 `[模型文档]` (1.1 操作类型分组) 和 `[组件文档]` (2.1.1, 2.5)，包含基础统计操作。
*   [✓] **组件实现/确保 - PlayerCard (事件):**
    *   实现/确保点击基础统计按钮时，`PlayerCard` 触发 `record-action` 事件，传递 `playerId` 和对应的 `ActionTypeEnum` 值 (`[组件文档]` 2.1.1 Emits)。
*   [✓] **数据流 - 事件处理:**
    *   在 `GameDataEntry.vue` (`[组件文档]` 2.2.2) 中实现/调整 `record-action` 事件的处理逻辑。
*   [✓] **数据流 - DataRecord构建:**
    *   根据接收到的 `playerId` 和 `actionType`，构建符合 `[模型文档]` (1.2.2 DataRecord) 结构的对象 (需要获取 `teamId` 等关联信息)。
*   [✓] **数据流 - 保存记录:**
    *   调用正确的 `saveDataRecord` API (`[模型文档]` 4. API接口设计) 函数发送 `DataRecord`。
*   [✓] **数据 - 保存换人记录:**
    *   调用正确的 `saveDataRecord` API (`[模型文档]` 4.) 保存换人记录。

**验收标准:**

*   `GameDataEntry.vue` 正确显示符合设计的 `ScoreBoard`, `TeamPlayerList` (基于 `activePlayers`)。
*   `PlayerCard` 显示球员信息和基础操作按钮。
*   点击基础操作按钮后，能构建符合 `DataRecord` 结构的对象，并调用 `saveDataRecord` API。

## Sprint 3: 球员选择与替换流程实现 (预计 3-4 天)

**目标:** 实现或替换球员选择和换人功能，使其符合新的交互模式和数据模型。

**状态:** (已完成)

**任务列表:**

*   [✓] **组件检查/创建/替换 - SubstituteDialog:**
    *   检查/创建/替换 `components/SubstituteDialog.vue` (`[组件文档]` 2.1.6)。如果 `GamePlayerSelect.vue` 不适用，直接创建新组件。
    *   实现/确保该组件支持 `select` 和 `substitute` 两种模式，props 和事件需符合文档定义。
*   [✓] **集成 - 选择上场球员:**
    *   在 `TeamPlayerList.vue` (`[组件文档]` 2.1.2) 中实现/确保 "选择上场球员" 按钮逻辑，点击打开 `SubstituteDialog` (select 模式)。
    *   处理 `SubstituteDialog` 返回的 `selectedPlayers` (`confirm-selection` 事件)，设置 `Pinia` store (`[模型文档]` 6. GameState home/guestActivePlayers)。
*   [✓] **集成 - 换人操作:**
    *   在 `PlayerCard.vue` (`[组件文档]` 2.1.1) 实现/确保 "换人" 按钮逻辑，点击打开 `SubstituteDialog` (substitute 模式)，传递 `outPlayer`。
    *   处理 `SubstituteDialog` 返回的 `inPlayer` (`confirm-substitute` 事件)，设置 `Pinia` store (`activePlayers`)。
*   [✓] **数据 - 换人记录构建:**
    *   换人成功后，构建 `actionType = SUBSTITUTION` 的 `DataRecord` (`[模型文档]` 1.2.2)，包含 `playerId` (换下) 和 `relatedPlayerId` (换上)，以及 `substituteTime`。

**验收标准:**

*   能够通过弹窗选择初始5名球员，结果反映在 `TeamPlayerList` 显示和 `Pinia` 状态中。
*   能够通过球员卡片上的换人按钮完成替换，界面和 `Pinia` 状态同步更新。
*   换人操作能生成符合 `DataRecord` 结构的记录并成功保存。

## Sprint 4: 得分操作实现 (无位置) (预计 2-3 天)

**状态:** (已完成)

**目标:** 实现或替换三分、两分、罚球操作的录入逻辑，确保使用 `isHit` 标志，并正确更新比分。

**任务列表:**

*   [✓] **组件实现/调整 - PlayerCard (得分按钮):**
    *   实现/调整 `PlayerCard.vue` (`[组件文档]` 2.1.1) 上的投篮/罚球按钮，按钮文本或交互方式明确区分命中与否（参考 `[模型文档]` 1.1 SHOT_ACTIONS, FREE_THROW_ACTIONS）。
*   [✓] **组件实现/确保 - PlayerCard (得分事件):**
    *   实现/确保点击按钮时，触发 `record-action` 事件 (`[组件文档]` 2.1.1 Emits)，传递 `playerId`, `actionType` (`[模型文档]` 1.1 ActionTypeEnum) 和 `isHit` (布尔值)。
*   [✓] **数据流 - 事件处理:**
    *   实现/调整 `GameDataEntry.vue` (`[组件文档]` 2.2.2) 中 `record-action` 处理逻辑，正确接收 `isHit`。
*   [✓] **数据流 - DataRecord构建:**
    *   构建包含 `isHit` 字段的 `DataRecord` 对象 (`[模型文档]` 1.2.2)。
*   [✓] **数据流 - 保存记录:**
    *   调用正确的 `saveDataRecord` API (`[模型文档]` 4.) 保存记录。
*   [✓] **数据流 - 得分更新:**
    *   根据 `actionType` 和 `isHit` 精确计算得分，设置/更新 `Pinia` (`[模型文档]` 6. GameState home/guestScore)。确保 `ScoreBoard` (`[组件文档]` 2.1.3) 响应更新。

**验收标准:**

*   点击区分命中与否的得分按钮后，能构建包含 `isHit` 的 `DataRecord` 并保存。
*   比分板 (`ScoreBoard`) 根据操作实时、准确地更新分数。

## Sprint 5: 投篮位置录入集成 (预计 2-3 天)

**状态:** (已完成)

**目标:** 集成投篮位置选择功能。

**任务列表:**

*   [✓] **组件检查/创建/替换 - CourtDialog:** (基本完成，待验证) 检查 `CourtDialog.vue`，确保其能正确显示、获取并返回位置坐标。
*   [✓] **集成 - 打开弹窗:** 实现 `GameDataEntry.vue` 中 `handleActionClick` 的逻辑：对于投篮动作，暂存信息（球员, 类型, 命中否）并打开 `CourtDialog`。
*   [✓] **集成 - 处理返回:** 在 `GameDataEntry.vue` 中实现 `handleCourtConfirm` (或类似名称) 函数，监听 `CourtDialog` 的 `confirm` 事件并接收 `position`。
*   [✓] **集成 - 构建与保存:** 在 `handleCourtConfirm` 中，组合暂存信息与返回的 `position`，构建完整 `DataRecord` 并调用 `saveDataRecord` 保存。

**验收标准:**

*   点击投篮按钮时弹出半场图对话框。
*   选择位置并确认后对话框关闭。
*   带有 `position` 信息的投篮记录被成功构建并保存。

## Sprint 6: 基础统计与节次控制实现 (预计 3-4 天)

**状态:** (已完成)

**目标:** 实现或替换底部统计表格和基础的节次控制功能。

**任务列表:**

*   [✓] **组件检查/创建/替换 - StatsTable:** 实现/验证 `StatsTable.vue` 内部渲染逻辑，确保能正确展示 `PlayerStats` 和 `TeamStats` 结构数据。
*   [✓] **UI - 节次控制:** (替代独立组件) 在 `GameDataEntry.vue` 顶部功能区 (`.function-bar`) 添加"下一节"按钮，并绑定到 `handlePeriodChange` 函数。
*   [✓] **数据 - 获取统计:** (依赖后端) 实现前端调用后端 API 获取 `PlayerStats[]` 和 `TeamStats[]` 的逻辑。 **(测试策略: 若后端未就绪，可在 `api.ts` 模拟返回示例统计数据)**。
*   [✓] **数据 - 集成统计显示:** (依赖上一任务) 确保 `GameDataEntry.vue` 能将获取到的真实或模拟统计数据传递给 `StatsTable` 并正确显示。
*   [✓] **功能 - 节次切换:** 在 `GameDataEntry.vue` 的 `handlePeriodChange` 中补充：
    *   [✓] 调用获取统计数据的逻辑刷新 `StatsTable` (依赖 Task 3)。
    *   [✓] 实现保存 `PERIOD_END` / `PERIOD_START` 类型 `DataRecord` 的逻辑 (调用 Store Action)。

**验收标准:**

*   页面底部表格能显示球员和球队统计数据（真实或模拟）。
*   页面顶部有"下一节"按钮。
*   点击"下一节"能推进节次，刷新统计（若有数据），清空场上球员，并保存流程记录。

## Sprint 7: 数据加载、缓存与记录管理实现 (预计 3-4 天)

**状态:** (已完成)

**目标:** 完善页面初始化加载、本地缓存机制，并实现或替换记录查看和删除功能。

**任务列表:**

*   [✓] **数据加载 - 完善:** (依赖后端) 确保 `getGameData` API 能返回真实的 `initialState` (含活跃球员、时钟等) 和 `records` 列表，并在前端正确恢复状态。**(测试策略: 若后端未就绪，可在 `api.ts` 模拟返回示例记录和状态)**。
*   [✓] **本地缓存 - 实现/确保:** 实现 `localStorage` 或 `IndexedDB` 缓存机制，包括状态保存、加载时检查、恢复选项等。
*   [✓] **记录管理 - 查看组件实现/替换:** 创建 `DataRecordTable.vue` 用于展示 `DataRecord` 列表。
*   [✓] **记录管理 - 查看功能集成:** 在 `GameDataEntry.vue` 或其他地方提供入口，允许用户查看操作记录列表。
*   [✓] **记录管理 - 删除功能:** (需 API 支持) 
    *   [✓] 在 `api.ts` 中实现（或模拟） `deleteDataRecord` 函数。
    *   [✓] 在记录列表 UI 中添加删除按钮及确认逻辑。
    *   [✓] 实现调用 `deleteDataRecord` API 并处理响应，更新本地记录和统计。

**验收标准:**

*   页面重载能恢复之前的完整状态和记录（依赖后端或完整缓存）。
*   本地缓存机制能有效工作并提供恢复选项。
*   可以查看格式正确的操作记录列表。
*   可以成功删除记录，并在界面和统计数据中得到反映。

## Sprint 8: 实现赛后录入模式切换与基础流程控制 (预计 4-5 天)

*   **目标:** 添加区分"实时录入"和"赛后录入"模式的功能，并提供适合赛后录入的基础流程控制（主要是节次管理和关键事件时间录入）。
*   **状态:** (已完成)
*   **任务列表:**
    *   [✓] **状态管理 - `entryMode`:**
        *   确保 `basketballGame.ts` store 中的 `entryMode` 状态可用。
        *   实现 Store Action `setEntryMode(mode: 'realtime' | 'post')`。
        *   更新 `loadGameData` (API 模拟或真实实现) 以便能根据 API 返回或默认值设置初始 `entryMode`。
        *   在 `GameDataEntry.vue` 顶部添加 UI 控件 (如 `el-radio-group`) 允许用户手动切换 `entryMode`。
    *   [✓] **组件创建 - `GameFlowControls.vue` (基础版):**
        *   创建组件文件。
        *   定义 Props: `currentSection`, `maxPeriods`, `entryMode`。
        *   定义 Emits: `start-period(time?: string)`, `end-period(time?: string)`, `record-pause(time?: string, reason?: string)`, `record-pause-end(time?: string)`。
        *   实现 UI: 
            *   包含"开始第 X 节"、"结束第 X 节"、"记录暂停"、"暂停结束"按钮。
            *   **在按钮旁添加可选的 `el-time-picker`，仅在 `entryMode === 'post'` 时显示，用于录入关键事件的实际时间。**
            *   暂时不实现实时计时器相关 UI。
    *   [✓] **组件修改 - `SubstituteDialog.vue`:**
        *   **添加可选的 `el-time-picker`，仅在 `mode === 'substitute'` 且 `entryMode === 'post'` 时显示，用于录入换人实际时间。**
        *   修改 `confirm-substitute` 事件，使其能传递可选的 `substituteTime: string`。
    *   [✓] **集成 - `GameDataEntry.vue`:**
        *   移除顶部的临时"下一节"按钮。
        *   引入并使用 `<GameFlowControls>` 组件，传递 props。
        *   实现事件处理函数 (`@start-period`, `@end-period` 等)，接收可选的 `time` 参数。
        *   在创建**关键流程事件** (`PERIOD_START`, `PERIOD_END`, `GAME_PAUSE`, `PAUSE_END`) 的 `DataRecord` 时，如果处于 'post' 模式且用户输入了时间，则使用该时间作为 `actionTime`；否则使用 `new Date().toISOString()`。
        *   修改 `handleSubstituteConfirm` 函数，接收可选的 `substituteTime` 参数。在创建**换人事件** (`SUBSTITUTION`) 的 `DataRecord` 时，如果处于 'post' 模式且用户输入了时间，则使用该时间作为 `substituteTime` (或 `actionTime`)；否则使用 `new Date().toISOString()`。
        *   **确保 `handleActionClick` 和 `handleCourtConfirm` 中的 `actionTime` 始终使用 `new Date().toISOString()`，不受 `entryMode` 影响。**
    *   [✓] **UI 适配 - `GameFlowControls.vue`:** 确保按钮文本等在 'post' 模式下语义清晰 (例如, "标记XX时间")。
    *   [✓] **文档更新 (进行中):** 随时根据开发调整更新相关文档。

---

## 后续 Sprint 计划 (优先级待定)


*   **Sprint 9: 实现历史节次数据回溯与修改功能:**
    *   **目标:** 允许用户切换到之前的比赛节次，查看该节次状态，并能修改或补充/插入该节次的数据记录，保证事件逻辑顺序和操作权限。
    *   **状态:** (进行中/根据实际情况填写)
    *   **核心实现:**
        *   [ ] **状态模式引入:** 创建 `sectionStates.ts`，定义 `Waiting`, `InProgress`, `Completed` 状态类及其行为和权限检查方法。
        *   [ ] **GameDataEntry 作为上下文:** 在 `GameDataEntry.vue` 中计算 `currentSectionState`，并调用状态对象的方法进行权限检查，替换原有分散的 `if` 判断。
        *   [ ] **节次切换逻辑 (`handleChangeSection`) 完善:**
            *   区分实时/赛后模式，限制实时模式下从 `InProgress` 切换。
            *   实现切换到新节次（向前）时清空球员并触发选择框的逻辑。
            *   实现切换到历史节次时恢复球员状态的逻辑。
        *   [ ] **赛后模式时间强制:** 在 `GameFlowControls.vue` 中为关键流程和换人操作添加时间选择器，并修改事件触发逻辑，确保赛后模式下时间必填。
        *   [ ] **UI 按钮状态:** `GameFlowControls.vue` 中的按钮（开始、结束、暂停等）根据当前节次的 `sectionStatus` 正确启用/禁用。
        *   [ ] **插入逻辑修正:** `basketballGame.ts` Store 中的 `addDataRecord` 使用 `splice` 插入记录，确保顺序正确。
        *   [ ] **记录筛选:** 恢复 `DataRecordTable.vue` 的按节次筛选功能。
    *   **待办/验证:**
        *   [ ] 验证 `Waiting` 状态下是否已禁止所有数据操作（包括插入）。
        *   [ ] 验证 `InProgress` 状态下是否已禁止插入操作。
        *   [ ] 验证 `Completed` 状态下是否只允许插入操作。
        *   [ ] 验证节次切换（特别是赛后模式和切换到新节次）的流程是否完全符合预期。
        *   [ ] 彻底解决 `GameFlowControls` 的 `isPeriodInProgress` linter 报错问题（可能需清理缓存或IDE重启）。
        *   [ ] (可选) 将状态检查逻辑传递给 `TeamPlayerList` 和 `PlayerCard` 以禁用其内部按钮。
        *   [ ] UI: 添加记录编辑按钮 (`DataRecordTable`)。
        *   [ ] UI: 添加记录插入按钮/入口 (`DataRecordTable`)。
        *   [ ] 组件: 实现记录编辑对话框 (`RecordEditDialog` 或复用现有)。
        *   [ ] API: 确保 `updateDataRecord` API 可用。
        *   [ ] 后端: 确保所有依赖顺序的计算 (如上场时间) 使用 `section` + `sequenceNumber` 排序。
*   **上场时间与效率值:** 对接后端计算结果或实现前端计算 (需要 `PlayingTimeChart`, `PlayerEfficiencyChart`)。
*   **高级统计与筛选:** `StatsTable` 支持更多维度的切换、排序、筛选。
*   **投篮热图:** 实现 `ShotHeatMap`。
*   **API 对接:** 将所有模拟 API 替换为真实后端接口。
*   **错误处理与用户提示:** 完善边界情况处理和用户反馈。
*   **UI/UX 优化:** 根据使用反馈进行界面和交互优化。
*   **撤销/重做功能:** (可选) 添加操作的撤销/重做支持。
*   **Sprint X: 实现实时模式下的高级流程控制:** 为 `GameFlowControls` 添加实时计时、详细暂停处理等功能。
---

**进度跟踪:**

*   **Sprint 1:** (已完成)
*   **Sprint 2:** (已完成)
*   **Sprint 3:** (已完成)
*   **Sprint 4:** (已完成)
*   **Sprint 5:** (已完成)
*   **Sprint 6:** (已完成)
*   **Sprint 7:** (已完成)
*   **Sprint 8:** (已完成)
*   **Sprint 9:** (进行中)

*(后续在此处更新各 Sprint 的实际完成状态)*

---

## 额外完成/相关工作

*   **(Sprint 8)** **模式切换与流程控制调试:**
    *   修复了 `handleActionClick` 事件参数处理错误，解决了按钮点击无效的问题。
    *   修复了 `DataRecordTable` 中因 `position` 为空导致 `toFixed` 报错的问题。
    *   修复了 `loading` 状态绑定错误，解决了页面持续加载的问题。
    *   修复了默认录入模式因缓存恢复逻辑不完善导致未能默认为"赛后录入"的问题。
    *   多次迭代修复了节次切换逻辑 (`nextPeriod` 计算和 `handleStartPeriod`/`handleEndPeriod` 处理)，解决了无法进入下一节的 Bug。
    *   修复了"开始节次"时未能按预期清空并提示选择球员的问题。
    *   修复了流程事件（如节次开始）成功提示显示为数字而非中文的问题。
    *   通过修改 API Mock 实现，解决了清除缓存后仍加载到固定历史记录的问题。
    *   将 `ScoreBoard` 组件彻底修改为纯展示组件，移除了内部交互逻辑。

在完成 Sprint 1-7 及修复相关问题的过程中，额外完成了：
*   **大量 Bug 修复:** (Sprint 1-4) 解决了由于重构引入的多种 Bug，包括 API 函数缺失、Prop 类型错误、`v-model` 误用、组件渲染问题等。
*   **术语统一:** (Sprint 1-4) 将 `api.ts` 中的 "Match" 相关命名统一修改为 "Game"。
*   **代码清理与 Linter 错误修复:** (Sprint 1-4) 移除了冗余代码，修复了多处 Linter 错误。
*   **API 模拟实现:** (Sprint 1-4) 为 `saveDataRecord` 和 `saveGameDataBulk` 添加了模拟实现。
*   **修复 Sprint 3 遗留问题:** (Sprint 5) 解决了换人流程中 `outPlayer` Prop 传递失败的问题。
*   **组件健壮性:** (Sprint 5) 修复了 `CourtDialog` 和 `SubstituteDialog` 中的多处 Bug。
*   **API 调用修复:** (Sprint 5) 解决了比赛列表页加载失败问题。
*   **临时移除 Table 组件:** (Sprint 5) 暂时移除了 `BasicTable` 的使用。
*   **Bug 修复:** (Sprint 6-7) 解决了 Store Action 调用失败、`StatsTable` 初始数据显示异常、Vue Router 跳转、球员卡片选择后无响应、下一节未提示选择球员、添加球员按钮无效等多项问题。
*   **功能增强:** (Sprint 6-7) 为新节次选择的球员自动记录"换上"事件。
*   **UI 优化:** (Sprint 6-7) 修正了操作成功提示信息、移除了不必要的滚动条、将操作记录集成到统计表格的标签页中。
*   **代码质量:** (Sprint 6-7) 修复了多个组件中的 Linter 错误，优化了事件参数传递。

*   **(通用)** **API 对接:** 在后续阶段，需要将所有 API 的模拟实现替换为对真实后端接口的调用。
