# 赛点篮球 - 移动应用原型

这是一个基于HTML、CSS和JavaScript开发的篮球领域移动应用原型设计。该原型展示了一个完整的篮球社交和比赛管理平台的用户界面和交互逻辑。

## 项目特点

- 高保真UI设计，符合iOS/Android设计规范
- 使用TailwindCSS和FontAwesome实现现代化界面
- 完整的页面交互和导航系统
- 响应式设计，适配不同设备尺寸

## 主要功能模块

- **首页**：展示活动广告、功能入口、赛程和排行榜等内容
- **赛程**：查看历史和未来的比赛安排
- **排行榜**：查看球员和球队的数据排名
- **比赛**：展示不同类型的比赛活动
- **球队**：管理球队或查找加入新球队
- **球队详情**：查看球队信息、球员名单和数据统计
- **生涯**：展示个人数据、能力值和比赛记录
- **我的**：账号管理和个人设置

## 技术栈

- HTML5
- CSS3 (TailwindCSS)
- JavaScript
- FontAwesome 图标库
- 真实UI图片 (来自Unsplash)

## 如何使用

1. 克隆或下载本仓库
2. 打开`index.html`文件查看所有界面的预览
3. 点击各个界面可以查看详细设计和交互

## 设计亮点

- **球队模块改进**：
  - 将左右箭头切换改为下拉选择方式，更符合用户习惯
  - 优化功能按钮布局，减小尺寸并移至右上角
  - 扩大球员信息卡片，增加展示数据，提升可读性
  - 采用卡片式设计，每个球员都有独立的信息区块

## 项目结构

```
├── index.html          # 主入口文件
├── css/
│   └── styles.css      # 全局样式文件
├── js/
│   └── main.js         # 主要JavaScript文件
├── img/                # 图片资源目录
└── pages/              # 页面文件目录
    ├── home.html       # 首页
    ├── schedule.html   # 赛程页
    ├── ranking.html    # 排行榜页
    ├── match.html      # 比赛页
    ├── team.html       # 球队页
    ├── team_detail.html # 球队详情页
    ├── career.html     # 生涯页
    └── profile.html    # 我的页面
```

## 设计参考

- NBA官方应用
- 虎扑应用
- iOS/Android设计规范

## 未来计划

- 添加更多交互动效
- 完善数据可视化展示
- 增加更多社交功能
- 优化移动端性能

## 贡献

欢迎提出建议和改进意见，可以通过Issue或Pull Request方式贡献。

## 许可

本项目采用MIT许可证。 