-- 修复 sd_player_career_stats 表缺失字段
-- 基于 PlayerCareerStatsDO 类，添加所有缺失的字段

-- 首先检查当前表结构
-- DESCRIBE sd_player_career_stats;

-- 添加缺失的基础统计总计字段（如果不存在）
ALTER TABLE sd_player_career_stats 
ADD COLUMN IF NOT EXISTS total_two_points_made INT DEFAULT 0 COMMENT '总二分命中数',
ADD COLUMN IF NOT EXISTS total_two_points_attempted INT DEFAULT 0 COMMENT '总二分出手数', 
ADD COLUMN IF NOT EXISTS total_minutes_played DECIMAL(8,2) DEFAULT 0 COMMENT '总出场时间(分钟)';

-- 添加缺失的场均数据字段
ALTER TABLE sd_player_career_stats
ADD COLUMN IF NOT EXISTS avg_minutes_played DECIMAL(5,2) DEFAULT 0 COMMENT '场均出场时间',
ADD COLUMN IF NOT EXISTS avg_efficiency DECIMAL(5,2) DEFAULT 0 COMMENT '场均效率值';

-- 添加缺失的命中率字段
ALTER TABLE sd_player_career_stats
ADD COLUMN IF NOT EXISTS two_point_percentage DECIMAL(5,2) DEFAULT 0 COMMENT '二分命中率';

-- 添加缺失的高阶统计字段
ALTER TABLE sd_player_career_stats
ADD COLUMN IF NOT EXISTS effective_field_goal_percentage DECIMAL(5,2) DEFAULT 0 COMMENT '有效投篮命中率',
ADD COLUMN IF NOT EXISTS offensive_rebound_rate DECIMAL(5,2) DEFAULT 0 COMMENT '进攻篮板率',
ADD COLUMN IF NOT EXISTS defensive_rebound_rate DECIMAL(5,2) DEFAULT 0 COMMENT '防守篮板率',
ADD COLUMN IF NOT EXISTS assist_turnover_ratio DECIMAL(5,2) DEFAULT 0 COMMENT '助攻失误比',
ADD COLUMN IF NOT EXISTS player_efficiency_rating DECIMAL(5,2) DEFAULT 0 COMMENT '球员效率指数(PER)',
ADD COLUMN IF NOT EXISTS usage_rate DECIMAL(5,2) DEFAULT 0 COMMENT '使用率',
ADD COLUMN IF NOT EXISTS plus_minus DECIMAL(5,2) DEFAULT 0 COMMENT '净胜分';

-- 检查表结构
DESCRIBE sd_player_career_stats;