-- 赛点篮球球员生涯模块完整数据表结构
-- 基于设计文档和代码实际需求

-- ================================
-- 1. 赛季管理表 (sd_season) - 已存在，无需创建
-- ================================
-- 注意：sd_season表已投产使用，结构如下：
/*
CREATE TABLE `sd_season` (
    `id` BIGINT(19) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `season_name` VARCHAR(50) NOT NULL COMMENT '赛季名称 如:2024春季赛季',
    `start_date` DATE NOT NULL COMMENT '赛季开始日期',
    `end_date` DATE NOT NULL COMMENT '赛季结束日期',
    `season_type` TINYINT(3) NOT NULL DEFAULT '1' COMMENT '赛季类型:1-春季,2-夏季,3-秋季,4-冬季',
    `status` TINYINT(3) NOT NULL DEFAULT '1' COMMENT '赛季状态:1-未开始,2-进行中,3-已结束',
    `is_current` TINYINT(3) NOT NULL DEFAULT '0' COMMENT '是否当前赛季:0-否,1-是',
    `description` VARCHAR(500) NULL DEFAULT NULL COMMENT '赛季描述',
    `total_games` INT(10) NULL DEFAULT '0' COMMENT '本赛季总比赛场次',
    `total_players` INT(10) NULL DEFAULT '0' COMMENT '本赛季参与球员数',
    `creator` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='赛季管理表';
*/

-- sd_season表已存在，直接使用现有表结构

-- ================================
-- 2. 球员生涯聚合统计表 (sd_player_career_stats)
-- ================================
CREATE TABLE sd_player_career_stats (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    player_id BIGINT NOT NULL COMMENT '球员ID',
    game_type TINYINT NOT NULL DEFAULT 0 COMMENT '比赛类型:0-全部,1-排位赛,2-友谊赛,3-联赛',
    stat_scope VARCHAR(20) DEFAULT 'career' COMMENT '统计范围:career-生涯,season-赛季',
    
    -- 生涯特有字段
    total_seasons INT DEFAULT 0 COMMENT '参赛总赛季数',
    first_game_date DATE COMMENT '生涯首场比赛日期',
    latest_game_date DATE COMMENT '生涯最近一场比赛日期',
    
    -- 基础统计 - 总计数据
    games_played INT DEFAULT 0 COMMENT '参赛场次',
    wins INT DEFAULT 0 COMMENT '胜场数',
    losses INT DEFAULT 0 COMMENT '负场数',
    win_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '胜率(%)',
    
    total_points INT DEFAULT 0 COMMENT '总得分',
    avg_points DECIMAL(5,2) DEFAULT 0.00 COMMENT '场均得分',
    
    total_rebounds INT DEFAULT 0 COMMENT '总篮板',
    avg_rebounds DECIMAL(5,2) DEFAULT 0.00 COMMENT '场均篮板',
    
    total_assists INT DEFAULT 0 COMMENT '总助攻',
    avg_assists DECIMAL(5,2) DEFAULT 0.00 COMMENT '场均助攻',
    
    total_steals INT DEFAULT 0 COMMENT '总抢断',
    avg_steals DECIMAL(5,2) DEFAULT 0.00 COMMENT '场均抢断',
    
    total_blocks INT DEFAULT 0 COMMENT '总盖帽',
    avg_blocks DECIMAL(5,2) DEFAULT 0.00 COMMENT '场均盖帽',
    
    total_turnovers INT DEFAULT 0 COMMENT '总失误',
    avg_turnovers DECIMAL(5,2) DEFAULT 0.00 COMMENT '场均失误',
    
    total_fouls INT DEFAULT 0 COMMENT '总犯规',
    avg_fouls DECIMAL(5,2) DEFAULT 0.00 COMMENT '场均犯规',
    
    total_playing_time INT DEFAULT 0 COMMENT '总上场时间(秒)',
    avg_playing_time DECIMAL(5,2) DEFAULT 0.00 COMMENT '场均上场时间(分钟)',
    
    -- 投篮统计
    total_field_goals_attempted INT DEFAULT 0 COMMENT '总投篮出手',
    total_field_goals_made INT DEFAULT 0 COMMENT '总投篮命中',
    field_goal_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT '投篮命中率(%)',
    
    total_three_points_attempted INT DEFAULT 0 COMMENT '总三分出手',
    total_three_points_made INT DEFAULT 0 COMMENT '总三分命中',
    three_point_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT '三分命中率(%)',
    
    total_two_points_attempted INT DEFAULT 0 COMMENT '总二分出手',
    total_two_points_made INT DEFAULT 0 COMMENT '总二分命中',
    two_point_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT '二分命中率(%)',
    
    total_free_throws_attempted INT DEFAULT 0 COMMENT '总罚球出手',
    total_free_throws_made INT DEFAULT 0 COMMENT '总罚球命中',
    free_throw_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT '罚球命中率(%)',
    
    -- 篮板细分
    total_offensive_rebounds INT DEFAULT 0 COMMENT '总前场篮板',
    avg_offensive_rebounds DECIMAL(5,2) DEFAULT 0.00 COMMENT '场均前场篮板',
    
    total_defensive_rebounds INT DEFAULT 0 COMMENT '总后场篮板',
    avg_defensive_rebounds DECIMAL(5,2) DEFAULT 0.00 COMMENT '场均后场篮板',
    
    -- 高阶数据统计
    efficiency_rating DECIMAL(5,2) DEFAULT 0.00 COMMENT '效率值',
    true_shooting_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT '真实命中率(%)',
    player_efficiency_rating DECIMAL(5,2) DEFAULT 0.00 COMMENT '球员效率指数',
    usage_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '使用率(%)',
    assist_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '助攻率(%)',
    turnover_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '失误率(%)',
    steal_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '抢断率(%)',
    block_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '盖帽率(%)',
    
    -- 连胜统计
    current_streak INT DEFAULT 0 COMMENT '当前连胜数(负数表示连败)',
    max_win_streak INT DEFAULT 0 COMMENT '最大连胜数',
    max_loss_streak INT DEFAULT 0 COMMENT '最大连败数',
    
    -- 特殊统计
    double_doubles INT DEFAULT 0 COMMENT '两双次数',
    triple_doubles INT DEFAULT 0 COMMENT '三双次数',
    near_triple_doubles INT DEFAULT 0 COMMENT '准三双次数',
    
    -- 单场最佳记录
    best_points INT DEFAULT 0 COMMENT '单场最高得分',
    best_rebounds INT DEFAULT 0 COMMENT '单场最高篮板',
    best_assists INT DEFAULT 0 COMMENT '单场最高助攻',
    best_steals INT DEFAULT 0 COMMENT '单场最高抢断',
    best_blocks INT DEFAULT 0 COMMENT '单场最高盖帽',
    
    -- 最佳记录对应的比赛ID
    best_points_game_id BIGINT COMMENT '最高得分对应比赛ID',
    best_rebounds_game_id BIGINT COMMENT '最高篮板对应比赛ID',
    best_assists_game_id BIGINT COMMENT '最高助攻对应比赛ID',
    best_steals_game_id BIGINT COMMENT '最高抢断对应比赛ID',
    best_blocks_game_id BIGINT COMMENT '最高盖帽对应比赛ID',
    
    -- 排名信息
    points_rank INT COMMENT '得分排名',
    rebounds_rank INT COMMENT '篮板排名',
    assists_rank INT COMMENT '助攻排名',
    efficiency_rank INT COMMENT '效率值排名',
    overall_rank INT COMMENT '综合排名',
    
    -- 系统字段
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    
    PRIMARY KEY (id),
    UNIQUE KEY uk_player_game_type (player_id, game_type, deleted),
    KEY idx_career_avg_points (avg_points DESC),
    KEY idx_career_win_rate (win_rate DESC),
    KEY idx_career_games_played (games_played DESC),
    KEY idx_career_efficiency (efficiency_rating DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='球员生涯聚合统计表';

-- ================================
-- 3. 球员赛季统计表 (sd_player_season_stats)
-- ================================
CREATE TABLE sd_player_season_stats (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    player_id BIGINT NOT NULL COMMENT '球员ID',
    season_id BIGINT NOT NULL COMMENT '赛季ID',
    game_type TINYINT NOT NULL DEFAULT 0 COMMENT '比赛类型:0-全部,1-排位赛,2-友谊赛,3-联赛',
    stat_scope VARCHAR(20) DEFAULT 'season' COMMENT '统计范围',
    
    -- 基础统计
    games_played INT DEFAULT 0 COMMENT '参赛场次',
    wins INT DEFAULT 0 COMMENT '胜场数',
    losses INT DEFAULT 0 COMMENT '负场数',
    win_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '胜率(%)',
    
    total_points INT DEFAULT 0 COMMENT '总得分',
    avg_points DECIMAL(5,2) DEFAULT 0.00 COMMENT '场均得分',
    
    total_rebounds INT DEFAULT 0 COMMENT '总篮板',
    avg_rebounds DECIMAL(5,2) DEFAULT 0.00 COMMENT '场均篮板',
    
    total_assists INT DEFAULT 0 COMMENT '总助攻',
    avg_assists DECIMAL(5,2) DEFAULT 0.00 COMMENT '场均助攻',
    
    total_steals INT DEFAULT 0 COMMENT '总抢断',
    avg_steals DECIMAL(5,2) DEFAULT 0.00 COMMENT '场均抢断',
    
    total_blocks INT DEFAULT 0 COMMENT '总盖帽',
    avg_blocks DECIMAL(5,2) DEFAULT 0.00 COMMENT '场均盖帽',
    
    total_turnovers INT DEFAULT 0 COMMENT '总失误',
    avg_turnovers DECIMAL(5,2) DEFAULT 0.00 COMMENT '场均失误',
    
    total_fouls INT DEFAULT 0 COMMENT '总犯规',
    avg_fouls DECIMAL(5,2) DEFAULT 0.00 COMMENT '场均犯规',
    
    total_playing_time INT DEFAULT 0 COMMENT '总上场时间(秒)',
    avg_playing_time DECIMAL(5,2) DEFAULT 0.00 COMMENT '场均上场时间(分钟)',
    
    -- 投篮统计
    total_field_goals_attempted INT DEFAULT 0 COMMENT '总投篮出手',
    total_field_goals_made INT DEFAULT 0 COMMENT '总投篮命中',
    field_goal_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT '投篮命中率(%)',
    
    total_three_points_attempted INT DEFAULT 0 COMMENT '总三分出手',
    total_three_points_made INT DEFAULT 0 COMMENT '总三分命中',
    three_point_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT '三分命中率(%)',
    
    total_two_points_attempted INT DEFAULT 0 COMMENT '总二分出手',
    total_two_points_made INT DEFAULT 0 COMMENT '总二分命中',
    two_point_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT '二分命中率(%)',
    
    total_free_throws_attempted INT DEFAULT 0 COMMENT '总罚球出手',
    total_free_throws_made INT DEFAULT 0 COMMENT '总罚球命中',
    free_throw_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT '罚球命中率(%)',
    
    -- 篮板细分
    total_offensive_rebounds INT DEFAULT 0 COMMENT '总前场篮板',
    avg_offensive_rebounds DECIMAL(5,2) DEFAULT 0.00 COMMENT '场均前场篮板',
    
    total_defensive_rebounds INT DEFAULT 0 COMMENT '总后场篮板',
    avg_defensive_rebounds DECIMAL(5,2) DEFAULT 0.00 COMMENT '场均后场篮板',
    
    -- 高阶数据统计
    efficiency_rating DECIMAL(5,2) DEFAULT 0.00 COMMENT '效率值',
    true_shooting_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT '真实命中率(%)',
    player_efficiency_rating DECIMAL(5,2) DEFAULT 0.00 COMMENT '球员效率指数',
    usage_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '使用率(%)',
    assist_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '助攻率(%)',
    turnover_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '失误率(%)',
    steal_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '抢断率(%)',
    block_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '盖帽率(%)',
    
    -- 连胜统计
    current_streak INT DEFAULT 0 COMMENT '当前连胜数',
    max_win_streak INT DEFAULT 0 COMMENT '最大连胜数',
    max_loss_streak INT DEFAULT 0 COMMENT '最大连败数',
    
    -- 特殊统计
    double_doubles INT DEFAULT 0 COMMENT '两双次数',
    triple_doubles INT DEFAULT 0 COMMENT '三双次数',  
    near_triple_doubles INT DEFAULT 0 COMMENT '准三双次数',
    
    -- 单场最佳记录
    best_points INT DEFAULT 0 COMMENT '单场最高得分',
    best_rebounds INT DEFAULT 0 COMMENT '单场最高篮板', 
    best_assists INT DEFAULT 0 COMMENT '单场最高助攻',
    best_steals INT DEFAULT 0 COMMENT '单场最高抢断',
    best_blocks INT DEFAULT 0 COMMENT '单场最高盖帽',
    
    -- 最佳记录对应的比赛ID
    best_points_game_id BIGINT COMMENT '最高得分对应比赛ID',
    best_rebounds_game_id BIGINT COMMENT '最高篮板对应比赛ID',
    best_assists_game_id BIGINT COMMENT '最高助攻对应比赛ID',
    best_steals_game_id BIGINT COMMENT '最高抢断对应比赛ID',
    best_blocks_game_id BIGINT COMMENT '最高盖帽对应比赛ID',
    
    -- 排名信息
    points_rank INT COMMENT '得分排名',
    rebounds_rank INT COMMENT '篮板排名',
    assists_rank INT COMMENT '助攻排名',
    efficiency_rank INT COMMENT '效率值排名',
    overall_rank INT COMMENT '综合排名',
    
    -- 系统字段
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    
    PRIMARY KEY (id),
    UNIQUE KEY uk_player_season_game_type (player_id, season_id, game_type, deleted),
    KEY idx_season_stats_composite (season_id, avg_points DESC, avg_rebounds DESC),
    KEY idx_season_player (player_id),
    KEY idx_season_efficiency (efficiency_rating DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='球员赛季统计表';

-- ================================
-- 4. 球员生涯最佳数据记录表 (sd_player_career_best_stats)
-- ================================
CREATE TABLE sd_player_career_best_stats (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    player_id BIGINT NOT NULL COMMENT '球员ID',
    game_type TINYINT NOT NULL DEFAULT 0 COMMENT '比赛类型:0-全部,1-排位赛,2-友谊赛,3-联赛',
    
    -- 最佳基础数据
    best_points INT DEFAULT 0 COMMENT '单场最高得分',
    best_points_game_id BIGINT COMMENT '最高得分对应比赛ID',
    best_points_date DATE COMMENT '最高得分比赛日期',
    
    best_rebounds INT DEFAULT 0 COMMENT '单场最高篮板',
    best_rebounds_game_id BIGINT COMMENT '最高篮板对应比赛ID',
    best_rebounds_date DATE COMMENT '最高篮板比赛日期',
    
    best_assists INT DEFAULT 0 COMMENT '单场最高助攻',
    best_assists_game_id BIGINT COMMENT '最高助攻对应比赛ID',
    best_assists_date DATE COMMENT '最高助攻比赛日期',
    
    best_steals INT DEFAULT 0 COMMENT '单场最高抢断',
    best_steals_game_id BIGINT COMMENT '最高抢断对应比赛ID',
    best_steals_date DATE COMMENT '最高抢断比赛日期',
    
    best_blocks INT DEFAULT 0 COMMENT '单场最高盖帽',
    best_blocks_game_id BIGINT COMMENT '最高盖帽对应比赛ID',
    best_blocks_date DATE COMMENT '最高盖帽比赛日期',
    
    best_efficiency DECIMAL(5,2) DEFAULT 0.00 COMMENT '单场最高效率值',
    best_efficiency_game_id BIGINT COMMENT '最高效率值对应比赛ID',
    best_efficiency_date DATE COMMENT '最高效率值比赛日期',
    
    -- 系统字段
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    
    PRIMARY KEY (id),
    UNIQUE KEY uk_best_player_game_type (player_id, game_type, deleted),
    KEY idx_best_points (best_points DESC),
    KEY idx_best_efficiency (best_efficiency DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='球员生涯最佳数据记录表';

-- ================================
-- 5. 球员7维度能力评分表 (sd_player_career_ability_scores)
-- ================================
CREATE TABLE sd_player_career_ability_scores (
    id BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    player_id BIGINT NOT NULL COMMENT '球员ID',
    game_type TINYINT NOT NULL DEFAULT 0 COMMENT '比赛类型:0-全部,1-排位赛,2-友谊赛,3-联赛',
    
    -- 7维度能力评分 (0-100分)
    efficiency_rating DECIMAL(5,2) DEFAULT 60.00 COMMENT '效率能力评分',
    scoring_rating DECIMAL(5,2) DEFAULT 60.00 COMMENT '得分能力评分',
    rebounding_rating DECIMAL(5,2) DEFAULT 60.00 COMMENT '篮板能力评分',
    assist_rating DECIMAL(5,2) DEFAULT 60.00 COMMENT '助攻能力评分',
    defense_rating DECIMAL(5,2) DEFAULT 60.00 COMMENT '防守能力评分',
    turnover_rating DECIMAL(5,2) DEFAULT 60.00 COMMENT '失误控制评分',
    foul_rating DECIMAL(5,2) DEFAULT 60.00 COMMENT '犯规控制评分',
    
    -- 综合评分
    overall_rating DECIMAL(5,2) DEFAULT 60.00 COMMENT '综合能力评分',
    display_ability_rating DECIMAL(5,2) DEFAULT 60.00 COMMENT '展示能力评分',
    
    -- 评分更新时间
    last_calculated_date DATE COMMENT '最后计算日期',
    calculation_games_count INT DEFAULT 0 COMMENT '计算时的比赛场次',
    
    -- 系统字段
    creator VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
    
    PRIMARY KEY (id),
    UNIQUE KEY uk_ability_player_game_type (player_id, game_type, deleted),
    KEY idx_ability_overall (overall_rating DESC),
    KEY idx_ability_efficiency (efficiency_rating DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='球员7维度能力评分表';

-- ================================
-- 6. 索引优化
-- ================================

-- 球员基础表索引优化（假设已存在，如需要可补充）
-- ALTER TABLE sd_player ADD INDEX idx_player_member_user_id (member_user_id);
-- ALTER TABLE sd_player ADD INDEX idx_player_ratings (ratings DESC);
-- ALTER TABLE sd_player ADD INDEX idx_player_position (position);

-- 比赛表关联索引优化（假设已存在，如需要可补充）  
-- ALTER TABLE sd_game ADD INDEX idx_game_season (season_id);
-- ALTER TABLE sd_game ADD INDEX idx_game_start_time (start_time);

-- 球员统计表索引优化（针对第0节数据查询）
-- ALTER TABLE sd_player_statistics ADD INDEX idx_player_stats_game_section (game_id, section);
-- ALTER TABLE sd_player_statistics ADD INDEX idx_player_stats_player_section (player_id, section);

-- ================================
-- 7. 触发器（可选，用于数据同步）
-- ================================

-- 示例：球员生涯数据更新时同步到主表的触发器
-- DELIMITER $$
-- CREATE TRIGGER tr_career_stats_update 
-- AFTER UPDATE ON sd_player_career_stats
-- FOR EACH ROW
-- BEGIN
--     -- 更新球员主表的冗余字段
--     UPDATE sd_player 
--     SET 
--         current_streak = NEW.current_streak,
--         career_max_win_streak = NEW.max_win_streak,
--         career_games = NEW.games_played,
--         career_win_rate = NEW.win_rate,
--         current_overall_rating = NEW.efficiency_rating
--     WHERE id = NEW.player_id;
-- END$$
-- DELIMITER ;

-- ================================
-- 8. 初始化数据示例
-- ================================

-- sd_season表已存在，无需插入初始化数据

-- ================================
-- 9. 数据完整性约束补充
-- ================================

-- 外键约束（可根据实际需要启用）
-- ALTER TABLE sd_player_career_stats ADD CONSTRAINT fk_career_stats_player 
--     FOREIGN KEY (player_id) REFERENCES sd_player(id) ON DELETE CASCADE;

-- ALTER TABLE sd_player_season_stats ADD CONSTRAINT fk_season_stats_player 
--     FOREIGN KEY (player_id) REFERENCES sd_player(id) ON DELETE CASCADE;
    
-- ALTER TABLE sd_player_season_stats ADD CONSTRAINT fk_season_stats_season 
--     FOREIGN KEY (season_id) REFERENCES sd_season(id) ON DELETE CASCADE;

-- ALTER TABLE sd_player_career_best_stats ADD CONSTRAINT fk_best_stats_player 
--     FOREIGN KEY (player_id) REFERENCES sd_player(id) ON DELETE CASCADE;

-- ALTER TABLE sd_player_career_ability_scores ADD CONSTRAINT fk_ability_scores_player 
--     FOREIGN KEY (player_id) REFERENCES sd_player(id) ON DELETE CASCADE;

-- ================================
-- 10. 视图创建（可选，便于查询）
-- ================================

-- 球员综合数据视图
-- CREATE VIEW v_player_comprehensive_stats AS
-- SELECT 
--     p.id as player_id,
--     p.name as player_name,
--     p.avatar,
--     p.position,
--     p.ratings,
--     pcs.games_played,
--     pcs.win_rate,
--     pcs.avg_points,
--     pcs.avg_rebounds, 
--     pcs.avg_assists,
--     pcs.efficiency_rating,
--     pas.overall_rating,
--     pas.scoring_rating,
--     pas.rebounding_rating,
--     pas.assist_rating
-- FROM sd_player p
-- LEFT JOIN sd_player_career_stats pcs ON p.id = pcs.player_id AND pcs.game_type = 0 AND pcs.deleted = 0
-- LEFT JOIN sd_player_career_ability_scores pas ON p.id = pas.player_id AND pas.game_type = 0 AND pas.deleted = 0
-- WHERE p.deleted = 0;

-- ================================
-- 表结构说明
-- ================================

/*
本SQL脚本包含了赛点篮球球员生涯模块的完整数据表结构，解决了以下问题：

1. **字段完整性**: 包含所有代码中需要的字段，特别是wins、losses等胜负相关字段
2. **数据类型统一**: 使用DECIMAL(5,2)存储百分比和评分，保证精度
3. **索引优化**: 针对查询热点添加了合适的索引
4. **约束完整**: 唯一键约束防止数据重复，外键约束保证数据完整性
5. **扩展性**: 预留了排名、评分等扩展字段
6. **注释完整**: 每个字段都有详细的中文注释

主要表说明：
- sd_season: 赛季管理，支持多赛季数据
- sd_player_career_stats: 生涯聚合统计，包含所有统计维度
- sd_player_season_stats: 赛季统计，支持按赛季分析
- sd_player_career_best_stats: 最佳记录，记录历史最佳表现
- sd_player_career_ability_scores: 7维度能力评分

执行顺序：
1. 先执行表创建语句
2. 根据需要启用外键约束
3. 插入初始化数据
4. 根据实际情况调整索引

注意事项：
- 所有表都包含deleted字段，支持逻辑删除
- 使用utf8mb4字符集，支持emoji等特殊字符
- 百分比字段使用DECIMAL存储，避免浮点数精度问题
- 预留了排名和评分字段，便于后续功能扩展

-- ================================
-- 架构问题分析与解决建议
-- ================================

/*
经过代码分析发现的问题：

1. **当前实现问题**：
   - 雷达图数据每次都从sd_player_statistics表实时计算
   - 联盟最佳数据同样每次重新聚合计算
   - 没有使用本SQL文件定义的聚合统计表

2. **性能影响**：
   - 每次请求都需要GROUP BY聚合大量原始数据
   - 查询响应时间长，特别是球员数据量大时
   - 数据库压力大

3. **建议修改方案**：
   a) 立即执行本SQL创建完整表结构
   b) 修改PlayerCareerStatsService使用sd_player_career_stats表
   c) 建立数据同步机制：比赛结束时更新聚合表
   d) 雷达图数据直接从聚合表读取，而不是实时计算

4. **代码修改要点**：
   - calculateCareerStats()方法应该查询sd_player_career_stats表
   - getLeagueBestStats()应该基于聚合表数据计算最佳值
   - 建立GameResultEventListener同步更新聚合表

这样可以解决"查球员生涯对比，怎么最后查询赛季去了"的架构混乱问题。
*/