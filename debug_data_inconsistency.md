# 球员数据不一致问题诊断和修复指南

## 问题描述
内管API (`http://localhost:48080/admin-api/operation/player-career/get?playerId=1`) 返回的场均得分是18.6，
而APP API (`http://localhost:48080/app-api/league/player/career/overview?playerId=1&gameType=0`) 返回的场均得分是25.6。

## 诊断步骤

### 1. 使用新增的调试接口

#### 详细验证球员数据
```bash
# 验证球员数据的详细信息，包括聚合表和原始数据的对比
GET http://localhost:48080/admin-api/operation/player-career/debug/validate-data?playerId=1&gameType=0
```

#### 比较两个API的数据
```bash
# 需要先登录获取token，然后调用
GET http://localhost:48080/admin-api/operation/player-career/debug/compare-api-data?playerId=1
```

#### 修复数据不一致问题
```bash
# 清除缓存并强制重新计算
POST http://localhost:48080/admin-api/operation/player-career/debug/fix-data-inconsistency?playerId=1
```

### 2. 检查日志输出

修改后的代码会在日志中输出详细信息：
- `🔧 ADMIN API获取球员 X 生涯数据` - 管理端API数据
- `🎯 APP API获取球员 X 生涯数据` - APP端API数据
- `✅ 从聚合表获取球员 X 生涯数据` - 从数据库获取的数据
- `✅ 球员 X 生涯统计计算完成` - 实时计算的数据

### 3. 可能的原因分析

1. **缓存不一致**: APP API使用了RadarChartCacheService缓存，可能存在过期数据
2. **数据库数据不同步**: 聚合表中的数据可能没有及时更新
3. **计算逻辑差异**: 虽然都调用同一个服务，但可能存在不同的数据处理路径

## 修复方案

### 方案1: 清除缓存并重新计算
```java
// 1. 清除所有缓存
radarChartCacheService.evictAllCache();

// 2. 强制重新计算生涯数据
playerCareerStatsService.forceRecalculateCareerStats(playerId);
```

### 方案2: 检查数据库一致性
```sql
-- 检查球员1的生涯统计数据
SELECT player_id, game_type, games_played, total_points, avg_points, 
       create_time, update_time
FROM sd_player_career_stats 
WHERE player_id = 1 AND game_type = 0;

-- 检查原始比赛数据
SELECT COUNT(*) as total_games, SUM(points) as total_points, 
       AVG(points) as avg_points
FROM sd_player_statistics 
WHERE player_id = 1 AND section = 0;
```

### 方案3: 数据重新初始化
如果数据严重不一致，可以使用生涯数据初始化功能：
```bash
POST http://localhost:48080/admin-api/operation/career/init/full-v2
```

## 验证修复结果

修复后，再次调用两个API验证数据是否一致：
1. 管理端API: `GET /admin-api/operation/player-career/get?playerId=1`
2. APP端API: `GET /app-api/league/player/career/overview?playerId=1&gameType=0`

两个API返回的场均得分应该相同。

## 预防措施

1. **统一数据源**: 确保所有API都使用相同的数据获取逻辑
2. **缓存同步**: 在数据更新时及时清除相关缓存
3. **数据校验**: 定期检查数据一致性
4. **监控告警**: 设置数据不一致的监控告警
